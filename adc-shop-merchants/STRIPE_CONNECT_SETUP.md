# Stripe Connect Integration Setup

This document provides a comprehensive guide for setting up Stripe Connect in the merchant platform to enable restaurants to accept credit card payments.

## Overview

The Stripe Connect integration allows restaurant merchants to:
- Create and manage Stripe Connect accounts
- Accept credit card payments from customers
- Handle payouts and transfers
- Manage account onboarding and verification
- Monitor payment status and capabilities

## Prerequisites

1. **Stripe Account**: You need a Stripe platform account with Connect enabled
2. **Environment Variables**: Configure the required Stripe API keys
3. **Webhook Endpoints**: Set up webhook endpoints for payment events

## Installation

The required packages are already installed:
- `@stripe/connect-js` - For Stripe Connect embedded components
- `@stripe/stripe-js` - For Stripe JavaScript SDK
- `stripe` - For server-side Stripe API calls

## Environment Configuration

Add the following environment variables to your `.env.local` file:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
```

### Getting Stripe API Keys

1. Log in to your [Stripe Dashboard](https://dashboard.stripe.com)
2. Navigate to **Developers** > **API keys**
3. Copy your **Publishable key** and **Secret key**
4. For production, use live keys (starting with `pk_live_` and `sk_live_`)

## File Structure

```
src/
├── components/stripe/
│   └── StripeConnectOnboarding.tsx    # Main Stripe Connect component
├── hooks/
│   └── useStripeConnect.ts            # Stripe Connect hook
├── app/api/stripe/
│   ├── connect/route.ts               # Connect account management
│   └── account_session/route.ts       # Account session creation
└── app/[locale]/app/restaurant/[slugShop]/[slugBranch]/settings/payments/
    └── page.tsx                       # Payment settings page
```

## Components

### StripeConnectOnboarding

The main component that handles Stripe Connect account creation, onboarding, and management.

**Props:**
- `onAccountConnected?: (accountId: string) => void` - Callback when account is connected
- `onAccountDisconnected?: () => void` - Callback when account is disconnected
- `initialAccountId?: string` - Existing account ID to load
- `shopEmail?: string` - Shop email for account creation

**Features:**
- Account creation with business email
- Real-time account status monitoring
- Embedded onboarding flow
- Account disconnection
- Capability status display

### useStripeConnect Hook

Custom hook for initializing Stripe Connect instance with proper configuration.

**Parameters:**
- `connectedAccountId: string | null` - The connected account ID

**Returns:**
- Stripe Connect instance for embedded components

## API Routes

### POST /api/stripe/connect

Creates a new Stripe Connect account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "country": "TH",
  "type": "express"
}
```

**Response:**
```json
{
  "account_id": "acct_**********",
  "email": "<EMAIL>",
  "country": "TH",
  "type": "express",
  "created": **********,
  "details_submitted": false,
  "charges_enabled": false,
  "payouts_enabled": false
}
```

### GET /api/stripe/connect

Retrieves connected account details.

**Query Parameters:**
- `account_id` - The Stripe account ID

### DELETE /api/stripe/connect

Deletes/deactivates a connected account.

**Request Body:**
```json
{
  "account_id": "acct_**********"
}
```

### POST /api/stripe/account_session

Creates an account session for embedded components.

**Request Body:**
```json
{
  "account": "acct_**********"
}
```

**Response:**
```json
{
  "client_secret": "acct_session_secret_123"
}
```

## Usage

### Basic Implementation

```tsx
import StripeConnectOnboarding from '@/components/stripe/StripeConnectOnboarding';

function PaymentSettings() {
  const handleAccountConnected = (accountId: string) => {
    console.log('Account connected:', accountId);
    // Update your database with the account ID
  };

  const handleAccountDisconnected = () => {
    console.log('Account disconnected');
    // Remove account ID from your database
  };

  return (
    <StripeConnectOnboarding
      onAccountConnected={handleAccountConnected}
      onAccountDisconnected={handleAccountDisconnected}
      shopEmail="<EMAIL>"
    />
  );
}
```

### Integration with Payment Settings

The component is already integrated into the payment settings page at:
`/app/restaurant/[slugShop]/[slugBranch]/settings/payments`

## Account States

### Account Status
- **inactive** - No account connected
- **pending** - Account created but onboarding incomplete
- **restricted** - Account has restrictions or missing information
- **active** - Account fully verified and operational

### Capabilities
- **charges_enabled** - Can accept payments
- **payouts_enabled** - Can receive payouts

## Testing

### Test Mode
- Use test API keys (starting with `sk_test_` and `pk_test_`)
- Test accounts are automatically created in test mode
- No real money is processed

### Test Cards
Use Stripe's test card numbers:
- `****************` - Visa (succeeds)
- `****************` - Visa (declined)
- `****************` - Visa (insufficient funds)

## Production Deployment

### Before Going Live

1. **Switch to Live Keys**: Replace test keys with live keys
2. **Webhook Configuration**: Set up production webhook endpoints
3. **Account Verification**: Ensure platform account is verified
4. **Compliance**: Review Stripe Connect compliance requirements

### Live Mode Considerations

- Real money will be processed
- Account verification requirements are stricter
- Payout schedules apply
- Transaction fees apply

## Troubleshooting

### Common Issues

1. **Invalid API Key**: Ensure environment variables are set correctly
2. **Account Creation Failed**: Check if email is already associated with another account
3. **Onboarding Issues**: Verify account session creation is working
4. **Capability Restrictions**: Review account requirements in Stripe Dashboard

### Error Handling

The implementation includes comprehensive error handling:
- API errors are caught and displayed to users
- Network errors are handled gracefully
- Loading states are shown during operations

## Security Considerations

1. **API Keys**: Never expose secret keys in client-side code
2. **Account Sessions**: Sessions are short-lived and secure
3. **Webhook Verification**: Verify webhook signatures in production
4. **Data Protection**: Handle sensitive payment data according to PCI compliance

## Support

For issues related to:
- **Stripe Connect**: Check [Stripe Connect Documentation](https://stripe.com/docs/connect)
- **API Integration**: Review [Stripe API Reference](https://stripe.com/docs/api)
- **Platform Setup**: Contact Stripe Support

## Next Steps

1. Set up webhook endpoints for payment events
2. Implement payment processing in the customer application
3. Add payout management features
4. Set up monitoring and analytics
5. Configure compliance and risk management
