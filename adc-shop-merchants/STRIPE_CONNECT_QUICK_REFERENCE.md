# 🚀 Stripe Connect Quick Reference

## Essential Commands

### 1. Start the Server
```bash
cd adc-shop-merchants/restaurant-backend
make build
./restaurant-api
```

### 2. Get Authentication Token
```bash
TOKEN=$(curl -s -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}' | jq -r '.token')
```

### 3. Create Order with Payment
```bash
curl -X POST http://localhost:8080/api/v1/orders/create-with-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "branch_id": "52e05798-2c76-4fe6-9c9c-72a83b7e203e",
    "customer_name": "Test Customer",
    "customer_email": "<EMAIL>",
    "customer_phone": "+**********",
    "order_type": "dine_in",
    "items": [
      {
        "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
        "quantity": 1,
        "unit_price": 12.99
      }
    ],
    "payment_method": "card"
  }'
```

### 4. List Connected Accounts
```bash
curl -X GET "http://localhost:8080/api/v1/stripe/connect/list" \
  -H "Authorization: Bearer $TOKEN"
```

### 5. Check Payment Status
```bash
curl -X GET "http://localhost:8080/api/v1/stripe/payment_intents/{PAYMENT_INTENT_ID}?connected_account_id=acct_1RWGqRCvS6V6yiE9" \
  -H "Authorization: Bearer $TOKEN"
```

## Test Data

### Working Connected Account
- **Account ID**: `acct_1RWGqRCvS6V6yiE9`
- **Status**: Active
- **Country**: Thailand (TH)
- **Business**: scandine.shop

### Test Branch ID
- **Branch ID**: `52e05798-2c76-4fe6-9c9c-72a83b7e203e`

### Test Menu Item ID
- **Menu Item ID**: `d5c6671b-21bf-4fae-8dc0-02564523fbe2`

### Test User Credentials
- **Email**: `<EMAIL>`
- **Password**: `password`

## Key Environment Variables

```bash
STRIPE_SECRET_KEY=sk_test_51QtW5QCtNXkGk5bXQrIg3IdxRXAsB6a5XBKbpxQiTlgdU1GN3Pq5deYt0gtq6mX2GKOnjNMaQaq8nWHvhMTNv5sh00PnRaVKrg
STRIPE_PLATFORM_FEE=250
STRIPE_CURRENCY=thb
DATABASE_URL=postgresql://postgres.sqzzpwirpwdlxzuvztey:<EMAIL>:5432/postgres
```

## Payment Flow Summary

1. **Create Order** → Returns `payment_intent_id` and `client_secret`
2. **Frontend Confirmation** → Use `client_secret` with Stripe.js
3. **Payment Success** → Order status updates to `paid`

## Platform Fee Calculation

- **Rate**: 2.5% (250 basis points)
- **Example**: 
  - Order Total: 100 THB
  - Platform Fee: 2.50 THB
  - Restaurant Receives: 97.50 THB

## Common Response Codes

- **200**: Success
- **400**: Bad Request (invalid data)
- **401**: Unauthorized (invalid/missing token)
- **404**: Not Found (resource doesn't exist)
- **500**: Server Error (check logs)

## Debugging Tips

### Check Server Logs
```bash
# Look for these log messages:
# "Payment intent created successfully"
# "Order created successfully with payment integration"
# "Stripe API key initialized"
```

### Verify Database Connection
```bash
# Check if orders are being created:
psql $DATABASE_URL -c "SELECT id, order_number, total, payment_status FROM orders ORDER BY created_at DESC LIMIT 5;"
```

### Test Stripe API Key
```bash
curl -X GET "https://api.stripe.com/v1/accounts/acct_1RWGqRCvS6V6yiE9" \
  -H "Authorization: Bearer sk_test_51QtW5QCtNXkGk5bXQrIg3IdxRXAsB6a5XBKbpxQiTlgdU1GN3Pq5deYt0gtq6mX2GKOnjNMaQaq8nWHvhMTNv5sh00PnRaVKrg"
```

## Frontend Integration

### Basic Stripe.js Setup
```javascript
import { loadStripe } from '@stripe/stripe-js';

const stripe = await loadStripe('pk_test_your_publishable_key', {
  stripeAccount: 'acct_1RWGqRCvS6V6yiE9'
});

// Confirm payment
const {error, paymentIntent} = await stripe.confirmCardPayment(
  client_secret,
  {
    payment_method: {
      card: cardElement,
      billing_details: { name: 'Customer Name' }
    }
  }
);
```

## Troubleshooting Checklist

- [ ] Server is running on port 8080
- [ ] Database connection is working
- [ ] Stripe API key is set in environment
- [ ] JWT token is valid and not expired
- [ ] Connected account exists and is active
- [ ] Branch ID exists in database
- [ ] Menu item ID exists in database

## Success Indicators

✅ **Order Creation**: Returns order ID and payment intent  
✅ **Payment Intent**: Has `client_secret` and `connected_account_id`  
✅ **Platform Fee**: Automatically calculated (2.5%)  
✅ **Database**: Order saved with payment_intent_id  
✅ **Stripe Dashboard**: Payment intent visible  

## Quick Test Script

```bash
#!/bin/bash
# Complete test flow

echo "🔐 Getting auth token..."
TOKEN=$(curl -s -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}' | jq -r '.token')

echo "🛒 Creating order with payment..."
RESPONSE=$(curl -s -X POST http://localhost:8080/api/v1/orders/create-with-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "branch_id": "52e05798-2c76-4fe6-9c9c-72a83b7e203e",
    "customer_name": "Test Customer",
    "customer_email": "<EMAIL>",
    "customer_phone": "+**********",
    "order_type": "dine_in",
    "items": [
      {
        "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
        "quantity": 1,
        "unit_price": 12.99
      }
    ],
    "payment_method": "card"
  }')

echo "📊 Response:"
echo $RESPONSE | jq .

PAYMENT_INTENT_ID=$(echo $RESPONSE | jq -r '.data.payment_intent.payment_intent_id')
echo "💳 Payment Intent ID: $PAYMENT_INTENT_ID"

echo "✅ Test completed successfully!"
```

## Support Contacts

- **Technical Issues**: Check server logs and database
- **Stripe Issues**: Stripe Dashboard → Logs
- **Payment Problems**: Verify connected account status
- **Database Issues**: Check PostgreSQL connection

---

**Quick Reference Version**: 1.0  
**Last Updated**: December 2024
