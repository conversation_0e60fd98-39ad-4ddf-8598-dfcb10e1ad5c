#!/bin/bash

# Test Script for Incomplete Order Scenarios
# This script tests various failure scenarios in the order processing system

set -e

# Configuration
BASE_URL="http://localhost:8080"
API_BASE="$BASE_URL/api/v1"
BRANCH_ID="52e05798-2c76-4fe6-9c9c-72a83b7e203e"
MENU_ITEM_ID="d5c6671b-21bf-4fae-8dc0-02564523fbe2"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to get JWT token
get_jwt_token() {
    log_info "Getting JWT token..."
    local response=$(curl -s -X POST "$API_BASE/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"email": "<EMAIL>", "password": "password"}')

    local token=$(echo "$response" | jq -r '.token // empty')

    if [ -z "$token" ] || [ "$token" = "null" ]; then
        log_error "Failed to get JWT token"
        echo "Response: $response"
        exit 1
    fi

    echo "$token"
}

# Function to create test order
create_test_order() {
    local customer_name="$1"
    local customer_email="$2"
    local unit_price="${3:-12.99}"
    local branch_id="${4:-$BRANCH_ID}"

    curl -s -X POST "$API_BASE/orders/create-with-payment" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $TOKEN" \
        -d "{
            \"branch_id\": \"$branch_id\",
            \"customer_name\": \"$customer_name\",
            \"customer_email\": \"$customer_email\",
            \"customer_phone\": \"+1234567890\",
            \"order_type\": \"dine_in\",
            \"items\": [
                {
                    \"menu_item_id\": \"$MENU_ITEM_ID\",
                    \"quantity\": 1,
                    \"unit_price\": $unit_price
                }
            ],
            \"payment_method\": \"card\"
        }"
}

# Function to check order status
check_order_status() {
    local order_id="$1"
    curl -s -X GET "$API_BASE/orders/$order_id" \
        -H "Authorization: Bearer $TOKEN"
}

# Function to check payment intent status
check_payment_intent_status() {
    local payment_intent_id="$1"
    curl -s -X GET "$API_BASE/payments/status/$payment_intent_id" \
        -H "Authorization: Bearer $TOKEN"
}

# Test 1: Connected Account Not Found
test_no_connected_account() {
    log_info "Test 1: Testing with invalid branch ID (no connected account)"

    local response=$(create_test_order "No Account Test" "<EMAIL>" "12.99" "********-0000-0000-0000-********0000")
    local success=$(echo "$response" | jq -r '.success // false')

    if [ "$success" = "false" ]; then
        log_success "✓ Correctly failed with no connected account"
        echo "Error: $(echo "$response" | jq -r '.error // .message // "Unknown error"')"
    else
        log_error "✗ Should have failed but didn't"
        echo "Response: $(echo "$response" | jq -r '.message // "No message"')"
    fi
    echo
}

# Test 2: Invalid Payment Amount
test_invalid_amount() {
    log_info "Test 2: Testing with zero payment amount"

    local response=$(create_test_order "Zero Amount Test" "<EMAIL>" "0")
    local success=$(echo "$response" | jq -r '.success // false')

    if [ "$success" = "false" ]; then
        log_success "✓ Correctly handled zero amount"
    else
        log_warning "⚠ Zero amount was accepted - check if this is intended"
    fi

    echo "Response: $(echo "$response" | jq -r '.message // .error')"
    echo
}

# Test 3: Valid Order Creation
test_valid_order_creation() {
    log_info "Test 3: Creating valid order for payment testing"

    local response=$(create_test_order "Valid Test Customer" "<EMAIL>" "25.99")
    local success=$(echo "$response" | jq -r '.success // false')

    if [ "$success" = "true" ]; then
        local order_id=$(echo "$response" | jq -r '.data.order.id // empty')
        local payment_intent_id=$(echo "$response" | jq -r '.data.payment_intent.payment_intent_id // empty')
        local client_secret=$(echo "$response" | jq -r '.data.payment_intent.client_secret // empty')
        local payment_status=$(echo "$response" | jq -r '.data.payment_intent.status // empty')
        local requires_payment=$(echo "$response" | jq -r '.data.requires_payment // false')

        log_success "✓ Order created successfully"
        echo "Order ID: $order_id"
        echo "Payment Intent ID: $payment_intent_id"
        echo "Payment Status: $payment_status"
        echo "Requires Payment: $requires_payment"
        echo "Client Secret: ${client_secret:0:30}..."

        # Store for later tests
        echo "$order_id" > /tmp/test_order_id
        echo "$payment_intent_id" > /tmp/test_payment_intent_id

        # This demonstrates an incomplete order - payment intent created but not paid
        if [ "$payment_status" = "requires_payment_method" ]; then
            log_warning "⚠ Order is incomplete - payment method required"
        fi

    else
        log_error "✗ Failed to create valid order"
        echo "Response: $(echo "$response" | jq -r '.message // "No message"')"
    fi
    echo
}

# Test 4: Payment Intent Status Check
test_payment_intent_status() {
    log_info "Test 4: Checking payment intent status"

    if [ -f /tmp/test_payment_intent_id ]; then
        local payment_intent_id=$(cat /tmp/test_payment_intent_id)
        local response=$(check_payment_intent_status "$payment_intent_id")
        local status=$(echo "$response" | jq -r '.data.status // "unknown"')

        log_info "Payment Intent Status: $status"
        echo "Full response: $(echo "$response" | jq '.')"
    else
        log_warning "No payment intent ID available from previous test"
    fi
    echo
}

# Test 5: Multiple Concurrent Orders
test_concurrent_orders() {
    log_info "Test 5: Creating multiple concurrent orders"

    local pids=()
    local temp_files=()

    for i in {1..3}; do
        local temp_file="/tmp/concurrent_order_$i"
        temp_files+=("$temp_file")

        (create_test_order "Concurrent Test $i" "concurrent$<EMAIL>" "15.99" > "$temp_file") &
        pids+=($!)
    done

    # Wait for all background processes
    for pid in "${pids[@]}"; do
        wait "$pid"
    done

    # Check results
    local success_count=0
    for temp_file in "${temp_files[@]}"; do
        if [ -f "$temp_file" ]; then
            local success=$(cat "$temp_file" | jq -r '.success // false')
            if [ "$success" = "true" ]; then
                ((success_count++))
            fi
        fi
    done

    log_info "Concurrent orders completed: $success_count/3 successful"

    # Cleanup temp files
    for temp_file in "${temp_files[@]}"; do
        rm -f "$temp_file"
    done
    echo
}

# Test 6: Database Consistency Check
test_database_consistency() {
    log_info "Test 6: Checking for database consistency issues"

    # This would require database access - placeholder for now
    log_warning "Database consistency check requires direct database access"
    log_info "Manual check recommended:"
    echo "  - Check for orders with payment_method='card' but no payment_intent_id"
    echo "  - Check for orders with payment_status='paid' but status='pending'"
    echo "  - Check for orphaned payment intents"
    echo
}

# Test 7: Webhook Endpoint Test
test_webhook_endpoint() {
    log_info "Test 7: Testing webhook endpoint accessibility"

    local webhook_url="$BASE_URL/api/v1/payments/webhook"
    local response=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$webhook_url" \
        -H "Content-Type: application/json" \
        -d '{"test": "data"}')

    if [ "$response" = "400" ]; then
        log_success "✓ Webhook endpoint is accessible (returned 400 as expected for invalid payload)"
    else
        log_warning "⚠ Webhook endpoint returned status: $response"
    fi
    echo
}

# Main execution
main() {
    log_info "Starting Incomplete Order Tests"
    log_info "================================"

    # Get authentication token
    TOKEN=$(get_jwt_token)
    log_success "JWT token obtained"
    echo

    # Run tests
    test_no_connected_account
    test_invalid_amount
    test_valid_order_creation
    test_payment_intent_status
    test_concurrent_orders
    test_database_consistency
    test_webhook_endpoint

    # Cleanup
    rm -f /tmp/test_order_id /tmp/test_payment_intent_id

    log_info "================================"
    log_success "All tests completed!"

    echo
    log_info "Next Steps:"
    echo "1. Test payment failures using Stripe test cards in frontend"
    echo "2. Use 'stripe trigger' commands to test webhook scenarios"
    echo "3. Monitor logs during payment processing"
    echo "4. Check database for any inconsistent states"
}

# Check if required tools are available
check_dependencies() {
    local missing_deps=()

    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi

    if ! command -v jq &> /dev/null; then
        missing_deps+=("jq")
    fi

    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        log_info "Please install missing dependencies and try again"
        exit 1
    fi
}

# Run dependency check and main function
check_dependencies
main "$@"
