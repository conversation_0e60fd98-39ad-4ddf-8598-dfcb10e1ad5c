# 🎯 Stripe Connect Integration Documentation

## Overview

This document provides comprehensive documentation for the Stripe Connect marketplace payment system implemented for the restaurant platform. The system enables automatic payment splitting between the platform and individual restaurants.

## 🏗️ Architecture

```
Customer Order → Go Backend → Stripe Connect → Payment Split
                     ↓
              Database Storage (Orders + Payment Data)
                     ↓
              Platform Fee (2.5%) → Your Account
              Remaining Amount → Restaurant Account
```

## 📋 Table of Contents

1. [Setup & Configuration](#setup--configuration)
2. [Database Models](#database-models)
3. [API Endpoints](#api-endpoints)
4. [Payment Flow](#payment-flow)
5. [Testing](#testing)
6. [Production Deployment](#production-deployment)
7. [Troubleshooting](#troubleshooting)

## 🔧 Setup & Configuration

### Environment Variables

Add the following to your `.env` file:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51QtW5QCtNXkGk5bXQrIg3IdxRXAsB6a5XBKbpxQiTlgdU1GN3Pq5deYt0gtq6mX2GKOnjNMaQaq8nWHvhMTNv5sh00PnRaVKrg
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_PLATFORM_FEE=250
STRIPE_CURRENCY=thb
```

### Database Setup

The system uses PostgreSQL with the following key tables:
- `stripe_connect_accounts` - Connected account information
- `orders` - Order data with payment integration
- `order_items` - Individual order items

## 🗄️ Database Models

### StripeConnectAccount Model

```go
type StripeConnectAccount struct {
    ID              uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
    ShopID          uuid.UUID  `json:"shop_id" gorm:"type:uuid;not null"`
    BranchID        *uuid.UUID `json:"branch_id,omitempty" gorm:"type:uuid"`
    StripeAccountID string     `json:"stripe_account_id" gorm:"type:varchar(255);uniqueIndex;not null"`
    AccountType     string     `json:"account_type" gorm:"type:varchar(50);not null"`
    Country         string     `json:"country" gorm:"type:varchar(2);not null"`
    Email           string     `json:"email" gorm:"type:varchar(255)"`
    // ... additional fields
}
```

### Order Model Enhancement

```go
type Order struct {
    // ... existing fields
    PaymentIntentID  *string `json:"payment_intent_id,omitempty" gorm:"type:varchar(255)"`
    PaymentStatus    string  `json:"payment_status" gorm:"type:varchar(50);default:'pending'"`
    PaymentMethod    string  `json:"payment_method" gorm:"type:varchar(50)"`
    // ... additional fields
}
```

## 🔌 API Endpoints

### Order Creation with Payment

**POST** `/api/v1/orders/create-with-payment`

Creates an order and associated payment intent.

**Request Body:**
```json
{
    "branch_id": "52e05798-2c76-4fe6-9c9c-72a83b7e203e",
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "+**********",
    "order_type": "dine_in",
    "items": [
        {
            "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
            "quantity": 2,
            "unit_price": 12.99
        }
    ],
    "payment_method": "card"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Order created successfully",
    "data": {
        "order": {
            "id": "9ddce980-c226-4b4b-970c-6779a8ba6218",
            "order_number": "ORD-********-213321-0703",
            "total": 30.3966,
            "payment_status": "pending"
        },
        "payment_intent": {
            "payment_intent_id": "pi_3RWINaCvS6V6yiE911WOL3et",
            "client_secret": "pi_3RWINaCvS6V6yiE911WOL3et_secret_...",
            "amount": 3039,
            "currency": "thb",
            "connected_account_id": "acct_1RWGqRCvS6V6yiE9"
        }
    }
}
```

### Stripe Connect Account Management

**GET** `/api/v1/stripe/connect/list`
- Lists all connected accounts for the authenticated shop

**POST** `/api/v1/stripe/connect`
- Creates a new Stripe Connect account

**GET** `/api/v1/stripe/connect?account_id={account_id}`
- Retrieves specific account details

### Payment Intent Management

**GET** `/api/v1/stripe/payment_intents/{payment_intent_id}`
- Retrieves payment intent status

**POST** `/api/v1/stripe/payment_intents/{payment_intent_id}/confirm`
- Confirms a payment intent

## 💳 Payment Flow

### 1. Order Creation
```bash
curl -X POST http://localhost:8080/api/v1/orders/create-with-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {jwt_token}" \
  -d '{order_data}'
```

### 2. Frontend Payment Confirmation
```javascript
// Use the client_secret from order creation response
const {error, paymentIntent} = await stripe.confirmCardPayment(
    client_secret,
    {
        payment_method: {
            card: cardElement,
            billing_details: {
                name: 'Customer Name'
            }
        }
    }
);
```

### 3. Payment Status Verification
```bash
curl -X GET "http://localhost:8080/api/v1/orders/{order_id}/payment-status" \
  -H "Authorization: Bearer {jwt_token}"
```

## 🧪 Testing

### Test Connected Account
- **Account ID**: `acct_1RWGqRCvS6V6yiE9`
- **Status**: Active
- **Country**: Thailand (TH)
- **Type**: `none` (Thailand marketplace restrictions)

### Test Payment Methods
Use Stripe's test payment methods:
- **Visa**: `****************`
- **Visa (debit)**: `****************`
- **Mastercard**: `****************`

### Sample Test Commands

**1. Login and get JWT token:**
```bash
TOKEN=$(curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}' | jq -r '.token')
```

**2. Create order with payment:**
```bash
curl -X POST http://localhost:8080/api/v1/orders/create-with-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "branch_id": "52e05798-2c76-4fe6-9c9c-72a83b7e203e",
    "customer_name": "Test Customer",
    "customer_email": "<EMAIL>",
    "customer_phone": "+**********",
    "order_type": "dine_in",
    "items": [
      {
        "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
        "quantity": 1,
        "unit_price": 12.99
      }
    ],
    "payment_method": "card"
  }'
```

## 🚀 Production Deployment

### 1. Environment Setup
- Replace test keys with live Stripe keys
- Update webhook endpoints
- Configure proper SSL certificates

### 2. Database Migration
Ensure all database migrations are applied:
```bash
go run cmd/migrate/main.go
```

### 3. Monitoring
- Set up logging for payment events
- Monitor Stripe dashboard for transactions
- Implement webhook handlers for payment status updates

## 🔧 Troubleshooting

### Common Issues

**1. "Connected account not found"**
- Verify the shop has a registered Stripe Connect account
- Check account status in database

**2. "Payment intent creation failed"**
- Verify Stripe API keys are correct
- Check connected account is active and charges_enabled=true

**3. "Database scanning errors"**
- Ensure StringSlice custom type is properly implemented
- Check JSONB field compatibility

### Debug Commands

**Check connected accounts:**
```bash
curl -X GET "http://localhost:8080/api/v1/stripe/connect/list" \
  -H "Authorization: Bearer $TOKEN"
```

**Verify payment intent:**
```bash
curl -X GET "http://localhost:8080/api/v1/stripe/payment_intents/{payment_intent_id}?connected_account_id={account_id}" \
  -H "Authorization: Bearer $TOKEN"
```

## 📊 Key Metrics

### Platform Fees
- **Rate**: 2.5% (250 basis points)
- **Calculation**: Automatic on all successful payments
- **Currency**: Thai Baht (THB)

### Supported Payment Methods
- Credit/Debit Cards (Visa, Mastercard)
- PromptPay (Thailand local payment method)

## 🔐 Security Considerations

1. **API Keys**: Store securely in environment variables
2. **Webhooks**: Verify webhook signatures
3. **Authentication**: All endpoints require valid JWT tokens
4. **Data Encryption**: Sensitive payment data is handled by Stripe
5. **PCI Compliance**: Achieved through Stripe's infrastructure

## 📞 Support

For technical issues:
1. Check server logs for detailed error messages
2. Verify Stripe dashboard for payment status
3. Review database records for data consistency
4. Contact Stripe support for payment-specific issues

## 🎯 Implementation Details

### Service Layer Architecture

**StripeConnectService** (`internal/services/stripe_connect_service.go`)
- Manages connected account lifecycle
- Handles account creation and registration
- Syncs account data with Stripe

**StripePaymentService** (`internal/services/stripe_payment_service.go`)
- Creates and manages payment intents
- Handles payment confirmation and cancellation
- Calculates platform fees automatically

**IntegratedOrderService** (`internal/services/integrated_order_service.go`)
- Combines order creation with payment processing
- Links orders to payment intents
- Manages order status updates

### Custom Types

**StringSlice** (`internal/models/custom_types.go`)
```go
type StringSlice []string

func (s *StringSlice) Scan(value interface{}) error {
    if value == nil {
        *s = StringSlice{}
        return nil
    }

    switch v := value.(type) {
    case []byte:
        return json.Unmarshal(v, s)
    case string:
        return json.Unmarshal([]byte(v), s)
    default:
        return fmt.Errorf("cannot scan %T into StringSlice", value)
    }
}

func (s StringSlice) Value() (driver.Value, error) {
    if len(s) == 0 {
        return "[]", nil
    }
    return json.Marshal(s)
}
```

## 🔄 Webhook Integration

### Webhook Endpoints
Set up webhooks in your Stripe dashboard to handle:
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `account.updated`

### Sample Webhook Handler
```go
func (h *WebhookHandler) HandleStripeWebhook(c *gin.Context) {
    payload, err := ioutil.ReadAll(c.Request.Body)
    if err != nil {
        c.JSON(400, gin.H{"error": "Invalid payload"})
        return
    }

    event, err := webhook.ConstructEvent(payload,
        c.GetHeader("Stripe-Signature"),
        h.webhookSecret)
    if err != nil {
        c.JSON(400, gin.H{"error": "Invalid signature"})
        return
    }

    switch event.Type {
    case "payment_intent.succeeded":
        // Update order status to paid
    case "payment_intent.payment_failed":
        // Update order status to failed
    }

    c.JSON(200, gin.H{"received": true})
}
```

## 📈 Analytics & Reporting

### Key Metrics to Track
1. **Total Transaction Volume**
2. **Platform Fee Revenue**
3. **Payment Success Rate**
4. **Average Order Value**
5. **Connected Account Performance**

### Database Queries for Analytics

**Daily Revenue:**
```sql
SELECT
    DATE(created_at) as date,
    COUNT(*) as order_count,
    SUM(total) as total_revenue,
    SUM(total * 0.025) as platform_fees
FROM orders
WHERE payment_status = 'paid'
    AND created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

**Top Performing Restaurants:**
```sql
SELECT
    s.name as shop_name,
    COUNT(o.id) as order_count,
    SUM(o.total) as total_revenue
FROM orders o
JOIN shop_branches sb ON o.branch_id = sb.id
JOIN shops s ON sb.shop_id = s.id
WHERE o.payment_status = 'paid'
    AND o.created_at >= NOW() - INTERVAL '30 days'
GROUP BY s.id, s.name
ORDER BY total_revenue DESC
LIMIT 10;
```

## 🌍 Thailand-Specific Considerations

### Stripe Thailand Limitations
- **Account Type**: Connected accounts have `type: "none"`
- **Dashboard Access**: Limited dashboard visibility for connected accounts
- **Loss Liability**: Platform is loss-liable for disputes
- **Supported Payment Methods**: Cards and PromptPay

### Compliance Requirements
1. **Business Registration**: Ensure all restaurants have valid Thai business registration
2. **Tax Compliance**: Handle VAT calculations (7% in Thailand)
3. **Currency**: All transactions in Thai Baht (THB)
4. **Local Banking**: Connected accounts need Thai bank accounts

## 🔧 Advanced Configuration

### Custom Fee Structures
```go
// Different fee rates per restaurant category
func (s *StripePaymentService) calculatePlatformFee(amount int64, shopType string) *int64 {
    var feeRate float64
    switch shopType {
    case "premium":
        feeRate = 0.02 // 2%
    case "standard":
        feeRate = 0.025 // 2.5%
    case "basic":
        feeRate = 0.03 // 3%
    default:
        feeRate = 0.025
    }

    fee := int64(float64(amount) * feeRate)
    return &fee
}
```

### Multi-Currency Support
```go
// Support for different currencies
func (s *StripePaymentService) getCurrencyForCountry(country string) string {
    currencyMap := map[string]string{
        "TH": "thb",
        "SG": "sgd",
        "MY": "myr",
        "ID": "idr",
    }

    if currency, exists := currencyMap[country]; exists {
        return currency
    }
    return "thb" // default
}
```

---

**Last Updated**: December 2024
**Version**: 1.0
**Status**: Production Ready ✅

## 📚 Additional Resources

- [Stripe Connect Documentation](https://stripe.com/docs/connect)
- [Stripe Thailand Guide](https://support.stripe.com/questions/stripe-thailand-support-for-marketplaces)
- [Go Stripe SDK](https://github.com/stripe/stripe-go)
- [Webhook Testing with Stripe CLI](https://stripe.com/docs/stripe-cli)
