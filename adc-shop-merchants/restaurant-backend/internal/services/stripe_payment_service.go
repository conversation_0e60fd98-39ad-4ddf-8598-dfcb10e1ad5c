package services

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stripe/stripe-go/v78"
	"github.com/stripe/stripe-go/v78/paymentintent"
	"gorm.io/gorm"
)

// StripePaymentService handles Stripe payment processing
type StripePaymentService struct {
	db                   *gorm.DB
	logger               *logrus.Logger
	stripeConnectService *StripeConnectService
	platformFee          int64  // Platform fee in basis points (e.g., 250 = 2.5%)
	currency             string // Default currency
}

// NewStripePaymentService creates a new Stripe payment service
func NewStripePaymentService(db *gorm.DB, logger *logrus.Logger, stripeConnectService *StripeConnectService, platformFee int64, currency string) *StripePaymentService {
	return &StripePaymentService{
		db:                   db,
		logger:               logger,
		stripeConnectService: stripeConnectService,
		platformFee:          platformFee,
		currency:             currency,
	}
}

// CreatePaymentIntentRequest represents the request to create a payment intent
type CreatePaymentIntentRequest struct {
	Amount               int64             `json:"amount"`                           // Amount in smallest currency unit (e.g., satang for THB)
	Currency             string            `json:"currency,omitempty"`               // Currency code (defaults to service currency)
	ShopID               uuid.UUID         `json:"shop_id"`                          // Shop ID
	BranchID             *uuid.UUID        `json:"branch_id,omitempty"`              // Optional branch ID
	OrderID              *uuid.UUID        `json:"order_id,omitempty"`               // Optional order ID
	CustomerID           *string           `json:"customer_id,omitempty"`            // Optional customer ID
	Description          string            `json:"description,omitempty"`            // Payment description
	Metadata             map[string]string `json:"metadata,omitempty"`               // Additional metadata
	ApplicationFeeAmount *int64            `json:"application_fee_amount,omitempty"` // Custom platform fee
}

// PaymentIntentResponse represents the response for payment intent creation
type PaymentIntentResponse struct {
	PaymentIntentID      string            `json:"payment_intent_id"`
	ClientSecret         string            `json:"client_secret"`
	Amount               int64             `json:"amount"`
	Currency             string            `json:"currency"`
	Status               string            `json:"status"`
	ConnectedAccountID   string            `json:"connected_account_id"`
	ApplicationFeeAmount *int64            `json:"application_fee_amount,omitempty"`
	Metadata             map[string]string `json:"metadata,omitempty"`
}

// CreatePaymentIntent creates a payment intent for a connected account
func (s *StripePaymentService) CreatePaymentIntent(ctx context.Context, req CreatePaymentIntentRequest) (*PaymentIntentResponse, error) {
	// Get connected account for the shop/branch
	connectAccount, err := s.stripeConnectService.GetConnectAccount(ctx, req.ShopID, req.BranchID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get connected account")
		return nil, fmt.Errorf("no connected account found for shop: %w", err)
	}

	// Check if account is active
	if !connectAccount.ChargesEnabled {
		return nil, fmt.Errorf("connected account is not enabled for charges")
	}

	// Set default currency if not provided
	currency := req.Currency
	if currency == "" {
		currency = s.currency
	}

	// Calculate application fee
	applicationFeeAmount := req.ApplicationFeeAmount
	if applicationFeeAmount == nil && s.platformFee > 0 {
		fee := (req.Amount * s.platformFee) / 10000 // Convert basis points to amount
		applicationFeeAmount = &fee
	}

	// Prepare metadata
	metadata := make(map[string]string)
	if req.Metadata != nil {
		for k, v := range req.Metadata {
			metadata[k] = v
		}
	}
	metadata["shop_id"] = req.ShopID.String()
	if req.BranchID != nil {
		metadata["branch_id"] = req.BranchID.String()
	}
	if req.OrderID != nil {
		metadata["order_id"] = req.OrderID.String()
	}

	// Create payment intent parameters
	params := &stripe.PaymentIntentParams{
		Amount:   stripe.Int64(req.Amount),
		Currency: stripe.String(currency),
		Params: stripe.Params{
			StripeAccount: stripe.String(connectAccount.StripeAccountID),
		},
		Metadata: metadata,
	}

	// Add application fee if specified
	if applicationFeeAmount != nil && *applicationFeeAmount > 0 {
		params.ApplicationFeeAmount = stripe.Int64(*applicationFeeAmount)
	}

	// Add description if provided
	if req.Description != "" {
		params.Description = stripe.String(req.Description)
	}

	// Add customer if provided
	if req.CustomerID != nil {
		params.Customer = stripe.String(*req.CustomerID)
	}

	// Create payment intent
	pi, err := paymentintent.New(params)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create payment intent")
		return nil, fmt.Errorf("failed to create payment intent: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"payment_intent_id":    pi.ID,
		"amount":               req.Amount,
		"currency":             currency,
		"connected_account_id": connectAccount.StripeAccountID,
		"shop_id":              req.ShopID,
		"branch_id":            req.BranchID,
	}).Info("Payment intent created successfully")

	// Return response
	response := &PaymentIntentResponse{
		PaymentIntentID:      pi.ID,
		ClientSecret:         pi.ClientSecret,
		Amount:               pi.Amount,
		Currency:             string(pi.Currency),
		Status:               string(pi.Status),
		ConnectedAccountID:   connectAccount.StripeAccountID,
		ApplicationFeeAmount: applicationFeeAmount,
		Metadata:             metadata,
	}

	return response, nil
}

// GetPaymentIntent retrieves a payment intent
func (s *StripePaymentService) GetPaymentIntent(ctx context.Context, paymentIntentID string, connectedAccountID string) (*PaymentIntentResponse, error) {
	params := &stripe.PaymentIntentParams{
		Params: stripe.Params{
			StripeAccount: stripe.String(connectedAccountID),
		},
	}

	pi, err := paymentintent.Get(paymentIntentID, params)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get payment intent")
		return nil, fmt.Errorf("failed to get payment intent: %w", err)
	}

	var applicationFeeAmount *int64
	if pi.ApplicationFeeAmount > 0 {
		applicationFeeAmount = &pi.ApplicationFeeAmount
	}

	response := &PaymentIntentResponse{
		PaymentIntentID:      pi.ID,
		ClientSecret:         pi.ClientSecret,
		Amount:               pi.Amount,
		Currency:             string(pi.Currency),
		Status:               string(pi.Status),
		ConnectedAccountID:   connectedAccountID,
		ApplicationFeeAmount: applicationFeeAmount,
		Metadata:             pi.Metadata,
	}

	return response, nil
}

// ConfirmPaymentIntent confirms a payment intent
func (s *StripePaymentService) ConfirmPaymentIntent(ctx context.Context, paymentIntentID string, connectedAccountID string, paymentMethodID *string) (*PaymentIntentResponse, error) {
	params := &stripe.PaymentIntentConfirmParams{
		Params: stripe.Params{
			StripeAccount: stripe.String(connectedAccountID),
		},
	}

	if paymentMethodID != nil {
		params.PaymentMethod = stripe.String(*paymentMethodID)
	}

	pi, err := paymentintent.Confirm(paymentIntentID, params)
	if err != nil {
		s.logger.WithError(err).Error("Failed to confirm payment intent")
		return nil, fmt.Errorf("failed to confirm payment intent: %w", err)
	}

	var applicationFeeAmount *int64
	if pi.ApplicationFeeAmount > 0 {
		applicationFeeAmount = &pi.ApplicationFeeAmount
	}

	response := &PaymentIntentResponse{
		PaymentIntentID:      pi.ID,
		ClientSecret:         pi.ClientSecret,
		Amount:               pi.Amount,
		Currency:             string(pi.Currency),
		Status:               string(pi.Status),
		ConnectedAccountID:   connectedAccountID,
		ApplicationFeeAmount: applicationFeeAmount,
		Metadata:             pi.Metadata,
	}

	s.logger.WithFields(map[string]interface{}{
		"payment_intent_id":    pi.ID,
		"status":               string(pi.Status),
		"connected_account_id": connectedAccountID,
	}).Info("Payment intent confirmed")

	return response, nil
}

// CancelPaymentIntent cancels a payment intent
func (s *StripePaymentService) CancelPaymentIntent(ctx context.Context, paymentIntentID string, connectedAccountID string, reason *string) (*PaymentIntentResponse, error) {
	params := &stripe.PaymentIntentCancelParams{
		Params: stripe.Params{
			StripeAccount: stripe.String(connectedAccountID),
		},
	}

	if reason != nil {
		params.CancellationReason = stripe.String(*reason)
	}

	pi, err := paymentintent.Cancel(paymentIntentID, params)
	if err != nil {
		s.logger.WithError(err).Error("Failed to cancel payment intent")
		return nil, fmt.Errorf("failed to cancel payment intent: %w", err)
	}

	var applicationFeeAmount *int64
	if pi.ApplicationFeeAmount > 0 {
		applicationFeeAmount = &pi.ApplicationFeeAmount
	}

	response := &PaymentIntentResponse{
		PaymentIntentID:      pi.ID,
		ClientSecret:         pi.ClientSecret,
		Amount:               pi.Amount,
		Currency:             string(pi.Currency),
		Status:               string(pi.Status),
		ConnectedAccountID:   connectedAccountID,
		ApplicationFeeAmount: applicationFeeAmount,
		Metadata:             pi.Metadata,
	}

	s.logger.WithFields(map[string]interface{}{
		"payment_intent_id":    pi.ID,
		"status":               string(pi.Status),
		"connected_account_id": connectedAccountID,
		"reason":               reason,
	}).Info("Payment intent cancelled")

	return response, nil
}
