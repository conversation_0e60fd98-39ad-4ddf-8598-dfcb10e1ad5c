package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"restaurant-backend/internal/models"

	"github.com/sirupsen/logrus"
)

// LocationService handles location-related operations
type LocationService struct {
	googleMapsAPIKey string
	logger           *logrus.Logger
	httpClient       *http.Client
}

// NewLocationService creates a new location service
func NewLocationService(googleMapsAPIKey string, logger *logrus.Logger) *LocationService {
	return &LocationService{
		googleMapsAPIKey: googleMapsAPIKey,
		logger:           logger,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// GeocodeRequest represents a geocoding request
type GeocodeRequest struct {
	Address string `json:"address"`
	Region  string `json:"region,omitempty"` // Country code for biasing results
}

// GeocodeResponse represents a geocoding response
type GeocodeResponse struct {
	Latitude         float64 `json:"latitude"`
	Longitude        float64 `json:"longitude"`
	FormattedAddress string  `json:"formatted_address"`
	PlaceID          string  `json:"place_id"`
	LocationType     string  `json:"location_type"`
	Accuracy         int     `json:"accuracy"`
}

// GoogleMapsGeocodeResponse represents the Google Maps API response
type GoogleMapsGeocodeResponse struct {
	Results []struct {
		FormattedAddress string `json:"formatted_address"`
		Geometry         struct {
			Location struct {
				Lat float64 `json:"lat"`
				Lng float64 `json:"lng"`
			} `json:"location"`
			LocationType string `json:"location_type"`
		} `json:"geometry"`
		PlaceID string   `json:"place_id"`
		Types   []string `json:"types"`
	} `json:"results"`
	Status string `json:"status"`
}

// NearbySearchRequest represents a nearby search request
type NearbySearchRequest struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
	Radius    float64 `json:"radius"`    // in kilometers
	Limit     int     `json:"limit"`     // max results
	ShopType  string  `json:"shop_type"` // restaurant, cafe, etc.
}

// NearbyShop represents a nearby shop result
type NearbyShop struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	CuisineType string  `json:"cuisine_type"`
	PriceRange  string  `json:"price_range"`
	Rating      float64 `json:"rating"`
	Distance    float64 `json:"distance"`
	Latitude    float64 `json:"latitude"`
	Longitude   float64 `json:"longitude"`
	IsActive    bool    `json:"is_active"`
	Address     string  `json:"address"`
	Phone       string  `json:"phone"`
	Website     string  `json:"website"`
	Logo        string  `json:"logo"`
	CoverImage  string  `json:"cover_image"`
}

// GeocodeAddress geocodes an address using Google Maps API
func (s *LocationService) GeocodeAddress(request GeocodeRequest) (*GeocodeResponse, error) {
	if s.googleMapsAPIKey == "" {
		return nil, fmt.Errorf("Google Maps API key not configured")
	}

	// Build the request URL
	baseURL := "https://maps.googleapis.com/maps/api/geocode/json"
	params := url.Values{}
	params.Add("address", request.Address)
	params.Add("key", s.googleMapsAPIKey)

	if request.Region != "" {
		params.Add("region", request.Region)
	}

	requestURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// Make the request
	resp, err := s.httpClient.Get(requestURL)
	if err != nil {
		s.logger.WithError(err).Error("Failed to make geocoding request")
		return nil, fmt.Errorf("failed to make geocoding request: %w", err)
	}
	defer resp.Body.Close()

	// Read the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		s.logger.WithError(err).Error("Failed to read geocoding response")
		return nil, fmt.Errorf("failed to read geocoding response: %w", err)
	}

	// Parse the response
	var googleResp GoogleMapsGeocodeResponse
	if err := json.Unmarshal(body, &googleResp); err != nil {
		s.logger.WithError(err).Error("Failed to parse geocoding response")
		return nil, fmt.Errorf("failed to parse geocoding response: %w", err)
	}

	// Check the status
	if googleResp.Status != "OK" {
		s.logger.WithField("status", googleResp.Status).Error("Geocoding request failed")
		return nil, fmt.Errorf("geocoding request failed with status: %s", googleResp.Status)
	}

	// Check if we have results
	if len(googleResp.Results) == 0 {
		return nil, fmt.Errorf("no geocoding results found for address: %s", request.Address)
	}

	// Use the first result
	result := googleResp.Results[0]

	// Determine accuracy based on location type
	accuracy := s.getAccuracyFromLocationType(result.Geometry.LocationType)

	return &GeocodeResponse{
		Latitude:         result.Geometry.Location.Lat,
		Longitude:        result.Geometry.Location.Lng,
		FormattedAddress: result.FormattedAddress,
		PlaceID:          result.PlaceID,
		LocationType:     "geocoded",
		Accuracy:         accuracy,
	}, nil
}

// UpdateShopLocation updates a shop's location information
func (s *LocationService) UpdateShopLocation(shop *models.Shop, geocodeResp *GeocodeResponse) {
	now := time.Now()

	shop.Address.Latitude = &geocodeResp.Latitude
	shop.Address.Longitude = &geocodeResp.Longitude
	shop.Address.LocationAccuracy = &geocodeResp.Accuracy
	shop.Address.GeocodedAt = &now
	shop.Address.PlaceID = geocodeResp.PlaceID
	shop.Address.FormattedAddress = geocodeResp.FormattedAddress
	shop.Address.LocationType = geocodeResp.LocationType
}

// UpdateShopBranchLocation updates a shop branch's location information
func (s *LocationService) UpdateShopBranchLocation(branch *models.ShopBranch, geocodeResp *GeocodeResponse) {
	now := time.Now()

	branch.Address.Latitude = &geocodeResp.Latitude
	branch.Address.Longitude = &geocodeResp.Longitude
	branch.Address.LocationAccuracy = &geocodeResp.Accuracy
	branch.Address.GeocodedAt = &now
	branch.Address.PlaceID = geocodeResp.PlaceID
	branch.Address.FormattedAddress = geocodeResp.FormattedAddress
	branch.Address.LocationType = geocodeResp.LocationType
}

// CalculateDistance calculates the distance between two points in kilometers
func (s *LocationService) CalculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	const earthRadius = 6371 // Earth radius in kilometers

	// Convert degrees to radians
	lat1Rad := lat1 * (3.14159265359 / 180)
	lng1Rad := lng1 * (3.14159265359 / 180)
	lat2Rad := lat2 * (3.14159265359 / 180)
	lng2Rad := lng2 * (3.14159265359 / 180)

	// Haversine formula
	dlat := lat2Rad - lat1Rad
	dlng := lng2Rad - lng1Rad

	a := (dlat/2)*(dlat/2) + lat1Rad*lat2Rad*(dlng/2)*(dlng/2)
	c := 2 * (a / (1 + a)) // Simplified atan2

	return earthRadius * c
}

// ValidateCoordinates validates latitude and longitude values
func (s *LocationService) ValidateCoordinates(lat, lng float64) error {
	if lat < -90 || lat > 90 {
		return fmt.Errorf("invalid latitude: %f (must be between -90 and 90)", lat)
	}
	if lng < -180 || lng > 180 {
		return fmt.Errorf("invalid longitude: %f (must be between -180 and 180)", lng)
	}
	return nil
}

// FormatCoordinates formats coordinates for display
func (s *LocationService) FormatCoordinates(lat, lng float64) string {
	return fmt.Sprintf("%.6f, %.6f", lat, lng)
}

// getAccuracyFromLocationType returns accuracy in meters based on Google's location type
func (s *LocationService) getAccuracyFromLocationType(locationType string) int {
	switch locationType {
	case "ROOFTOP":
		return 10 // Very accurate
	case "RANGE_INTERPOLATED":
		return 50 // Good accuracy
	case "GEOMETRIC_CENTER":
		return 100 // Moderate accuracy
	case "APPROXIMATE":
		return 500 // Low accuracy
	default:
		return 1000 // Unknown accuracy
	}
}

// BuildAddressString builds a complete address string from address components
func (s *LocationService) BuildAddressString(address models.Address) string {
	parts := []string{}

	if address.Street != "" {
		parts = append(parts, address.Street)
	}
	if address.City != "" {
		parts = append(parts, address.City)
	}
	if address.State != "" {
		parts = append(parts, address.State)
	}
	if address.ZipCode != "" {
		parts = append(parts, address.ZipCode)
	}
	if address.Country != "" {
		parts = append(parts, address.Country)
	}

	result := ""
	for i, part := range parts {
		if i > 0 {
			result += ", "
		}
		result += part
	}

	return result
}
