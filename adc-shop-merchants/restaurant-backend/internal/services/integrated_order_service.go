package services

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"
)

// IntegratedOrderService handles order creation with Stripe Connect integration
type IntegratedOrderService struct {
	db                   *gorm.DB
	logger               *logrus.Logger
	orderRepo            repositories.OrderRepository
	stripePaymentService *StripePaymentService
	stripeConnectService *StripeConnectService
}

// NewIntegratedOrderService creates a new integrated order service
func NewIntegratedOrderService(
	db *gorm.DB,
	logger *logrus.Logger,
	orderRepo repositories.OrderRepository,
	stripePaymentService *StripePaymentService,
	stripeConnectService *StripeConnectService,
) *IntegratedOrderService {
	return &IntegratedOrderService{
		db:                   db,
		logger:               logger,
		orderRepo:            orderRepo,
		stripePaymentService: stripePaymentService,
		stripeConnectService: stripeConnectService,
	}
}

// CreateOrderWithPaymentRequest represents the request to create an order with payment
type CreateOrderWithPaymentRequest struct {
	// Order details
	BranchID        uuid.UUID                     `json:"branch_id" binding:"required"`
	TableID         *uuid.UUID                    `json:"table_id,omitempty"`
	CustomerName    string                        `json:"customer_name" binding:"required"`
	CustomerPhone   string                        `json:"customer_phone" binding:"required"`
	CustomerEmail   string                        `json:"customer_email,omitempty"`
	OrderType       string                        `json:"order_type" binding:"required,oneof=dine_in takeaway delivery"`
	Items           []OrderItemRequest            `json:"items" binding:"required,min=1"`
	Notes           string                        `json:"notes,omitempty"`
	DeliveryAddress *types.DeliveryAddressRequest `json:"delivery_address,omitempty"`
	ScheduledFor    *time.Time                    `json:"scheduled_for,omitempty"`

	// Payment details
	PaymentMethod string            `json:"payment_method" binding:"required,oneof=card cash stripe_connect"`
	AutoConfirm   bool              `json:"auto_confirm,omitempty"` // Auto-confirm payment intent
	Metadata      map[string]string `json:"metadata,omitempty"`
}

// OrderItemRequest represents an item in the order
type OrderItemRequest struct {
	MenuItemID      uuid.UUID                `json:"menu_item_id" binding:"required"`
	Quantity        int                      `json:"quantity" binding:"required,min=1"`
	UnitPrice       float64                  `json:"unit_price" binding:"required,min=0"`
	Customizations  []OrderItemCustomization `json:"customizations,omitempty"`
	SpecialRequests string                   `json:"special_requests,omitempty"`
}

// OrderItemCustomization represents customizations for an order item
type OrderItemCustomization struct {
	OptionID uuid.UUID `json:"option_id"`
	ChoiceID uuid.UUID `json:"choice_id"`
	Name     string    `json:"name" binding:"required"`
	Price    float64   `json:"price,omitempty"`
}

// CreateOrderWithPaymentResponse represents the response for order creation with payment
type CreateOrderWithPaymentResponse struct {
	Order              *models.Order          `json:"order"`
	PaymentIntent      *PaymentIntentResponse `json:"payment_intent,omitempty"`
	ConnectedAccountID string                 `json:"connected_account_id,omitempty"`
	RequiresPayment    bool                   `json:"requires_payment"`
	EstimatedTime      *int                   `json:"estimated_time,omitempty"`
}

// CreateOrderWithPayment creates an order and associated payment intent
func (s *IntegratedOrderService) CreateOrderWithPayment(ctx context.Context, req CreateOrderWithPaymentRequest) (*CreateOrderWithPaymentResponse, error) {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Calculate order totals
	subtotal, err := s.calculateSubtotal(ctx, req.Items)
	if err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to calculate subtotal: %w", err)
	}

	// Calculate taxes and fees
	serviceCharge := subtotal * 0.10 // 10% service charge
	tax := subtotal * 0.07           // 7% VAT
	total := subtotal + serviceCharge + tax

	// Generate order number
	orderNumber := s.generateOrderNumber()

	// Create order items
	orderItems := make([]models.OrderItem, len(req.Items))
	for i, item := range req.Items {
		// Convert customizations to ModificationsData
		modifications := make(models.ModificationsData, len(item.Customizations))
		for j, custom := range item.Customizations {
			modifications[j] = models.OrderModification{
				OptionID: custom.OptionID,
				ChoiceID: custom.ChoiceID,
				Name:     custom.Name,
				Price:    custom.Price,
			}
		}

		// Calculate item total
		itemTotal := item.UnitPrice * float64(item.Quantity)
		for _, mod := range modifications {
			itemTotal += mod.Price * float64(item.Quantity)
		}

		orderItems[i] = models.OrderItem{
			MenuItemID:    &item.MenuItemID,
			Name:          fmt.Sprintf("Menu Item %s", item.MenuItemID.String()[:8]), // Placeholder name
			Price:         item.UnitPrice,
			Quantity:      item.Quantity,
			Modifications: modifications,
			Notes:         item.SpecialRequests,
			Total:         itemTotal,
			Status:        models.OrderItemStatusPending,
		}
	}

	// Determine payment status based on payment method
	paymentStatus := types.PaymentStatusPending
	if req.PaymentMethod == "cash" {
		paymentStatus = types.PaymentStatusPending // Will be marked as paid when order is completed
	}

	// Create order
	order := &models.Order{
		BranchID:      req.BranchID,
		TableID:       req.TableID,
		OrderNumber:   orderNumber,
		CustomerName:  req.CustomerName,
		CustomerPhone: req.CustomerPhone,
		CustomerEmail: req.CustomerEmail,
		Type:          req.OrderType,
		Status:        types.OrderStatusPending,
		Items:         orderItems,
		Subtotal:      subtotal,
		ServiceCharge: serviceCharge,
		Tax:           tax,
		Total:         total,
		Notes:         req.Notes,
		PaymentMethod: req.PaymentMethod,
		PaymentStatus: paymentStatus,
	}

	// Note: Delivery address handling would need to be added to the Order model
	// For now, we'll store delivery info in the Notes field if provided
	if req.DeliveryAddress != nil {
		deliveryInfo := fmt.Sprintf("Delivery: %s, %s, %s %s, %s",
			req.DeliveryAddress.Street,
			req.DeliveryAddress.City,
			req.DeliveryAddress.State,
			req.DeliveryAddress.ZipCode,
			req.DeliveryAddress.Country)
		if req.DeliveryAddress.Notes != "" {
			deliveryInfo += " - " + req.DeliveryAddress.Notes
		}
		if order.Notes != "" {
			order.Notes += "\n" + deliveryInfo
		} else {
			order.Notes = deliveryInfo
		}
	}

	// Save order to database
	if err := tx.Create(order).Error; err != nil {
		tx.Rollback()
		s.logger.WithError(err).Error("Failed to create order")
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	// Commit transaction for order creation
	if err := tx.Commit().Error; err != nil {
		s.logger.WithError(err).Error("Failed to commit order transaction")
		return nil, fmt.Errorf("failed to commit order: %w", err)
	}

	response := &CreateOrderWithPaymentResponse{
		Order:           order,
		RequiresPayment: req.PaymentMethod != "cash",
		EstimatedTime:   s.calculateEstimatedTime(req.OrderType, len(req.Items)),
	}

	// Create payment intent if using Stripe Connect
	if req.PaymentMethod == "stripe_connect" || req.PaymentMethod == "card" {
		paymentIntent, connectedAccountID, err := s.createPaymentIntentForOrder(ctx, order, req.Metadata)
		if err != nil {
			s.logger.WithError(err).Error("Failed to create payment intent for order")
			// Don't fail the order creation, but log the error
			s.logger.WithFields(map[string]interface{}{
				"order_id":     order.ID,
				"order_number": order.OrderNumber,
			}).Warn("Order created but payment intent creation failed")
		} else {
			response.PaymentIntent = paymentIntent
			response.ConnectedAccountID = connectedAccountID

			// Update order with payment intent ID
			if err := s.updateOrderPaymentIntent(ctx, order.ID, paymentIntent.PaymentIntentID, connectedAccountID); err != nil {
				s.logger.WithError(err).Warn("Failed to update order with payment intent ID")
			}
		}
	}

	logFields := map[string]interface{}{
		"order_id":         order.ID,
		"order_number":     order.OrderNumber,
		"customer_name":    order.CustomerName,
		"total":            order.Total,
		"payment_method":   order.PaymentMethod,
		"requires_payment": response.RequiresPayment,
	}

	if response.PaymentIntent != nil {
		logFields["payment_intent_id"] = response.PaymentIntent.PaymentIntentID
	}

	if response.ConnectedAccountID != "" {
		logFields["connected_account_id"] = response.ConnectedAccountID
	}

	s.logger.WithFields(logFields).Info("Order created successfully with payment integration")

	return response, nil
}

// createPaymentIntentForOrder creates a payment intent for the given order
func (s *IntegratedOrderService) createPaymentIntentForOrder(ctx context.Context, order *models.Order, metadata map[string]string) (*PaymentIntentResponse, string, error) {
	// Get shop ID from branch
	var branch models.ShopBranch
	if err := s.db.Where("id = ?", order.BranchID).First(&branch).Error; err != nil {
		return nil, "", fmt.Errorf("failed to get branch: %w", err)
	}

	// Prepare metadata
	paymentMetadata := make(map[string]string)
	if metadata != nil {
		for k, v := range metadata {
			paymentMetadata[k] = v
		}
	}
	paymentMetadata["order_id"] = order.ID.String()
	paymentMetadata["order_number"] = order.OrderNumber
	paymentMetadata["customer_name"] = order.CustomerName
	paymentMetadata["order_type"] = order.Type
	if order.TableID != nil {
		paymentMetadata["table_id"] = order.TableID.String()
	}

	// Convert total to smallest currency unit (satang for THB)
	amountInSatang := int64(order.Total * 100)

	// Create payment intent request
	paymentReq := CreatePaymentIntentRequest{
		Amount:      amountInSatang,
		Currency:    "thb",
		ShopID:      branch.ShopID,
		BranchID:    &order.BranchID,
		OrderID:     &order.ID,
		Description: fmt.Sprintf("Order %s - %s", order.OrderNumber, order.CustomerName),
		Metadata:    paymentMetadata,
	}

	// Create payment intent
	paymentIntent, err := s.stripePaymentService.CreatePaymentIntent(ctx, paymentReq)
	if err != nil {
		return nil, "", err
	}

	return paymentIntent, paymentIntent.ConnectedAccountID, nil
}

// updateOrderPaymentIntent updates the order with payment intent information
func (s *IntegratedOrderService) updateOrderPaymentIntent(ctx context.Context, orderID uuid.UUID, paymentIntentID, connectedAccountID string) error {
	return s.db.WithContext(ctx).Model(&models.Order{}).
		Where("id = ?", orderID).
		Updates(map[string]interface{}{
			"payment_intent_id": paymentIntentID,
			"connected_account": connectedAccountID,
		}).Error
}

// calculateSubtotal calculates the subtotal for the order items
func (s *IntegratedOrderService) calculateSubtotal(ctx context.Context, items []OrderItemRequest) (float64, error) {
	var subtotal float64
	for _, item := range items {
		itemTotal := item.UnitPrice * float64(item.Quantity)

		// Add customization costs
		for _, custom := range item.Customizations {
			itemTotal += custom.Price * float64(item.Quantity)
		}

		subtotal += itemTotal
	}
	return subtotal, nil
}

// generateOrderNumber generates a unique order number
func (s *IntegratedOrderService) generateOrderNumber() string {
	now := time.Now()
	return fmt.Sprintf("ORD-%d%02d%02d-%02d%02d%02d-%04d",
		now.Year(), now.Month(), now.Day(),
		now.Hour(), now.Minute(), now.Second(),
		now.Nanosecond()/1000000) // milliseconds
}

// calculateEstimatedTime calculates estimated preparation time
func (s *IntegratedOrderService) calculateEstimatedTime(orderType string, itemCount int) *int {
	baseTime := 15 // base 15 minutes

	// Add time based on order type
	switch orderType {
	case "dine_in":
		baseTime += 5
	case "takeaway":
		baseTime += 0
	case "delivery":
		baseTime += 20
	}

	// Add time based on item count
	baseTime += (itemCount - 1) * 3 // 3 minutes per additional item

	// Cap at reasonable maximum
	if baseTime > 60 {
		baseTime = 60
	}

	return &baseTime
}
