package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// StringSlice represents a slice of strings that can be stored as JSONB
type StringSlice []string

// <PERSON>an implements the sql.Scanner interface for StringSlice
func (s *StringSlice) Scan(value interface{}) error {
	if value == nil {
		*s = StringSlice{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	if len(bytes) == 0 {
		*s = StringSlice{}
		return nil
	}

	return json.Unmarshal(bytes, s)
}

// Value implements the driver.Valuer interface for StringSlice
func (s StringSlice) Value() (driver.Value, error) {
	if len(s) == 0 {
		return json.Marshal([]string{})
	}
	return json.Marshal(s)
}

// StripeConnectAccount represents a Stripe Connect account for a shop/branch
type StripeConnectAccount struct {
	BaseModel

	// Ownership
	ShopID   uuid.UUID  `json:"shop_id" gorm:"type:uuid;not null;index"`
	BranchID *uuid.UUID `json:"branch_id" gorm:"type:uuid;index"` // Optional: specific branch

	// Stripe Account Information
	StripeAccountID string `json:"stripe_account_id" gorm:"type:varchar(255);uniqueIndex;not null"`
	AccountType     string `json:"account_type" gorm:"type:varchar(50);not null"`        // express, standard, custom
	Country         string `json:"country" gorm:"type:varchar(2);not null"`              // ISO country code
	Email           string `json:"email" gorm:"type:varchar(255)"`                       // Account email
	BusinessType    string `json:"business_type" gorm:"type:varchar(50)"`                // individual, company
	DefaultCurrency string `json:"default_currency" gorm:"type:varchar(3);default:'thb'` // ISO currency code

	// Account Status
	DetailsSubmitted bool   `json:"details_submitted" gorm:"default:false"`
	ChargesEnabled   bool   `json:"charges_enabled" gorm:"default:false"`
	PayoutsEnabled   bool   `json:"payouts_enabled" gorm:"default:false"`
	AccountStatus    string `json:"account_status" gorm:"type:varchar(20);default:'pending'"` // pending, active, restricted, inactive

	// Capabilities
	CardPayments string `json:"card_payments" gorm:"type:varchar(20)"` // active, inactive, pending
	Transfers    string `json:"transfers" gorm:"type:varchar(20)"`     // active, inactive, pending

	// Business Profile
	BusinessName string `json:"business_name" gorm:"type:varchar(255)"`
	BusinessURL  string `json:"business_url" gorm:"type:varchar(500)"`
	SupportEmail string `json:"support_email" gorm:"type:varchar(255)"`
	SupportPhone string `json:"support_phone" gorm:"type:varchar(50)"`

	// Requirements (stored as JSON for flexibility)
	CurrentlyDue        StringSlice `json:"currently_due" gorm:"type:jsonb"`
	EventuallyDue       StringSlice `json:"eventually_due" gorm:"type:jsonb"`
	PastDue             StringSlice `json:"past_due" gorm:"type:jsonb"`
	PendingVerification StringSlice `json:"pending_verification" gorm:"type:jsonb"`

	// Settings
	PayoutSchedule string `json:"payout_schedule" gorm:"type:varchar(20);default:'daily'"` // daily, weekly, monthly
	LiveMode       bool   `json:"live_mode" gorm:"default:false"`

	// Timestamps
	StripeCreatedAt    *time.Time `json:"stripe_created_at"`
	LastSyncedAt       *time.Time `json:"last_synced_at"`
	OnboardingComplete bool       `json:"onboarding_complete" gorm:"default:false"`

	// Status
	IsActive bool `json:"is_active" gorm:"default:true"`

	// Relationships
	Shop   Shop        `json:"shop,omitempty" gorm:"foreignKey:ShopID"`
	Branch *ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
}

// StripeConnectWebhook represents webhook events from Stripe Connect
type StripeConnectWebhook struct {
	BaseModel

	// Stripe Information
	StripeEventID   string `json:"stripe_event_id" gorm:"type:varchar(255);uniqueIndex;not null"`
	EventType       string `json:"event_type" gorm:"type:varchar(100);not null;index"`
	StripeAccountID string `json:"stripe_account_id" gorm:"type:varchar(255);index"`

	// Event Data
	EventData        map[string]interface{} `json:"event_data" gorm:"type:jsonb"`
	ProcessedAt      *time.Time             `json:"processed_at"`
	ProcessingStatus string                 `json:"processing_status" gorm:"type:varchar(20);default:'pending'"` // pending, processed, failed

	// Error Handling
	ErrorMessage string `json:"error_message" gorm:"type:text"`
	RetryCount   int    `json:"retry_count" gorm:"default:0"`

	// Relationships
	ConnectAccount *StripeConnectAccount `json:"connect_account,omitempty" gorm:"foreignKey:StripeAccountID;references:StripeAccountID"`
}

// BeforeCreate hook for StripeConnectAccount
func (s *StripeConnectAccount) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	now := time.Now()
	s.CreatedAt = now
	s.UpdatedAt = now
	return nil
}

// BeforeUpdate hook for StripeConnectAccount
func (s *StripeConnectAccount) BeforeUpdate(tx *gorm.DB) error {
	s.UpdatedAt = time.Now()
	return nil
}

// UpdateFromStripeAccount updates the model from Stripe account data
func (s *StripeConnectAccount) UpdateFromStripeAccount(account map[string]interface{}) {
	if email, ok := account["email"].(string); ok {
		s.Email = email
	}
	if country, ok := account["country"].(string); ok {
		s.Country = country
	}
	if accountType, ok := account["type"].(string); ok {
		s.AccountType = accountType
	}
	if detailsSubmitted, ok := account["details_submitted"].(bool); ok {
		s.DetailsSubmitted = detailsSubmitted
	}
	if chargesEnabled, ok := account["charges_enabled"].(bool); ok {
		s.ChargesEnabled = chargesEnabled
	}
	if payoutsEnabled, ok := account["payouts_enabled"].(bool); ok {
		s.PayoutsEnabled = payoutsEnabled
	}

	// Update capabilities
	if capabilities, ok := account["capabilities"].(map[string]interface{}); ok {
		if cardPayments, ok := capabilities["card_payments"].(map[string]interface{}); ok {
			if status, ok := cardPayments["status"].(string); ok {
				s.CardPayments = status
			}
		}
		if transfers, ok := capabilities["transfers"].(map[string]interface{}); ok {
			if status, ok := transfers["status"].(string); ok {
				s.Transfers = status
			}
		}
	}

	// Update business profile
	if businessProfile, ok := account["business_profile"].(map[string]interface{}); ok {
		if name, ok := businessProfile["name"].(string); ok {
			s.BusinessName = name
		}
		if url, ok := businessProfile["url"].(string); ok {
			s.BusinessURL = url
		}
		if supportEmail, ok := businessProfile["support_email"].(string); ok {
			s.SupportEmail = supportEmail
		}
		if supportPhone, ok := businessProfile["support_phone"].(string); ok {
			s.SupportPhone = supportPhone
		}
	}

	// Update requirements
	if requirements, ok := account["requirements"].(map[string]interface{}); ok {
		if currentlyDue, ok := requirements["currently_due"].([]interface{}); ok {
			s.CurrentlyDue = make(StringSlice, len(currentlyDue))
			for i, v := range currentlyDue {
				if str, ok := v.(string); ok {
					s.CurrentlyDue[i] = str
				}
			}
		}
		if eventuallyDue, ok := requirements["eventually_due"].([]interface{}); ok {
			s.EventuallyDue = make(StringSlice, len(eventuallyDue))
			for i, v := range eventuallyDue {
				if str, ok := v.(string); ok {
					s.EventuallyDue[i] = str
				}
			}
		}
		if pastDue, ok := requirements["past_due"].([]interface{}); ok {
			s.PastDue = make(StringSlice, len(pastDue))
			for i, v := range pastDue {
				if str, ok := v.(string); ok {
					s.PastDue[i] = str
				}
			}
		}
		if pendingVerification, ok := requirements["pending_verification"].([]interface{}); ok {
			s.PendingVerification = make(StringSlice, len(pendingVerification))
			for i, v := range pendingVerification {
				if str, ok := v.(string); ok {
					s.PendingVerification[i] = str
				}
			}
		}
	}

	// Determine account status
	if s.ChargesEnabled && s.PayoutsEnabled {
		s.AccountStatus = "active"
	} else if s.DetailsSubmitted {
		s.AccountStatus = "pending"
	} else {
		s.AccountStatus = "restricted"
	}

	// Check if onboarding is complete
	s.OnboardingComplete = s.DetailsSubmitted && len(s.CurrentlyDue) == 0 && len(s.PastDue) == 0

	// Update sync timestamp
	now := time.Now()
	s.LastSyncedAt = &now
}
