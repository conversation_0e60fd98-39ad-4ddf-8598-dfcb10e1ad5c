package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/utils"
)

// IntegratedOrderHandler handles order operations with Stripe Connect integration
type IntegratedOrderHandler struct {
	integratedOrderService *services.IntegratedOrderService
	stripePaymentService   *services.StripePaymentService
	logger                 *logrus.Logger
}

// NewIntegratedOrderHandler creates a new integrated order handler
func NewIntegratedOrderHandler(
	integratedOrderService *services.IntegratedOrderService,
	stripePaymentService *services.StripePaymentService,
	logger *logrus.Logger,
) *IntegratedOrderHandler {
	return &IntegratedOrderHandler{
		integratedOrderService: integratedOrderService,
		stripePaymentService:   stripePaymentService,
		logger:                 logger,
	}
}

// CreateOrderWithPayment creates an order with integrated payment processing
func (h *IntegratedOrderHandler) CreateOrderWithPayment(c *gin.Context) {
	var req services.CreateOrderWithPaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// Create order with payment
	response, err := h.integratedOrderService.CreateOrderWithPayment(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create order with payment")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create order", err.Error())
		return
	}

	logFields := map[string]interface{}{
		"order_id":         response.Order.ID,
		"order_number":     response.Order.OrderNumber,
		"total":            response.Order.Total,
		"requires_payment": response.RequiresPayment,
	}

	if response.PaymentIntent != nil {
		logFields["payment_intent_id"] = response.PaymentIntent.PaymentIntentID
	}

	if response.ConnectedAccountID != "" {
		logFields["connected_account_id"] = response.ConnectedAccountID
	}

	h.logger.WithFields(logFields).Info("Order created successfully with payment integration")

	utils.SuccessResponse(c, "Order created successfully", response)
}

// ConfirmOrderPayment confirms payment for an existing order
func (h *IntegratedOrderHandler) ConfirmOrderPayment(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Order ID is required", "")
		return
	}

	orderUUID, err := uuid.Parse(orderID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid order ID format", err.Error())
		return
	}

	var req struct {
		PaymentIntentID    string  `json:"payment_intent_id" binding:"required"`
		ConnectedAccountID string  `json:"connected_account_id" binding:"required"`
		PaymentMethodID    *string `json:"payment_method_id,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// Confirm payment intent
	paymentResponse, err := h.stripePaymentService.ConfirmPaymentIntent(
		c.Request.Context(),
		req.PaymentIntentID,
		req.ConnectedAccountID,
		req.PaymentMethodID,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to confirm payment intent")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to confirm payment", err.Error())
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"order_id":             orderUUID,
		"payment_intent_id":    req.PaymentIntentID,
		"payment_status":       paymentResponse.Status,
		"connected_account_id": req.ConnectedAccountID,
	}).Info("Order payment confirmed successfully")

	utils.SuccessResponse(c, "Payment confirmed successfully", paymentResponse)
}

// CancelOrderPayment cancels payment for an existing order
func (h *IntegratedOrderHandler) CancelOrderPayment(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Order ID is required", "")
		return
	}

	orderUUID, err := uuid.Parse(orderID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid order ID format", err.Error())
		return
	}

	var req struct {
		PaymentIntentID    string  `json:"payment_intent_id" binding:"required"`
		ConnectedAccountID string  `json:"connected_account_id" binding:"required"`
		Reason             *string `json:"reason,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// Cancel payment intent
	paymentResponse, err := h.stripePaymentService.CancelPaymentIntent(
		c.Request.Context(),
		req.PaymentIntentID,
		req.ConnectedAccountID,
		req.Reason,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to cancel payment intent")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to cancel payment", err.Error())
		return
	}

	h.logger.WithFields(map[string]interface{}{
		"order_id":             orderUUID,
		"payment_intent_id":    req.PaymentIntentID,
		"payment_status":       paymentResponse.Status,
		"connected_account_id": req.ConnectedAccountID,
		"reason":               req.Reason,
	}).Info("Order payment cancelled successfully")

	utils.SuccessResponse(c, "Payment cancelled successfully", paymentResponse)
}

// CreateQuickOrder creates a quick order for common scenarios
func (h *IntegratedOrderHandler) CreateQuickOrder(c *gin.Context) {
	var req struct {
		// Quick order details
		BranchID      uuid.UUID  `json:"branch_id" binding:"required"`
		TableID       *uuid.UUID `json:"table_id,omitempty"`
		CustomerName  string     `json:"customer_name" binding:"required"`
		CustomerPhone string     `json:"customer_phone" binding:"required"`
		CustomerEmail string     `json:"customer_email,omitempty"`
		OrderType     string     `json:"order_type" binding:"required,oneof=dine_in takeaway delivery"`

		// Simplified items (menu item ID and quantity)
		Items []struct {
			MenuItemID uuid.UUID `json:"menu_item_id" binding:"required"`
			Quantity   int       `json:"quantity" binding:"required,min=1"`
		} `json:"items" binding:"required,min=1"`

		Notes         string `json:"notes,omitempty"`
		PaymentMethod string `json:"payment_method" binding:"required,oneof=card cash stripe_connect"`
		AutoConfirm   bool   `json:"auto_confirm,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// TODO: Get menu item prices from database
	// For now, using placeholder prices - in real implementation, fetch from menu_items table
	orderItems := make([]services.OrderItemRequest, len(req.Items))
	for i, item := range req.Items {
		// In real implementation, fetch price from database
		unitPrice := 150.0 // Placeholder price

		orderItems[i] = services.OrderItemRequest{
			MenuItemID: item.MenuItemID,
			Quantity:   item.Quantity,
			UnitPrice:  unitPrice,
		}
	}

	// Create full order request
	orderReq := services.CreateOrderWithPaymentRequest{
		BranchID:      req.BranchID,
		TableID:       req.TableID,
		CustomerName:  req.CustomerName,
		CustomerPhone: req.CustomerPhone,
		CustomerEmail: req.CustomerEmail,
		OrderType:     req.OrderType,
		Items:         orderItems,
		Notes:         req.Notes,
		PaymentMethod: req.PaymentMethod,
		AutoConfirm:   req.AutoConfirm,
		Metadata: map[string]string{
			"order_source": "quick_order",
			"created_via":  "api",
		},
	}

	// Create order with payment
	response, err := h.integratedOrderService.CreateOrderWithPayment(c.Request.Context(), orderReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create quick order")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create order", err.Error())
		return
	}

	logFields := map[string]interface{}{
		"order_id":         response.Order.ID,
		"order_number":     response.Order.OrderNumber,
		"order_type":       "quick_order",
		"total":            response.Order.Total,
		"requires_payment": response.RequiresPayment,
	}

	if response.PaymentIntent != nil {
		logFields["payment_intent_id"] = response.PaymentIntent.PaymentIntentID
	}

	if response.ConnectedAccountID != "" {
		logFields["connected_account_id"] = response.ConnectedAccountID
	}

	h.logger.WithFields(logFields).Info("Quick order created successfully")

	utils.SuccessResponse(c, "Quick order created successfully", response)
}

// GetOrderPaymentStatus gets the payment status for an order
func (h *IntegratedOrderHandler) GetOrderPaymentStatus(c *gin.Context) {
	orderID := c.Param("order_id")
	if orderID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Order ID is required", "")
		return
	}

	paymentIntentID := c.Query("payment_intent_id")
	connectedAccountID := c.Query("connected_account_id")

	if paymentIntentID == "" || connectedAccountID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Payment intent ID and connected account ID are required", "")
		return
	}

	// Get payment intent status
	paymentResponse, err := h.stripePaymentService.GetPaymentIntent(
		c.Request.Context(),
		paymentIntentID,
		connectedAccountID,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get payment intent status")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to get payment status", err.Error())
		return
	}

	utils.SuccessResponse(c, "Payment status retrieved successfully", paymentResponse)
}

// CreateTableOrder creates an order specifically for table service
func (h *IntegratedOrderHandler) CreateTableOrder(c *gin.Context) {
	tableID := c.Param("table_id")
	if tableID == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "Table ID is required", "")
		return
	}

	tableUUID, err := uuid.Parse(tableID)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid table ID format", err.Error())
		return
	}

	var req struct {
		BranchID      uuid.UUID                   `json:"branch_id" binding:"required"`
		CustomerName  string                      `json:"customer_name" binding:"required"`
		CustomerPhone string                      `json:"customer_phone" binding:"required"`
		CustomerEmail string                      `json:"customer_email,omitempty"`
		Items         []services.OrderItemRequest `json:"items" binding:"required,min=1"`
		Notes         string                      `json:"notes,omitempty"`
		PaymentMethod string                      `json:"payment_method" binding:"required,oneof=card cash stripe_connect"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// Create table order request
	orderReq := services.CreateOrderWithPaymentRequest{
		BranchID:      req.BranchID,
		TableID:       &tableUUID,
		CustomerName:  req.CustomerName,
		CustomerPhone: req.CustomerPhone,
		CustomerEmail: req.CustomerEmail,
		OrderType:     "dine_in",
		Items:         req.Items,
		Notes:         req.Notes,
		PaymentMethod: req.PaymentMethod,
		Metadata: map[string]string{
			"order_source": "table_order",
			"table_id":     tableID,
			"created_via":  "api",
		},
	}

	// Create order with payment
	response, err := h.integratedOrderService.CreateOrderWithPayment(c.Request.Context(), orderReq)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create table order")
		utils.ErrorResponse(c, http.StatusInternalServerError, "Failed to create table order", err.Error())
		return
	}

	logFields := map[string]interface{}{
		"order_id":         response.Order.ID,
		"order_number":     response.Order.OrderNumber,
		"table_id":         tableID,
		"order_type":       "table_order",
		"total":            response.Order.Total,
		"requires_payment": response.RequiresPayment,
	}

	if response.PaymentIntent != nil {
		logFields["payment_intent_id"] = response.PaymentIntent.PaymentIntentID
	}

	if response.ConnectedAccountID != "" {
		logFields["connected_account_id"] = response.ConnectedAccountID
	}

	h.logger.WithFields(logFields).Info("Table order created successfully")

	utils.SuccessResponse(c, "Table order created successfully", response)
}
