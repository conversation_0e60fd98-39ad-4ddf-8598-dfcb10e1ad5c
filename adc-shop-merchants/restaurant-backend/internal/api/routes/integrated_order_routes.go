package routes

import (
	"restaurant-backend/internal/api/handlers"
	"restaurant-backend/internal/api/middleware"

	"github.com/gin-gonic/gin"
)

// SetupIntegratedOrderRoutes sets up order routes with Stripe Connect integration
func SetupIntegratedOrderRoutes(router *gin.Engine, integratedOrderHandler *handlers.IntegratedOrderHandler, jwtSecret string) {
	// Integrated order routes (protected)
	orderGroup := router.Group("/api/v1/orders")
	orderGroup.Use(middleware.AuthRequired(jwtSecret))
	{
		// Order creation with payment integration
		orderGroup.POST("/create-with-payment", integratedOrderHandler.CreateOrderWithPayment)
		orderGroup.POST("/quick-order", integratedOrderHandler.CreateQuickOrder)
		orderGroup.POST("/table/:table_id", integratedOrderHandler.CreateTableOrder)

		// Payment management for orders
		orderGroup.POST("/:order_id/confirm-payment", integratedOrderHandler.ConfirmOrderPayment)
		orderGroup.POST("/:order_id/cancel-payment", integratedOrderHandler.CancelOrderPayment)
		orderGroup.GET("/:order_id/payment-status", integratedOrderHandler.GetOrderPaymentStatus)
	}
}
