package routes

import (
	"restaurant-backend/internal/api/handlers"
	"restaurant-backend/internal/api/middleware"

	"github.com/gin-gonic/gin"
)

// SetupStripeConnectRoutes sets up Stripe Connect related routes
func SetupStripeConnectRoutes(router *gin.Engine, stripeConnectHandler *handlers.StripeConnectHandler, jwtSecret string) {
	// Stripe Connect routes (protected)
	stripeGroup := router.Group("/api/v1/stripe")
	stripeGroup.Use(middleware.AuthRequired(jwtSecret))
	{
		// Connect account management
		stripeGroup.POST("/connect", stripeConnectHandler.CreateConnectAccount)
		stripeGroup.POST("/connect/register", stripeConnectHandler.RegisterExistingAccount)
		stripeGroup.GET("/connect", stripeConnectHandler.GetConnectAccount)
		stripeGroup.DELETE("/connect", stripeConnectHandler.DeleteConnectAccount)
		stripeGroup.GET("/connect/list", stripeConnectHandler.ListConnectAccounts)

		// Account session for embedded components
		stripeGroup.POST("/account_session", stripeConnectHandler.CreateAccountSession)

		// Payment processing
		stripeGroup.POST("/payment_intents", stripeConnectHandler.CreatePaymentIntent)
		stripeGroup.GET("/payment_intents/:payment_intent_id", stripeConnectHandler.GetPaymentIntent)
		stripeGroup.POST("/payment_intents/:payment_intent_id/confirm", stripeConnectHandler.ConfirmPaymentIntent)
		stripeGroup.POST("/payment_intents/:payment_intent_id/cancel", stripeConnectHandler.CancelPaymentIntent)
	}
}
