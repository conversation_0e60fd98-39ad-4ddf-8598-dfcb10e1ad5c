package types

import (
	"time"

	"github.com/google/uuid"
)

// Table request types
type CreateTableRequest struct {
	AreaID      *uuid.UUID      `json:"area_id"`
	Number      string          `json:"number" binding:"required"`
	Name        string          `json:"name"`
	Capacity    int             `json:"capacity" binding:"required,min=1"`
	MinCapacity int             `json:"min_capacity" binding:"min=1"`
	MaxCapacity int             `json:"max_capacity" binding:"min=1"`
	Shape       string          `json:"shape" binding:"oneof=round square rectangle"`
	Position    PositionRequest `json:"position"`
	ImageURL    string          `json:"image_url"`
	IsActive    bool            `json:"is_active"`
	QRCode      string          `json:"qr_code"`
}

type UpdateTableRequest struct {
	AreaID      *uuid.UUID       `json:"area_id"`
	Number      *string          `json:"number"`
	Name        *string          `json:"name"`
	Capacity    *int             `json:"capacity" binding:"omitempty,min=1"`
	MinCapacity *int             `json:"min_capacity" binding:"omitempty,min=1"`
	MaxCapacity *int             `json:"max_capacity" binding:"omitempty,min=1"`
	Shape       *string          `json:"shape" binding:"omitempty,oneof=round square rectangle"`
	Position    *PositionRequest `json:"position"`
	ImageURL    *string          `json:"image_url"`
	IsActive    *bool            `json:"is_active"`
	QRCode      *string          `json:"qr_code"`
}

type UpdateTableStatusRequest struct {
	Status string `json:"status" binding:"required,oneof=available occupied reserved cleaning maintenance"`
}

type TableFilters struct {
	AreaID   *uuid.UUID `form:"area_id"`
	Status   string     `form:"status"`
	IsActive *bool      `form:"is_active"`
	Search   string     `form:"search"`
	Page     int        `form:"page" binding:"min=0"`
	Limit    int        `form:"limit" binding:"min=0,max=100"`
}

// Area request types
type CreateAreaRequest struct {
	Name        string          `json:"name" binding:"required"`
	Description string          `json:"description"`
	Color       string          `json:"color"`
	Position    PositionRequest `json:"position"`
	IsActive    bool            `json:"is_active"`
}

type UpdateAreaRequest struct {
	Name        *string          `json:"name"`
	Description *string          `json:"description"`
	Color       *string          `json:"color"`
	Position    *PositionRequest `json:"position"`
	IsActive    *bool            `json:"is_active"`
}

type AreaFilters struct {
	IsActive *bool  `form:"is_active"`
	Search   string `form:"search"`
	Page     int    `form:"page" binding:"min=0"`
	Limit    int    `form:"limit" binding:"min=0,max=100"`
}

// Layout request types
type UpdateLayoutRequest struct {
	Name        string               `json:"name" binding:"required"`
	Description string               `json:"description"`
	Width       float64              `json:"width" binding:"required,min=0"`
	Height      float64              `json:"height" binding:"required,min=0"`
	Areas       []AreaLayoutRequest  `json:"areas"`
	Tables      []TableLayoutRequest `json:"tables"`
}

type AreaLayoutRequest struct {
	ID          uuid.UUID       `json:"id"`
	Name        string          `json:"name"`
	Description string          `json:"description"`
	Color       string          `json:"color"`
	Position    PositionRequest `json:"position"`
	IsActive    bool            `json:"is_active"`
}

type TableLayoutRequest struct {
	ID          uuid.UUID       `json:"id"`
	AreaID      *uuid.UUID      `json:"area_id"`
	Number      string          `json:"number"`
	Name        string          `json:"name"`
	Capacity    int             `json:"capacity"`
	MinCapacity int             `json:"min_capacity"`
	MaxCapacity int             `json:"max_capacity"`
	Shape       string          `json:"shape"`
	Position    PositionRequest `json:"position"`
	IsActive    bool            `json:"is_active"`
}

// Supporting types
type PositionRequest struct {
	X      float64 `json:"x"`
	Y      float64 `json:"y"`
	Width  float64 `json:"width"`
	Height float64 `json:"height"`
	Angle  float64 `json:"angle"`
}

type GenerateQRCodeRequest struct {
	Size    int    `json:"size" binding:"min=100,max=1000"`
	Format  string `json:"format" binding:"oneof=png jpg svg"`
	BaseURL string `json:"base_url,omitempty"` // Customer domain URL
}

// Response types
type TableResponse struct {
	ID          uuid.UUID        `json:"id"`
	BranchID    uuid.UUID        `json:"branch_id"`
	AreaID      *uuid.UUID       `json:"area_id"`
	Area        *AreaResponse    `json:"area,omitempty"`
	Number      string           `json:"number"`
	Name        string           `json:"name"`
	Capacity    int              `json:"capacity"`
	MinCapacity int              `json:"min_capacity"`
	MaxCapacity int              `json:"max_capacity"`
	Shape       string           `json:"shape"`
	Position    PositionResponse `json:"position"`
	Status      string           `json:"status"`
	ImageURL    string           `json:"image_url"`
	IsActive    bool             `json:"is_active"`
	QRCode      string           `json:"qr_code"`
	QRCodeURL   string           `json:"qr_code_url"`
	CreatedAt   time.Time        `json:"created_at"`
	UpdatedAt   time.Time        `json:"updated_at"`
}

type TablesResponse struct {
	Data       []TableResponse `json:"data"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	Limit      int             `json:"limit"`
	TotalPages int             `json:"total_pages"`
}

type AreaResponse struct {
	ID          uuid.UUID        `json:"id"`
	BranchID    uuid.UUID        `json:"branch_id"`
	FloorID     *uuid.UUID       `json:"floor_id,omitempty"`
	Name        string           `json:"name"`
	Description string           `json:"description"`
	Color       string           `json:"color"`
	Position    PositionResponse `json:"position"`
	IsActive    bool             `json:"is_active"`
	TableCount  int              `json:"table_count"`
	CreatedAt   time.Time        `json:"created_at"`
	UpdatedAt   time.Time        `json:"updated_at"`
}

type AreasResponse struct {
	Data       []AreaResponse `json:"data"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	Limit      int            `json:"limit"`
	TotalPages int            `json:"total_pages"`
}

type LayoutResponse struct {
	ID          uuid.UUID       `json:"id"`
	BranchID    uuid.UUID       `json:"branch_id"`
	Name        string          `json:"name"`
	Description string          `json:"description"`
	Width       float64         `json:"width"`
	Height      float64         `json:"height"`
	Areas       []AreaResponse  `json:"areas"`
	Tables      []TableResponse `json:"tables"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}

type PositionResponse struct {
	X      float64 `json:"x"`
	Y      float64 `json:"y"`
	Width  float64 `json:"width"`
	Height float64 `json:"height"`
	Angle  float64 `json:"angle"`
}

type QRCodeResponse struct {
	TableID     uuid.UUID `json:"table_id"`
	QRCode      string    `json:"qr_code"`
	QRCodeURL   string    `json:"qr_code_url"`
	OrderURL    string    `json:"order_url"`
	ImageData   string    `json:"image_data,omitempty"`
	Format      string    `json:"format"`
	Size        int       `json:"size"`
	TableNumber int       `json:"table_number"`
	ShopSlug    string    `json:"shop_slug"`
	BranchSlug  string    `json:"branch_slug"`
}

// Table status constants
const (
	TableStatusAvailable   = "available"
	TableStatusOccupied    = "occupied"
	TableStatusReserved    = "reserved"
	TableStatusCleaning    = "cleaning"
	TableStatusMaintenance = "maintenance"
)

// Table shape constants
const (
	TableShapeRound     = "round"
	TableShapeSquare    = "square"
	TableShapeRectangle = "rectangle"
)
