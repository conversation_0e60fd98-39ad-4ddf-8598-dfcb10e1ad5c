package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// SuccessResponse sends a standardized success response
func SuccessResponse(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": message,
		"data":    data,
	})
}

// ErrorResponse sends a standardized error response
func ErrorResponse(c *gin.Context, statusCode int, message string, details string) {
	response := gin.H{
		"success": false,
		"message": message,
	}
	
	if details != "" {
		response["details"] = details
	}
	
	c.JSON(statusCode, response)
}

// PaginatedResponse sends a standardized paginated response
func PaginatedResponse(c *gin.Context, message string, data interface{}, page, limit, total int) {
	totalPages := (total + limit - 1) / limit
	if totalPages < 0 {
		totalPages = 0
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": message,
		"data":    data,
		"pagination": gin.H{
			"page":        page,
			"limit":       limit,
			"total":       total,
			"total_pages": totalPages,
		},
	})
}
