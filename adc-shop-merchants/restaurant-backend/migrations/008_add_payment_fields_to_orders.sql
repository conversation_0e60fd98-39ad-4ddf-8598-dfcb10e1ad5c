-- Migration: Add payment fields to orders table
-- Description: Add missing payment-related fields to support Stripe Connect integration
-- Created: 2024-12-04

-- Add payment-related fields to orders table
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS payment_intent_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS connected_account VARCHAR(255);

-- Add indexes for payment fields
CREATE INDEX IF NOT EXISTS idx_orders_payment_intent_id ON orders(payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_orders_connected_account ON orders(connected_account);

-- Add foreign key constraint to link connected_account to stripe_connect_accounts
ALTER TABLE orders 
ADD CONSTRAINT fk_orders_connected_account 
FOREIGN KEY (connected_account) 
REFERENCES stripe_connect_accounts(stripe_account_id) 
ON DELETE SET NULL;

-- Add comments for documentation
COMMENT ON COLUMN orders.payment_intent_id IS 'Stripe Payment Intent ID for tracking payment status';
COMMENT ON COLUMN orders.connected_account IS 'Stripe Connected Account ID for marketplace payments';

-- Update existing orders with NULL values for new fields (they will be populated by future orders)
-- No data migration needed as these are new fields
