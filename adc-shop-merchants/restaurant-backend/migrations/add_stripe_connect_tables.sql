-- Migration: Add Stripe Connect tables
-- Created: 2024-01-XX
-- Description: Add tables for Stripe Connect account management

-- Create stripe_connect_accounts table
CREATE TABLE IF NOT EXISTS stripe_connect_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ownership
    shop_id UUID NOT NULL REFERENCES shops(id) ON DELETE CASCADE,
    branch_id UUID REFERENCES shop_branches(id) ON DELETE CASCADE,
    
    -- Stripe Account Information
    stripe_account_id VARCHAR(255) UNIQUE NOT NULL,
    account_type VARCHAR(50) NOT NULL DEFAULT 'express',
    country VARCHAR(2) NOT NULL DEFAULT 'TH',
    email VARCHAR(255),
    business_type VARCHAR(50),
    default_currency VARCHAR(3) DEFAULT 'thb',
    
    -- Account Status
    details_submitted BOOLEAN DEFAULT FALSE,
    charges_enabled BOOLEAN DEFAULT FALSE,
    payouts_enabled BOOLEAN DEFAULT FALSE,
    account_status VARCHAR(20) DEFAULT 'pending',
    
    -- Capabilities
    card_payments VARCHAR(20),
    transfers VARCHAR(20),
    
    -- Business Profile
    business_name VARCHAR(255),
    business_url VARCHAR(500),
    support_email VARCHAR(255),
    support_phone VARCHAR(50),
    
    -- Requirements (stored as JSONB for flexibility)
    currently_due JSONB DEFAULT '[]',
    eventually_due JSONB DEFAULT '[]',
    past_due JSONB DEFAULT '[]',
    pending_verification JSONB DEFAULT '[]',
    
    -- Settings
    payout_schedule VARCHAR(20) DEFAULT 'daily',
    live_mode BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    stripe_created_at TIMESTAMP WITH TIME ZONE,
    last_synced_at TIMESTAMP WITH TIME ZONE,
    onboarding_complete BOOLEAN DEFAULT FALSE,
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE
);

-- Create stripe_connect_webhooks table
CREATE TABLE IF NOT EXISTS stripe_connect_webhooks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Stripe Information
    stripe_event_id VARCHAR(255) UNIQUE NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    stripe_account_id VARCHAR(255),
    
    -- Event Data
    event_data JSONB,
    processed_at TIMESTAMP WITH TIME ZONE,
    processing_status VARCHAR(20) DEFAULT 'pending',
    
    -- Error Handling
    error_message TEXT,
    retry_count INTEGER DEFAULT 0
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_stripe_connect_accounts_shop_id ON stripe_connect_accounts(shop_id);
CREATE INDEX IF NOT EXISTS idx_stripe_connect_accounts_branch_id ON stripe_connect_accounts(branch_id);
CREATE INDEX IF NOT EXISTS idx_stripe_connect_accounts_stripe_id ON stripe_connect_accounts(stripe_account_id);
CREATE INDEX IF NOT EXISTS idx_stripe_connect_accounts_status ON stripe_connect_accounts(account_status);
CREATE INDEX IF NOT EXISTS idx_stripe_connect_accounts_active ON stripe_connect_accounts(is_active);

CREATE INDEX IF NOT EXISTS idx_stripe_connect_webhooks_event_id ON stripe_connect_webhooks(stripe_event_id);
CREATE INDEX IF NOT EXISTS idx_stripe_connect_webhooks_event_type ON stripe_connect_webhooks(event_type);
CREATE INDEX IF NOT EXISTS idx_stripe_connect_webhooks_account_id ON stripe_connect_webhooks(stripe_account_id);
CREATE INDEX IF NOT EXISTS idx_stripe_connect_webhooks_status ON stripe_connect_webhooks(processing_status);

-- Add foreign key constraint for webhook to connect account
ALTER TABLE stripe_connect_webhooks 
ADD CONSTRAINT fk_stripe_connect_webhooks_account 
FOREIGN KEY (stripe_account_id) 
REFERENCES stripe_connect_accounts(stripe_account_id) 
ON DELETE SET NULL;

-- Add unique constraint to ensure one active account per shop/branch
CREATE UNIQUE INDEX IF NOT EXISTS idx_stripe_connect_accounts_unique_shop_branch 
ON stripe_connect_accounts(shop_id, COALESCE(branch_id, '********-0000-0000-0000-************'::UUID)) 
WHERE is_active = TRUE;

-- Add check constraints
ALTER TABLE stripe_connect_accounts 
ADD CONSTRAINT chk_account_type 
CHECK (account_type IN ('express', 'standard', 'custom'));

ALTER TABLE stripe_connect_accounts 
ADD CONSTRAINT chk_account_status 
CHECK (account_status IN ('pending', 'active', 'restricted', 'inactive'));

ALTER TABLE stripe_connect_accounts 
ADD CONSTRAINT chk_payout_schedule 
CHECK (payout_schedule IN ('daily', 'weekly', 'monthly', 'manual'));

ALTER TABLE stripe_connect_accounts 
ADD CONSTRAINT chk_capability_status 
CHECK (
    (card_payments IS NULL OR card_payments IN ('active', 'inactive', 'pending')) AND
    (transfers IS NULL OR transfers IN ('active', 'inactive', 'pending'))
);

ALTER TABLE stripe_connect_webhooks 
ADD CONSTRAINT chk_processing_status 
CHECK (processing_status IN ('pending', 'processed', 'failed'));

-- Add comments for documentation
COMMENT ON TABLE stripe_connect_accounts IS 'Stores Stripe Connect account information for shops and branches';
COMMENT ON TABLE stripe_connect_webhooks IS 'Stores Stripe Connect webhook events for processing';

COMMENT ON COLUMN stripe_connect_accounts.stripe_account_id IS 'Stripe Connect account ID (e.g., acct_1234567890)';
COMMENT ON COLUMN stripe_connect_accounts.account_type IS 'Type of Stripe account: express, standard, or custom';
COMMENT ON COLUMN stripe_connect_accounts.account_status IS 'Current status of the account: pending, active, restricted, or inactive';
COMMENT ON COLUMN stripe_connect_accounts.currently_due IS 'Array of requirements currently due for account verification';
COMMENT ON COLUMN stripe_connect_accounts.eventually_due IS 'Array of requirements that will be due in the future';
COMMENT ON COLUMN stripe_connect_accounts.past_due IS 'Array of requirements that are past due';
COMMENT ON COLUMN stripe_connect_accounts.pending_verification IS 'Array of requirements pending verification';

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_stripe_connect_accounts_updated_at 
    BEFORE UPDATE ON stripe_connect_accounts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_stripe_connect_webhooks_updated_at 
    BEFORE UPDATE ON stripe_connect_webhooks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
