-- Migration: Add location fields for Google Maps integration
-- Description: Adds latitude, longitude, and location-related fields to shops and shop_branches tables

-- Add location fields to shops table
ALTER TABLE shops 
ADD COLUMN IF NOT EXISTS latitude DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS longitude DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS location_accuracy INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS geocoded_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS place_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS formatted_address TEXT,
ADD COLUMN IF NOT EXISTS location_type VARCHAR(50) DEFAULT 'manual';

-- Add location fields to shop_branches table
ALTER TABLE shop_branches 
ADD COLUMN IF NOT EXISTS latitude DECIMAL(10, 8),
ADD COLUMN IF NOT EXISTS longitude DECIMAL(11, 8),
ADD COLUMN IF NOT EXISTS location_accuracy INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS geocoded_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS place_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS formatted_address TEXT,
ADD COLUMN IF NOT EXISTS location_type VARCHAR(50) DEFAULT 'manual';

-- Create indexes for location-based queries
CREATE INDEX IF NOT EXISTS idx_shops_location ON shops USING GIST (
    ll_to_earth(latitude, longitude)
) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_shop_branches_location ON shop_branches USING GIST (
    ll_to_earth(latitude, longitude)
) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- Create indexes for efficient location searches
CREATE INDEX IF NOT EXISTS idx_shops_lat_lng ON shops (latitude, longitude) 
WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_shop_branches_lat_lng ON shop_branches (latitude, longitude) 
WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- Create indexes for place_id lookups
CREATE INDEX IF NOT EXISTS idx_shops_place_id ON shops (place_id) 
WHERE place_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_shop_branches_place_id ON shop_branches (place_id) 
WHERE place_id IS NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN shops.latitude IS 'Latitude coordinate for Google Maps (-90 to 90)';
COMMENT ON COLUMN shops.longitude IS 'Longitude coordinate for Google Maps (-180 to 180)';
COMMENT ON COLUMN shops.location_accuracy IS 'Accuracy of the location in meters (0 = unknown)';
COMMENT ON COLUMN shops.geocoded_at IS 'Timestamp when the address was last geocoded';
COMMENT ON COLUMN shops.place_id IS 'Google Places API place ID for this location';
COMMENT ON COLUMN shops.formatted_address IS 'Google-formatted address string';
COMMENT ON COLUMN shops.location_type IS 'Source of location data: manual, geocoded, gps, imported';

COMMENT ON COLUMN shop_branches.latitude IS 'Latitude coordinate for Google Maps (-90 to 90)';
COMMENT ON COLUMN shop_branches.longitude IS 'Longitude coordinate for Google Maps (-180 to 180)';
COMMENT ON COLUMN shop_branches.location_accuracy IS 'Accuracy of the location in meters (0 = unknown)';
COMMENT ON COLUMN shop_branches.geocoded_at IS 'Timestamp when the address was last geocoded';
COMMENT ON COLUMN shop_branches.place_id IS 'Google Places API place ID for this location';
COMMENT ON COLUMN shop_branches.formatted_address IS 'Google-formatted address string';
COMMENT ON COLUMN shop_branches.location_type IS 'Source of location data: manual, geocoded, gps, imported';

-- Create function for calculating distance between two points
CREATE OR REPLACE FUNCTION calculate_distance(
    lat1 DECIMAL(10,8), 
    lng1 DECIMAL(11,8), 
    lat2 DECIMAL(10,8), 
    lng2 DECIMAL(11,8)
) RETURNS DECIMAL(10,2) AS $$
DECLARE
    earth_radius DECIMAL := 6371; -- Earth radius in kilometers
    dlat DECIMAL;
    dlng DECIMAL;
    a DECIMAL;
    c DECIMAL;
BEGIN
    -- Convert degrees to radians
    dlat := RADIANS(lat2 - lat1);
    dlng := RADIANS(lng2 - lng1);
    
    -- Haversine formula
    a := SIN(dlat/2) * SIN(dlat/2) + COS(RADIANS(lat1)) * COS(RADIANS(lat2)) * SIN(dlng/2) * SIN(dlng/2);
    c := 2 * ATAN2(SQRT(a), SQRT(1-a));
    
    -- Return distance in kilometers
    RETURN earth_radius * c;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create function to find nearby shops
CREATE OR REPLACE FUNCTION find_nearby_shops(
    search_lat DECIMAL(10,8),
    search_lng DECIMAL(11,8),
    radius_km DECIMAL DEFAULT 10,
    limit_count INTEGER DEFAULT 50
) RETURNS TABLE (
    id UUID,
    name VARCHAR(255),
    distance_km DECIMAL(10,2),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id,
        s.name,
        calculate_distance(search_lat, search_lng, s.latitude, s.longitude) as distance_km,
        s.latitude,
        s.longitude
    FROM shops s
    WHERE s.latitude IS NOT NULL 
      AND s.longitude IS NOT NULL
      AND s.is_active = true
      AND calculate_distance(search_lat, search_lng, s.latitude, s.longitude) <= radius_km
    ORDER BY distance_km ASC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Insert sample location data for existing shops (Bangkok area)
UPDATE shops SET 
    latitude = 13.7563,
    longitude = 100.5018,
    location_accuracy = 100,
    geocoded_at = NOW(),
    formatted_address = 'Bangkok, Thailand',
    location_type = 'sample'
WHERE latitude IS NULL AND name LIKE '%Bangkok%';

UPDATE shops SET 
    latitude = 13.7440,
    longitude = 100.5255,
    location_accuracy = 100,
    geocoded_at = NOW(),
    formatted_address = 'Silom, Bangkok, Thailand',
    location_type = 'sample'
WHERE latitude IS NULL AND name LIKE '%Silom%';

-- Add sample coordinates for any remaining shops (random Bangkok locations)
UPDATE shops SET 
    latitude = 13.7563 + (RANDOM() - 0.5) * 0.1, -- Random within ~5km of Bangkok center
    longitude = 100.5018 + (RANDOM() - 0.5) * 0.1,
    location_accuracy = 500,
    geocoded_at = NOW(),
    formatted_address = 'Bangkok, Thailand',
    location_type = 'sample'
WHERE latitude IS NULL AND longitude IS NULL;
