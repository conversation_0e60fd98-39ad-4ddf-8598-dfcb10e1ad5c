# Testing Incomplete Order Scenarios

## Overview
This document provides comprehensive test scenarios for incomplete orders, payment failures, and edge cases in the restaurant ordering system with Stripe Connect integration.

## Test Environment Setup

### Prerequisites
1. Stripe CLI installed and configured
2. Test connected account: `acct_1RWGqRCvS6V6yiE9`
3. Backend running on `http://localhost:8080`
4. Valid JWT token for authentication

### Get JWT Token
```bash
TOKEN=$(curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}' | jq -r '.token')
```

## Test Scenarios

### 1. Payment Intent Creation Failures

#### 1.1 Test: Connected Account Not Found
```bash
# Test with invalid branch ID (no connected account)
curl -X POST http://localhost:8080/api/v1/orders/create-with-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "branch_id": "********-0000-0000-0000-********0000",
    "customer_name": "Test Customer",
    "customer_email": "<EMAIL>",
    "customer_phone": "+**********",
    "order_type": "dine_in",
    "items": [
      {
        "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
        "quantity": 1,
        "unit_price": 12.99
      }
    ],
    "payment_method": "card"
  }'
```

**Expected Result**: Order creation should fail with "connected account not found" error

#### 1.2 Test: Invalid Payment Amount
```bash
# Test with zero amount
curl -X POST http://localhost:8080/api/v1/orders/create-with-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "branch_id": "52e05798-2c76-4fe6-9c9c-72a83b7e203e",
    "customer_name": "Test Customer",
    "customer_email": "<EMAIL>",
    "customer_phone": "+**********",
    "order_type": "dine_in",
    "items": [
      {
        "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
        "quantity": 1,
        "unit_price": 0
      }
    ],
    "payment_method": "card"
  }'
```

**Expected Result**: Should handle zero/negative amounts gracefully

### 2. Payment Processing Failures

#### 2.1 Test: Card Declined
Use Stripe test card that always declines: `****************`

```bash
# First create an order to get payment intent
ORDER_RESPONSE=$(curl -X POST http://localhost:8080/api/v1/orders/create-with-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "branch_id": "52e05798-2c76-4fe6-9c9c-72a83b7e203e",
    "customer_name": "Test Declined Card",
    "customer_email": "<EMAIL>",
    "customer_phone": "+**********",
    "order_type": "dine_in",
    "items": [
      {
        "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
        "quantity": 1,
        "unit_price": 12.99
      }
    ],
    "payment_method": "card"
  }')

echo "Order created for declined card test:"
echo $ORDER_RESPONSE | jq '.'
```

**Manual Test**: Use the client_secret with declined card `****************` in frontend

#### 2.2 Test: Insufficient Funds
Use Stripe test card: `****************`

```bash
# Create order for insufficient funds test
ORDER_RESPONSE=$(curl -X POST http://localhost:8080/api/v1/orders/create-with-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "branch_id": "52e05798-2c76-4fe6-9c9c-72a83b7e203e",
    "customer_name": "Test Insufficient Funds",
    "customer_email": "<EMAIL>",
    "customer_phone": "+**********",
    "order_type": "dine_in",
    "items": [
      {
        "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
        "quantity": 1,
        "unit_price": 12.99
      }
    ],
    "payment_method": "card"
  }')

echo "Order created for insufficient funds test:"
echo $ORDER_RESPONSE | jq '.'
```

#### 2.3 Test: 3D Secure Authentication Required
Use Stripe test card: `****************`

```bash
# Create order for 3D Secure test
ORDER_RESPONSE=$(curl -X POST http://localhost:8080/api/v1/orders/create-with-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "branch_id": "52e05798-2c76-4fe6-9c9c-72a83b7e203e",
    "customer_name": "Test 3D Secure",
    "customer_email": "<EMAIL>",
    "customer_phone": "+**********",
    "order_type": "dine_in",
    "items": [
      {
        "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
        "quantity": 1,
        "unit_price": 12.99
      }
    ],
    "payment_method": "card"
  }')

echo "Order created for 3D Secure test:"
echo $ORDER_RESPONSE | jq '.'
```

### 3. Order Status Verification Tests

#### 3.1 Test: Check Order Status After Failed Payment
```bash
# Function to check order status
check_order_status() {
  local order_id=$1
  curl -X GET "http://localhost:8080/api/v1/orders/$order_id" \
    -H "Authorization: Bearer $TOKEN" | jq '.data.payment_status'
}

# Usage: check_order_status "order-id-here"
```

#### 3.2 Test: Payment Intent Status Check
```bash
# Function to check payment intent status
check_payment_status() {
  local payment_intent_id=$1
  curl -X GET "http://localhost:8080/api/v1/payments/status/$payment_intent_id" \
    -H "Authorization: Bearer $TOKEN" | jq '.data.status'
}

# Usage: check_payment_status "pi_xxx"
```

### 4. Webhook Simulation Tests

#### 4.1 Test: Simulate Payment Failed Webhook
```bash
# Use Stripe CLI to simulate webhook
stripe trigger payment_intent.payment_failed
```

#### 4.2 Test: Simulate Payment Succeeded Webhook
```bash
# Use Stripe CLI to simulate webhook
stripe trigger payment_intent.succeeded
```

### 5. Timeout and Network Failure Tests

#### 5.1 Test: Payment Intent Creation Timeout
```bash
# Create multiple concurrent orders to test timeout handling
for i in {1..5}; do
  curl -X POST http://localhost:8080/api/v1/orders/create-with-payment \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d '{
      "branch_id": "52e05798-2c76-4fe6-9c9c-72a83b7e203e",
      "customer_name": "Concurrent Test '$i'",
      "customer_email": "concurrent'$i'@example.com",
      "customer_phone": "+123456789'$i'",
      "order_type": "dine_in",
      "items": [
        {
          "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
          "quantity": 1,
          "unit_price": 12.99
        }
      ],
      "payment_method": "card"
    }' &
done
wait
```

### 6. Database Consistency Tests

#### 6.1 Test: Check for Orphaned Orders
```sql
-- Run this query to find orders without payment intents
SELECT o.id, o.order_number, o.payment_status, o.payment_intent_id, o.created_at
FROM orders o
WHERE o.payment_method = 'card' 
  AND o.payment_intent_id IS NULL 
  AND o.payment_status = 'pending'
  AND o.created_at > NOW() - INTERVAL '1 hour';
```

#### 6.2 Test: Check Payment Status Consistency
```sql
-- Find orders with inconsistent payment status
SELECT o.id, o.order_number, o.payment_status, o.status, o.created_at
FROM orders o
WHERE o.payment_status = 'paid' 
  AND o.status = 'pending'
  AND o.created_at > NOW() - INTERVAL '1 hour';
```

## Test Execution Checklist

### Before Testing
- [ ] Backend services running
- [ ] Database accessible
- [ ] Stripe CLI configured
- [ ] Test connected account active
- [ ] JWT token obtained

### During Testing
- [ ] Monitor server logs for errors
- [ ] Check database for order consistency
- [ ] Verify webhook delivery in Stripe dashboard
- [ ] Test frontend payment flow with test cards

### After Testing
- [ ] Clean up test orders
- [ ] Review error logs
- [ ] Document any issues found
- [ ] Verify webhook endpoints working

## Common Issues and Solutions

### Issue: "Connected account not found"
**Solution**: Verify the branch has a valid Stripe Connect account registered

### Issue: "Payment intent creation failed"
**Solution**: Check Stripe API keys and connected account status

### Issue: Orders stuck in "pending" status
**Solution**: Check webhook configuration and processing

### Issue: Database transaction failures
**Solution**: Review database connection pool and transaction handling

## Monitoring Commands

### Real-time Log Monitoring
```bash
# Monitor backend logs
tail -f /path/to/backend/logs/app.log | grep -E "(payment|order|stripe)"
```

### Stripe CLI Webhook Monitoring
```bash
# Forward webhooks to local development
stripe listen --forward-to localhost:8080/api/v1/payments/webhook
```
