#!/bin/bash

# Script to register existing Stripe Connect account
# Usage: ./register_stripe_account.sh [shop_id] [jwt_token]

STRIPE_ACCOUNT_ID="acct_1RWGqRCvS6V6yiE9"
BACKEND_URL="http://localhost:8900"

# Check if shop_id and jwt_token are provided
if [ $# -lt 2 ]; then
    echo "Usage: $0 <shop_id> <jwt_token>"
    echo "Example: $0 123e4567-e89b-12d3-a456-************ eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    exit 1
fi

SHOP_ID=$1
JWT_TOKEN=$2

echo "Registering existing Stripe Connect account..."
echo "Account ID: $STRIPE_ACCOUNT_ID"
echo "Shop ID: $SHOP_ID"
echo "Backend URL: $BACKEND_URL"
echo ""

# Make API call to register existing account
response=$(curl -s -w "\n%{http_code}" \
  -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d "{
    \"stripe_account_id\": \"$STRIPE_ACCOUNT_ID\"
  }" \
  "$BACKEND_URL/api/v1/stripe/connect/register")

# Extract response body and status code
http_code=$(echo "$response" | tail -n1)
response_body=$(echo "$response" | head -n -1)

echo "HTTP Status: $http_code"
echo "Response:"
echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"

if [ "$http_code" = "200" ]; then
    echo ""
    echo "✅ Successfully registered Stripe Connect account!"
    echo ""
    echo "Next steps:"
    echo "1. The account is now linked to your shop"
    echo "2. You can start processing payments"
    echo "3. Check the merchant dashboard for account status"
else
    echo ""
    echo "❌ Failed to register account. Please check the error above."
    echo ""
    echo "Common issues:"
    echo "- Invalid JWT token (expired or malformed)"
    echo "- Invalid shop ID"
    echo "- Account already registered"
    echo "- Backend server not running"
fi
