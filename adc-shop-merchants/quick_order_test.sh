#!/bin/bash

# Quick Order Test - Simple test for order creation and status updates
# Usage: ./quick_order_test.sh

set -e

# Configuration
BACKEND_URL="http://localhost:8080"
BRANCH_ID="52e05798-2c76-4fe6-9c9c-72a83b7e203e"
STRIPE_ACCOUNT_ID="acct_1RWGqRCvS6V6yiE9"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

# Get JWT token
get_token() {
    log_info "Getting authentication token..."
    
    local response=$(curl -s -X POST "$BACKEND_URL/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"email": "<EMAIL>", "password": "password"}')
    
    local token=$(echo "$response" | jq -r '.token // empty')
    
    if [ -n "$token" ] && [ "$token" != "null" ]; then
        log_success "Authentication successful"
        echo "$token"
    else
        log_error "Authentication failed"
        echo "$response"
        exit 1
    fi
}

# Create test order
create_order() {
    local token=$1
    log_info "Creating test order..."
    
    local order_data='{
        "branch_id": "'$BRANCH_ID'",
        "table_id": "550e8400-e29b-41d4-a716-************",
        "customer_name": "Quick Test Customer",
        "customer_phone": "+***********",
        "customer_email": "<EMAIL>",
        "order_type": "dine_in",
        "items": [
            {
                "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
                "quantity": 1,
                "unit_price": 150.0,
                "special_requests": "Quick test order"
            }
        ],
        "payment_method": "stripe_connect",
        "connected_account_id": "'$STRIPE_ACCOUNT_ID'",
        "notes": "Quick test order"
    }'
    
    local response=$(curl -s -w "\n%{http_code}" \
        -X POST "$BACKEND_URL/api/v1/orders/create-with-payment" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $token" \
        -d "$order_data")
    
    local http_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
        log_success "Order created successfully!"
        
        local order_id=$(echo "$body" | jq -r '.data.order.id // empty')
        local payment_intent_id=$(echo "$body" | jq -r '.data.payment_intent.payment_intent_id // empty')
        
        echo "Order ID: $order_id"
        echo "Payment Intent: $payment_intent_id"
        echo "Response: $body" | jq '.'
        
        # Save for status updates
        echo "$order_id" > /tmp/quick_test_order_id
        
        return 0
    else
        log_error "Order creation failed (HTTP $http_code)"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
        return 1
    fi
}

# Update order status
update_status() {
    local token=$1
    local status=$2
    local notes=$3
    
    if [ ! -f /tmp/quick_test_order_id ]; then
        log_warning "No order ID found, skipping status update"
        return 0
    fi
    
    local order_id=$(cat /tmp/quick_test_order_id)
    log_info "Updating order status to: $status"
    
    local update_data='{
        "status": "'$status'",
        "notes": "'$notes'"
    }'
    
    local response=$(curl -s -w "\n%{http_code}" \
        -X PUT "$BACKEND_URL/api/v1/shops/slug/scandine-restaurant/branches/slug/main-branch/orders/$order_id/status" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $token" \
        -d "$update_data")
    
    local http_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "200" ]; then
        log_success "Status updated to: $status"
        echo "$body" | jq '.'
        return 0
    else
        log_error "Status update failed (HTTP $http_code)"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
        return 1
    fi
}

# Get order details
get_order() {
    local token=$1
    
    if [ ! -f /tmp/quick_test_order_id ]; then
        log_warning "No order ID found, skipping order retrieval"
        return 0
    fi
    
    local order_id=$(cat /tmp/quick_test_order_id)
    log_info "Getting order details..."
    
    local response=$(curl -s -w "\n%{http_code}" \
        -X GET "$BACKEND_URL/api/v1/shops/slug/scandine-restaurant/branches/slug/main-branch/orders/$order_id" \
        -H "Authorization: Bearer $token")
    
    local http_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" = "200" ]; then
        log_success "Order details retrieved"
        echo "$body" | jq '.'
        return 0
    else
        log_error "Failed to get order details (HTTP $http_code)"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
        return 1
    fi
}

# Main test function
main() {
    echo "🍽️  Quick Order Test"
    echo "==================="
    echo "Backend: $BACKEND_URL"
    echo "Branch ID: $BRANCH_ID"
    echo "Stripe Account: $STRIPE_ACCOUNT_ID"
    echo ""
    
    # Check if backend is running
    if ! curl -s "$BACKEND_URL/health" > /dev/null; then
        log_error "Backend is not running at $BACKEND_URL"
        log_info "Please start the backend with: cd restaurant-backend && make dev"
        exit 1
    fi
    
    log_success "Backend is running"
    
    # Get authentication token
    local token
    token=$(get_token)
    
    echo ""
    
    # Create order
    if create_order "$token"; then
        echo ""
        
        # Update status through the flow
        update_status "$token" "preparing" "Kitchen started preparing the order"
        sleep 2
        
        update_status "$token" "ready" "Order is ready for pickup"
        sleep 2
        
        update_status "$token" "completed" "Order completed and served to customer"
        
        echo ""
        
        # Get final order details
        get_order "$token"
        
        echo ""
        log_success "Quick order test completed successfully!"
        
    else
        log_error "Order creation failed, stopping test"
        exit 1
    fi
    
    # Cleanup
    rm -f /tmp/quick_test_order_id
    
    echo ""
    echo "🎯 Test Summary:"
    echo "✅ Order creation with Stripe Connect"
    echo "✅ Order status updates (pending → preparing → ready → completed)"
    echo "✅ Order retrieval"
    echo ""
    echo "Next steps:"
    echo "1. Test payment confirmation with frontend"
    echo "2. Test webhook integration"
    echo "3. Test order cancellation"
}

# Check dependencies
if ! command -v curl &> /dev/null; then
    log_error "curl is required but not installed"
    exit 1
fi

if ! command -v jq &> /dev/null; then
    log_error "jq is required but not installed"
    exit 1
fi

# Run the test
main "$@"
