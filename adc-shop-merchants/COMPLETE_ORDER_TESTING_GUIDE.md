# 🍽️ Complete Order Testing Guide

This guide helps you test the complete order flow from creation to completion, including payment processing with Stripe Connect.

## 📋 Prerequisites

### 1. Start Backend Services

```bash
# Terminal 1: Start Merchant Backend
cd adc-shop-merchants/restaurant-backend
make dev
# Should run on http://localhost:8080

# Terminal 2: Start Customer Backend (if testing customer flow)
cd adc-shop-customer/customer-backend
make dev
# Should run on http://localhost:8900
```

### 2. Verify Services are Running

```bash
# Check merchant backend
curl http://localhost:8080/health

# Check customer backend
curl http://localhost:8900/health
```

### 3. Register Stripe Connect Account (if not done)

```bash
# Get auth token
TOKEN=$(curl -s -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}' | jq -r '.token')

# Register Stripe Connect account
curl -X POST http://localhost:8080/api/v1/stripe/connect/register \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"stripe_account_id": "acct_1RWGqRCvS6V6yiE9"}'
```

## 🚀 Quick Testing

### Run Complete Order Flow Test

```bash
cd adc-shop-merchants
./test_complete_order_flow.sh
```

This script tests:
- ✅ Order creation via customer backend
- ✅ Order creation with Stripe Connect via merchant backend
- ✅ Order status retrieval
- ✅ Order status updates (pending → preparing → completed)
- ✅ Payment intent creation and status

### Run Existing Test Scripts

```bash
# Test order creation with different scenarios
./test_order_creation.sh $TOKEN $BRANCH_ID

# Test incomplete order scenarios
./test_incomplete_orders.sh

# Test API endpoints
cd restaurant-backend
./scripts/test-api.sh
```

## 🔧 Manual Testing Steps

### Step 1: Create Order with Payment

```bash
# Get authentication token
TOKEN=$(curl -s -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}' | jq -r '.token')

# Create order with Stripe Connect
curl -X POST http://localhost:8080/api/v1/orders/create-with-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "branch_id": "52e05798-2c76-4fe6-9c9c-72a83b7e203e",
    "table_id": "550e8400-e29b-41d4-a716-************",
    "customer_name": "Test Customer",
    "customer_phone": "+***********",
    "customer_email": "<EMAIL>",
    "order_type": "dine_in",
    "items": [
      {
        "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
        "quantity": 2,
        "unit_price": 150.0,
        "special_requests": "Medium spice"
      }
    ],
    "payment_method": "stripe_connect",
    "connected_account_id": "acct_1RWGqRCvS6V6yiE9"
  }'
```

### Step 2: Update Order Status

```bash
# Update to preparing
curl -X PUT http://localhost:8080/api/v1/shops/slug/scandine-restaurant/branches/slug/main-branch/orders/$ORDER_ID/status \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "status": "preparing",
    "estimated_time": 25,
    "notes": "Order is being prepared"
  }'

# Update to ready
curl -X PUT http://localhost:8080/api/v1/shops/slug/scandine-restaurant/branches/slug/main-branch/orders/$ORDER_ID/status \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "status": "ready",
    "notes": "Order is ready for pickup"
  }'

# Complete order
curl -X PUT http://localhost:8080/api/v1/shops/slug/scandine-restaurant/branches/slug/main-branch/orders/$ORDER_ID/status \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "status": "completed",
    "notes": "Order completed and served"
  }'
```

### Step 3: Check Payment Status

```bash
# Check payment intent status
curl http://localhost:8900/payments/$PAYMENT_INTENT_ID/status
```

## 🎯 Order Status Flow

```
pending → preparing → ready → completed
    ↓         ↓         ↓         ↓
  Created   Kitchen   Ready    Served
```

### Valid Status Transitions

- `pending` → `preparing` (Kitchen starts cooking)
- `preparing` → `ready` (Food is ready)
- `ready` → `completed` (Customer served)
- Any status → `cancelled` (Order cancelled)

## 💳 Payment Testing

### Test Cards for Stripe

```bash
# Successful payment
****************

# Requires authentication
****************

# Declined card
****************

# Insufficient funds
****************
```

### Payment Flow Testing

1. **Create Order**: Order created with `payment_status: "pending"`
2. **Payment Intent**: Stripe payment intent created
3. **Payment Confirmation**: Use Stripe test cards to confirm payment
4. **Webhook**: Payment status updated via webhook
5. **Order Update**: Order status updated based on payment

## 🔍 Debugging

### Check Logs

```bash
# Backend logs
tail -f adc-shop-merchants/restaurant-backend/logs/app.log

# Customer backend logs
tail -f adc-shop-customer/customer-backend/logs/app.log
```

### Common Issues

1. **Authentication Failed**: Check if user exists and password is correct
2. **Stripe Account Not Found**: Ensure Stripe Connect account is registered
3. **Order Creation Failed**: Check branch_id and menu_item_id exist
4. **Payment Failed**: Verify Stripe keys and connected account setup

### Database Queries

```sql
-- Check orders
SELECT id, order_number, status, payment_status, total, created_at 
FROM orders 
ORDER BY created_at DESC 
LIMIT 10;

-- Check Stripe Connect accounts
SELECT * FROM stripe_connect_accounts;

-- Check payment intents
SELECT * FROM payment_intents 
ORDER BY created_at DESC 
LIMIT 5;
```

## 📊 Expected Results

### Successful Order Creation

```json
{
  "success": true,
  "data": {
    "order": {
      "id": "uuid",
      "order_number": "ORD-********-001",
      "status": "pending",
      "payment_status": "pending",
      "total": 300.0
    },
    "payment_intent": {
      "payment_intent_id": "pi_xxx",
      "client_secret": "pi_xxx_secret_xxx",
      "status": "requires_payment_method"
    },
    "connected_account_id": "acct_1RWGqRCvS6V6yiE9"
  }
}
```

### Successful Status Update

```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "status": "preparing",
    "estimated_time": 25,
    "updated_at": "2024-12-22T10:30:00Z"
  }
}
```

## 🎯 Next Steps

After successful testing:

1. **Frontend Integration**: Test with actual frontend forms
2. **Webhook Testing**: Use `stripe trigger` commands
3. **Load Testing**: Test with multiple concurrent orders
4. **Error Scenarios**: Test network failures, timeouts, etc.
5. **Production Testing**: Test with real Stripe account (not test mode)

## 📞 Support

If you encounter issues:

1. Check the logs for error messages
2. Verify all services are running
3. Ensure database connections are working
4. Check Stripe dashboard for payment details
5. Review the test script output for specific failures
