import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
});

// Create a new connected account
export async function POST(request: NextRequest) {
  try {
    const { email, country = 'TH', type = 'express' } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Create a connected account
    const account = await stripe.accounts.create({
      type: type,
      country: country,
      email: email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: 'company',
      settings: {
        payouts: {
          schedule: {
            interval: 'daily',
          },
        },
      },
    });

    return NextResponse.json({
      account_id: account.id,
      email: account.email,
      country: account.country,
      type: account.type,
      created: account.created,
      details_submitted: account.details_submitted,
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
    });
  } catch (error) {
    console.error('Error creating connected account:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode || 500 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Get connected account details
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('account_id');

    if (!accountId) {
      return NextResponse.json(
        { error: 'Account ID is required' },
        { status: 400 }
      );
    }

    // Retrieve account details
    const account = await stripe.accounts.retrieve(accountId);

    return NextResponse.json({
      account_id: account.id,
      email: account.email,
      country: account.country,
      type: account.type,
      created: account.created,
      details_submitted: account.details_submitted,
      charges_enabled: account.charges_enabled,
      payouts_enabled: account.payouts_enabled,
      requirements: account.requirements,
      capabilities: account.capabilities,
      business_profile: account.business_profile,
    });
  } catch (error) {
    console.error('Error retrieving connected account:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode || 500 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Delete/deactivate connected account
export async function DELETE(request: NextRequest) {
  try {
    const { account_id } = await request.json();

    if (!account_id) {
      return NextResponse.json(
        { error: 'Account ID is required' },
        { status: 400 }
      );
    }

    // Delete the connected account
    const deletedAccount = await stripe.accounts.del(account_id);

    return NextResponse.json({
      deleted: deletedAccount.deleted,
      account_id: deletedAccount.id,
    });
  } catch (error) {
    console.error('Error deleting connected account:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json(
        { error: error.message },
        { status: error.statusCode || 500 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
