'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import StripeConnectOnboarding from '@/components/stripe/StripeConnectOnboarding';
import { toast } from 'sonner';

export default function TestStripePage() {
  const handleAccountConnected = (accountId: string) => {
    console.log('Test: Account connected:', accountId);
    toast.success(`Account connected: ${accountId}`);
  };

  const handleAccountDisconnected = () => {
    console.log('Test: Account disconnected');
    toast.success('Account disconnected');
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Stripe Connect Test Page
          </h1>
          <p className="text-gray-600">
            Test the Stripe Connect integration functionality
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Stripe Connect Integration Test</CardTitle>
            <CardDescription>
              This page allows you to test the Stripe Connect onboarding flow.
              Make sure you have configured your Stripe API keys in the environment variables.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h3 className="font-semibold text-yellow-800 mb-2">Environment Check</h3>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">Publishable Key:</span>
                    <span className={process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ? 'text-green-600' : 'text-red-600'}>
                      {process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ? '✓ Configured' : '✗ Missing'}
                    </span>
                  </div>
                  <div className="text-xs text-gray-600">
                    Secret key status is checked server-side
                  </div>
                </div>
              </div>

              <StripeConnectOnboarding
                onAccountConnected={handleAccountConnected}
                onAccountDisconnected={handleAccountDisconnected}
                shopEmail="<EMAIL>"
              />
            </div>
          </CardContent>
        </Card>

        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div>
                <h4 className="font-semibold mb-2">1. Environment Setup</h4>
                <p className="text-gray-600">
                  Ensure you have added your Stripe API keys to the <code className="bg-gray-100 px-1 rounded">.env.local</code> file:
                </p>
                <pre className="bg-gray-100 p-2 rounded mt-2 text-xs">
{`STRIPE_SECRET_KEY=sk_test_your_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here`}
                </pre>
              </div>

              <div>
                <h4 className="font-semibold mb-2">2. Test Account Creation</h4>
                <p className="text-gray-600">
                  Click "Create Stripe Account" to create a test connected account. 
                  Use a test email address that hasn't been used before.
                </p>
              </div>

              <div>
                <h4 className="font-semibold mb-2">3. Onboarding Flow</h4>
                <p className="text-gray-600">
                  After account creation, the embedded onboarding component should appear.
                  Complete the onboarding process with test information.
                </p>
              </div>

              <div>
                <h4 className="font-semibold mb-2">4. Account Management</h4>
                <p className="text-gray-600">
                  Once connected, you can view account details and test the disconnect functionality.
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h4 className="font-semibold text-blue-800 mb-1">Note</h4>
                <p className="text-blue-700 text-xs">
                  This is a test environment. No real money will be processed, and test accounts 
                  can be safely created and deleted.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
