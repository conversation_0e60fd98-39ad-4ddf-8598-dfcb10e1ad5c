'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  CreditCard,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Shield,
  Zap,
  Loader2,
  Building,
  Mail,
  Globe,
} from 'lucide-react';
import { toast } from 'sonner';
import { useStripeConnect } from '@/hooks/useStripeConnect';

interface ConnectedAccount {
  account_id: string;
  email: string;
  country: string;
  type: string;
  created: number;
  details_submitted: boolean;
  charges_enabled: boolean;
  payouts_enabled: boolean;
  requirements?: {
    currently_due: string[];
    eventually_due: string[];
    past_due: string[];
    pending_verification: string[];
  };
  capabilities?: {
    card_payments: string;
    transfers: string;
  };
  business_profile?: {
    name?: string;
    url?: string;
  };
}

interface StripeConnectOnboardingProps {
  onAccountConnected?: (accountId: string) => void;
  onAccountDisconnected?: () => void;
  initialAccountId?: string;
  shopEmail?: string;
}

export default function StripeConnectOnboarding({
  onAccountConnected,
  onAccountDisconnected,
  initialAccountId,
  shopEmail,
}: StripeConnectOnboardingProps) {
  const [connectedAccount, setConnectedAccount] = useState<ConnectedAccount | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [email, setEmail] = useState(shopEmail || '');
  const [showOnboarding, setShowOnboarding] = useState(false);

  // Initialize Stripe Connect instance
  const stripeConnectInstance = useStripeConnect(connectedAccount?.account_id || null);

  // Load existing account if provided
  useEffect(() => {
    if (initialAccountId) {
      loadAccountDetails(initialAccountId);
    }
  }, [initialAccountId]);

  const loadAccountDetails = async (accountId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/stripe/connect?account_id=${accountId}`);
      if (response.ok) {
        const result = await response.json();
        setConnectedAccount(result.data);
      } else {
        const error = await response.json();
        toast.error(`Failed to load account: ${error.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error loading account:', error);
      toast.error('Failed to load account details');
    } finally {
      setIsLoading(false);
    }
  };

  const createConnectedAccount = async () => {
    if (!email) {
      toast.error('Email is required');
      return;
    }

    setIsCreating(true);
    try {
      const response = await fetch('/api/stripe/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          country: 'TH',
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setConnectedAccount(result.data);
        setShowOnboarding(true);
        onAccountConnected?.(result.data.account_id);
        toast.success('Connected account created successfully!');
      } else {
        const error = await response.json();
        toast.error(`Failed to create account: ${error.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error creating account:', error);
      toast.error('Failed to create connected account');
    } finally {
      setIsCreating(false);
    }
  };

  const disconnectAccount = async () => {
    if (!connectedAccount) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/stripe/connect', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}), // Go backend will get shop/branch from auth context
      });

      if (response.ok) {
        setConnectedAccount(null);
        setShowOnboarding(false);
        onAccountDisconnected?.();
        toast.success('Account disconnected successfully!');
      } else {
        const error = await response.json();
        toast.error(`Failed to disconnect: ${error.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error disconnecting account:', error);
      toast.error('Failed to disconnect account');
    } finally {
      setIsLoading(false);
    }
  };

  const getAccountStatus = () => {
    if (!connectedAccount) return 'inactive';

    if (connectedAccount.charges_enabled && connectedAccount.payouts_enabled) {
      return 'active';
    } else if (connectedAccount.details_submitted) {
      return 'pending';
    } else {
      return 'restricted';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Active
          </Badge>
        );
      case 'pending':
        return (
          <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case 'restricted':
        return (
          <Badge className="bg-red-100 text-red-800 border-red-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Restricted
          </Badge>
        );
      default:
        return (
          <Badge className="bg-gray-100 text-gray-800 border-gray-200">
            <AlertCircle className="h-3 w-3 mr-1" />
            Not Connected
          </Badge>
        );
    }
  };

  if (isLoading) {
    return (
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-[#8a745c]" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="bg-[#f1edea] p-2 rounded-lg">
            <CreditCard className="h-5 w-5 text-[#8a745c]" />
          </div>
          <div>
            <CardTitle className="text-[#181510] text-lg">Stripe Connect</CardTitle>
            <CardDescription className="text-[#8a745c]">
              Accept international credit card payments via Stripe
            </CardDescription>
          </div>
          <div className="ml-auto">
            {getStatusBadge(getAccountStatus())}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {!connectedAccount ? (
          <div className="text-center py-8">
            <div className="bg-[#f8f6f3] rounded-lg p-6 mb-4">
              <Shield className="h-12 w-12 text-[#8a745c] mx-auto mb-3" />
              <h3 className="text-[#181510] font-semibold mb-2">Connect your Stripe account</h3>
              <p className="text-[#8a745c] text-sm mb-4">
                Create a Stripe Connect account to start accepting credit card payments from customers worldwide.
              </p>

              <div className="max-w-sm mx-auto mb-4">
                <Label htmlFor="email" className="text-[#181510] text-sm font-medium">
                  Business Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc]"
                  placeholder="Enter your business email"
                />
              </div>

              <Button
                onClick={createConnectedAccount}
                disabled={isCreating || !email}
                className="bg-[#635bff] hover:bg-[#5a52e8] text-white"
              >
                {isCreating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating Account...
                  </>
                ) : (
                  <>
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Create Stripe Account
                  </>
                )}
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Account Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-[#f8f6f3] rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Building className="h-4 w-4 text-[#8a745c]" />
                  <span className="text-[#181510] font-medium">Account ID</span>
                </div>
                <p className="text-[#8a745c] text-sm font-mono">{connectedAccount.account_id}</p>
              </div>

              <div className="p-4 bg-[#f8f6f3] rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Mail className="h-4 w-4 text-[#8a745c]" />
                  <span className="text-[#181510] font-medium">Email</span>
                </div>
                <p className="text-[#8a745c] text-sm">{connectedAccount.email}</p>
              </div>

              <div className="p-4 bg-[#f8f6f3] rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Globe className="h-4 w-4 text-[#8a745c]" />
                  <span className="text-[#181510] font-medium">Country</span>
                </div>
                <p className="text-[#8a745c] text-sm">{connectedAccount.country}</p>
              </div>

              <div className="p-4 bg-[#f8f6f3] rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="h-4 w-4 text-[#8a745c]" />
                  <span className="text-[#181510] font-medium">Account Type</span>
                </div>
                <p className="text-[#8a745c] text-sm capitalize">{connectedAccount.type}</p>
              </div>
            </div>

            {/* Capabilities */}
            <div className="p-4 bg-[#f8f6f3] rounded-lg">
              <h4 className="text-[#181510] font-medium mb-3">Capabilities</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <span className="text-[#8a745c] text-sm">Card Payments</span>
                  {connectedAccount.charges_enabled ? (
                    <Badge className="bg-green-100 text-green-800 border-green-200 text-xs">
                      Enabled
                    </Badge>
                  ) : (
                    <Badge className="bg-red-100 text-red-800 border-red-200 text-xs">
                      Disabled
                    </Badge>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-[#8a745c] text-sm">Payouts</span>
                  {connectedAccount.payouts_enabled ? (
                    <Badge className="bg-green-100 text-green-800 border-green-200 text-xs">
                      Enabled
                    </Badge>
                  ) : (
                    <Badge className="bg-red-100 text-red-800 border-red-200 text-xs">
                      Disabled
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {/* Stripe Connect Embedded Component */}
            {showOnboarding && stripeConnectInstance && (
              <div className="p-4 bg-[#f8f6f3] rounded-lg">
                <h4 className="text-[#181510] font-medium mb-3">Complete Account Setup</h4>
                <div id="stripe-connect-onboarding">
                  {/* Stripe Connect embedded component will be rendered here */}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-between items-center">
              <Button
                onClick={() => setShowOnboarding(!showOnboarding)}
                variant="outline"
                className="border-[#e5e1dc] text-[#181510]"
              >
                {showOnboarding ? 'Hide' : 'Show'} Account Dashboard
              </Button>

              <Button
                onClick={disconnectAccount}
                disabled={isLoading}
                variant="outline"
                className="border-red-200 text-red-600 hover:bg-red-50"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Disconnecting...
                  </>
                ) : (
                  'Disconnect Account'
                )}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
