# Stripe Connect Integration Guide

## 🎯 Overview

This guide shows how to integrate your existing Stripe Connect account (`acct_1RWGqRCvS6V6yiE9`) with the restaurant platform to process payments.

## 🏗️ Architecture

```
Customer Frontend → Go Backend → Stripe Connect Account → Payment Processing
                 ↓
              Database Storage (Orders, Payments, Account Info)
```

## 🚀 Quick Start

### 1. **Set Environment Variables**

Add to your `.env` file:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_51QtW5QCtNXkGk5bXQrIg3IdxRXAsB6a5XBKbpxQiTlgdU1GN3Pq5deYt0gtq6mX2GKOnjNMaQaq8nWHvhMTNv5sh00PnRaVKrg
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_PLATFORM_FEE=250
STRIPE_CURRENCY=thb
```

### 2. **Run Database Migration**

```bash
# Apply the Stripe Connect tables migration
psql -d your_database -f migrations/add_stripe_connect_tables.sql
```

### 3. **Register Your Existing Account**

```bash
# Make the script executable
chmod +x register_stripe_account.sh

# Register your existing Stripe Connect account
./register_stripe_account.sh YOUR_SHOP_ID YOUR_JWT_TOKEN
```

### 4. **Test Payment Processing**

```bash
# Make the test script executable
chmod +x test_stripe_payments.sh

# Run payment tests
./test_stripe_payments.sh YOUR_JWT_TOKEN
```

## 📋 API Endpoints

### Stripe Connect Management

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/v1/stripe/connect` | Create new Stripe Connect account |
| `POST` | `/api/v1/stripe/connect/register` | Register existing account |
| `GET` | `/api/v1/stripe/connect` | Get account details |
| `DELETE` | `/api/v1/stripe/connect` | Delete/deactivate account |
| `GET` | `/api/v1/stripe/connect/list` | List all accounts for shop |
| `POST` | `/api/v1/stripe/account_session` | Create account session |

### Payment Processing

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/v1/stripe/payment_intents` | Create payment intent |
| `GET` | `/api/v1/stripe/payment_intents/:id` | Get payment intent |
| `POST` | `/api/v1/stripe/payment_intents/:id/confirm` | Confirm payment |
| `POST` | `/api/v1/stripe/payment_intents/:id/cancel` | Cancel payment |

## 💳 Payment Flow

### 1. **Create Payment Intent**

```bash
curl -X POST http://localhost:8900/api/v1/stripe/payment_intents \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "amount": 10000,
    "currency": "thb",
    "description": "Restaurant order payment",
    "metadata": {
      "order_id": "order_123",
      "table_number": "5"
    }
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Payment intent created successfully",
  "data": {
    "payment_intent_id": "pi_1234567890",
    "client_secret": "pi_1234567890_secret_abc123",
    "amount": 10000,
    "currency": "thb",
    "status": "requires_payment_method",
    "connected_account_id": "acct_1RWGqRCvS6V6yiE9"
  }
}
```

### 2. **Frontend Payment Collection**

Use the `client_secret` in your frontend to collect payment:

```javascript
// Frontend (React/Next.js)
import { loadStripe } from '@stripe/stripe-js';

const stripe = await loadStripe('pk_test_your_publishable_key', {
  stripeAccount: 'acct_1RWGqRCvS6V6yiE9'
});

const { error } = await stripe.confirmCardPayment(clientSecret, {
  payment_method: {
    card: cardElement,
    billing_details: {
      name: 'Customer Name',
    },
  }
});
```

### 3. **Handle Payment Result**

```javascript
if (error) {
  // Payment failed
  console.error('Payment failed:', error.message);
} else {
  // Payment succeeded
  console.log('Payment succeeded!');
  // Update order status, redirect to success page, etc.
}
```

## 🔧 Integration Examples

### Restaurant Order Payment

```bash
# Create payment for a restaurant order
curl -X POST http://localhost:8900/api/v1/stripe/payment_intents \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "amount": 25000,
    "currency": "thb",
    "description": "Restaurant Order #12345",
    "order_id": "12345",
    "metadata": {
      "order_type": "dine_in",
      "table_number": "8",
      "customer_id": "cust_123",
      "items": "Pad Thai, Tom Yum, Mango Sticky Rice"
    }
  }'
```

### Takeaway Order Payment

```bash
# Create payment for takeaway order
curl -X POST http://localhost:8900/api/v1/stripe/payment_intents \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "amount": 15000,
    "currency": "thb",
    "description": "Takeaway Order #67890",
    "order_id": "67890",
    "metadata": {
      "order_type": "takeaway",
      "pickup_time": "2024-01-15T18:30:00Z",
      "customer_phone": "+***********"
    }
  }'
```

## 🎛️ Platform Fees

The system automatically calculates platform fees:

- **Default Fee**: 2.5% (250 basis points)
- **Configurable**: Set via `STRIPE_PLATFORM_FEE` environment variable
- **Per Transaction**: Deducted automatically from each payment

**Example:**
- Order Amount: ฿1,000 (100,000 satang)
- Platform Fee: ฿25 (2,500 satang)
- Restaurant Receives: ฿975 (97,500 satang)

## 🔔 Webhook Integration

### 1. **Set Up Webhook Endpoint**

```go
// Add to your routes
router.POST("/webhooks/stripe", stripeWebhookHandler.HandleWebhook)
```

### 2. **Handle Payment Events**

```go
func (h *StripeWebhookHandler) HandleWebhook(c *gin.Context) {
    payload, _ := ioutil.ReadAll(c.Request.Body)
    event, err := webhook.ConstructEvent(payload, c.GetHeader("Stripe-Signature"), webhookSecret)

    switch event.Type {
    case "payment_intent.succeeded":
        // Payment completed successfully
        // Update order status to "paid"
    case "payment_intent.payment_failed":
        // Payment failed
        // Update order status to "payment_failed"
    }
}
```

## 🧪 Testing

### Test Cards

| Card Number | Brand | Result |
|-------------|-------|--------|
| `****************` | Visa | Success |
| `****************` | Visa | Declined |
| `****************` | Visa | Insufficient funds |
| `****************` | Visa | Expired card |

### Test Scenarios

1. **Successful Payment**: Use `****************`
2. **Declined Payment**: Use `****************`
3. **3D Secure**: Use `****************`
4. **Insufficient Funds**: Use `****************`

## 🚨 Error Handling

### Common Errors

| Error Code | Description | Solution |
|------------|-------------|----------|
| `account_not_found` | Stripe account not registered | Register account first |
| `charges_not_enabled` | Account can't accept payments | Complete account verification |
| `insufficient_funds` | Customer card declined | Ask customer to try different card |
| `authentication_required` | 3D Secure required | Handle authentication flow |

### Error Response Format

```json
{
  "success": false,
  "message": "Failed to create payment intent",
  "details": "The connected account is not enabled for charges"
}
```

## 📊 Monitoring

### Key Metrics to Track

1. **Payment Success Rate**: `successful_payments / total_payment_attempts`
2. **Average Order Value**: `total_revenue / number_of_orders`
3. **Platform Fee Revenue**: `total_platform_fees_collected`
4. **Failed Payment Rate**: `failed_payments / total_payment_attempts`

### Logging

The system logs all payment activities:

```json
{
  "level": "info",
  "message": "Payment intent created successfully",
  "payment_intent_id": "pi_1234567890",
  "amount": 10000,
  "currency": "thb",
  "connected_account_id": "acct_1RWGqRCvS6V6yiE9",
  "shop_id": "shop_uuid",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 🔒 Security

### Best Practices

1. **API Keys**: Never expose secret keys in frontend code
2. **Webhook Verification**: Always verify webhook signatures
3. **HTTPS**: Use HTTPS in production
4. **Input Validation**: Validate all payment amounts and metadata
5. **Rate Limiting**: Implement rate limiting on payment endpoints

## 🚀 Production Deployment

### Checklist

- [ ] Switch to live Stripe API keys
- [ ] Configure production webhook endpoints
- [ ] Set up monitoring and alerting
- [ ] Test with real payment methods
- [ ] Configure proper error handling
- [ ] Set up backup and recovery procedures
- [ ] Review security configurations
- [ ] Test platform fee calculations
- [ ] Verify payout schedules

## 📞 Support

### Troubleshooting

1. **Check Logs**: Review application logs for errors
2. **Stripe Dashboard**: Check Stripe dashboard for payment details
3. **Test Environment**: Use test mode for debugging
4. **API Documentation**: Refer to Stripe API documentation

### Contact

- **Stripe Support**: [https://support.stripe.com](https://support.stripe.com)
- **Platform Issues**: Check application logs and error messages

---

## 🍽️ Order Creation with Stripe Connect

### Complete Order Flow

Your Stripe Connect account is now integrated with a complete order creation system:

#### 1. **Order Creation Endpoints**

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/v1/orders/create-with-payment` | Create order with payment |
| `POST` | `/api/v1/orders/quick-order` | Create quick order |
| `POST` | `/api/v1/orders/table/:table_id` | Create table-specific order |
| `POST` | `/api/v1/orders/:order_id/confirm-payment` | Confirm payment |
| `POST` | `/api/v1/orders/:order_id/cancel-payment` | Cancel payment |
| `GET` | `/api/v1/orders/:order_id/payment-status` | Get payment status |

#### 2. **Complete Order Creation Example**

```bash
# Create order with Stripe Connect payment
curl -X POST http://localhost:8900/api/v1/orders/create-with-payment \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "branch_id": "your-branch-uuid",
    "table_id": "your-table-uuid",
    "customer_name": "John Doe",
    "customer_phone": "+***********",
    "customer_email": "<EMAIL>",
    "order_type": "dine_in",
    "items": [
      {
        "menu_item_id": "menu-item-uuid",
        "quantity": 2,
        "unit_price": 150.0,
        "customizations": [
          {
            "name": "Spice Level",
            "value": "Medium",
            "price": 0
          }
        ],
        "special_requests": "No onions"
      }
    ],
    "notes": "Birthday celebration",
    "payment_method": "stripe_connect",
    "metadata": {
      "occasion": "birthday",
      "source": "mobile_app"
    }
  }'
```

**Response:**
```json
{
  "success": true,
  "message": "Order created successfully",
  "data": {
    "order": {
      "id": "order-uuid",
      "order_number": "ORD-********-143022-1234",
      "customer_name": "John Doe",
      "total": 351.0,
      "status": "pending",
      "payment_status": "pending"
    },
    "payment_intent": {
      "payment_intent_id": "pi_1234567890",
      "client_secret": "pi_1234567890_secret_abc123",
      "amount": 35100,
      "currency": "thb",
      "status": "requires_payment_method"
    },
    "connected_account_id": "acct_1RWGqRCvS6V6yiE9",
    "requires_payment": true,
    "estimated_time": 25
  }
}
```

#### 3. **Frontend Integration**

```typescript
// Create order with payment
const orderResult = await createOrder(cartItems, total, {
  customerName: "John Doe",
  customerPhone: "+***********",
  customerEmail: "<EMAIL>",
  tableId: "table-uuid",
  orderType: "dine_in",
  paymentMethod: "stripe_connect",
  notes: "Special occasion",
  branchId: "branch-uuid"
});

// Handle payment if required
if (orderResult.requiresPayment && orderResult.paymentIntent) {
  // Use StripeConnectPayment component
  <StripeConnectPayment
    paymentIntent={orderResult.paymentIntent}
    orderDetails={{
      orderId: orderResult.orderId,
      customerName: "John Doe",
      total: total
    }}
    onPaymentSuccess={(paymentIntentId) => {
      console.log('Payment successful:', paymentIntentId);
      // Redirect to order confirmation
    }}
    onPaymentError={(error) => {
      console.error('Payment failed:', error);
      // Handle payment failure
    }}
  />
}
```

#### 4. **Test Order Creation**

```bash
# Make the test script executable
chmod +x test_order_creation.sh

# Test order creation with your account
./test_order_creation.sh YOUR_JWT_TOKEN YOUR_BRANCH_ID
```

### Order Types Supported

1. **Dine-in Orders**: Table service with payment integration
2. **Takeaway Orders**: Quick pickup with cash or card payment
3. **Delivery Orders**: With address and delivery tracking
4. **Quick Orders**: Simplified ordering for regular customers
5. **Table Orders**: QR code ordering for specific tables

### Payment Methods

- **Stripe Connect**: Full card payment processing
- **Cash**: Pay on delivery/pickup
- **Card**: Direct card payments through your account

### Order Features

- ✅ **Automatic Calculations**: Subtotal, tax, service charges
- ✅ **Item Customizations**: Spice levels, sizes, extras
- ✅ **Special Requests**: Customer notes and preferences
- ✅ **Estimated Time**: Dynamic preparation time calculation
- ✅ **Order Tracking**: Real-time status updates
- ✅ **Payment Integration**: Seamless Stripe Connect processing
- ✅ **Metadata Support**: Custom order information
- ✅ **Multi-branch Support**: Branch-specific ordering

🎉 **Your Stripe Connect account (`acct_1RWGqRCvS6V6yiE9`) is now ready to process orders and payments!**
