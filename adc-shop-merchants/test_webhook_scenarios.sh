#!/bin/bash

# Webhook Testing Script for Incomplete Order Scenarios
# This script simulates various webhook events to test order completion flows

set -e

# Configuration
BASE_URL="http://localhost:8080"
WEBHOOK_URL="$BASE_URL/api/v1/payments/webhook"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to simulate webhook with invalid signature
test_invalid_webhook_signature() {
    log_info "Test 1: Invalid webhook signature"
    
    local payload='{"type": "payment_intent.succeeded", "data": {"object": {"id": "pi_test"}}}'
    local response=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -H "Stripe-Signature: invalid_signature" \
        -d "$payload")
    
    if [ "$response" = "400" ]; then
        log_success "✓ Correctly rejected invalid signature (HTTP 400)"
    else
        log_error "✗ Expected HTTP 400, got $response"
    fi
    echo
}

# Function to simulate webhook with missing signature
test_missing_webhook_signature() {
    log_info "Test 2: Missing webhook signature"
    
    local payload='{"type": "payment_intent.succeeded", "data": {"object": {"id": "pi_test"}}}'
    local response=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -d "$payload")
    
    if [ "$response" = "400" ]; then
        log_success "✓ Correctly rejected missing signature (HTTP 400)"
    else
        log_error "✗ Expected HTTP 400, got $response"
    fi
    echo
}

# Function to simulate malformed webhook payload
test_malformed_webhook_payload() {
    log_info "Test 3: Malformed webhook payload"
    
    local payload='{"invalid": "json", "missing_closing_brace": true'
    local response=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -H "Stripe-Signature: t=1234567890,v1=test_signature" \
        -d "$payload")
    
    if [ "$response" = "400" ]; then
        log_success "✓ Correctly handled malformed payload (HTTP 400)"
    else
        log_warning "⚠ Got HTTP $response for malformed payload"
    fi
    echo
}

# Function to test webhook endpoint availability
test_webhook_endpoint_availability() {
    log_info "Test 4: Webhook endpoint availability"
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" -X GET "$WEBHOOK_URL")
    
    if [ "$response" = "405" ] || [ "$response" = "404" ]; then
        log_success "✓ Webhook endpoint exists (GET not allowed: HTTP $response)"
    else
        log_warning "⚠ Unexpected response for GET request: HTTP $response"
    fi
    echo
}

# Function to simulate payment_intent.succeeded webhook
simulate_payment_succeeded() {
    log_info "Simulating payment_intent.succeeded webhook"
    
    if command -v stripe &> /dev/null; then
        log_info "Using Stripe CLI to trigger webhook..."
        stripe trigger payment_intent.succeeded
        log_success "✓ payment_intent.succeeded webhook triggered"
    else
        log_warning "Stripe CLI not found. Install with: brew install stripe/stripe-cli/stripe"
        log_info "Manual webhook simulation would require valid signature"
    fi
    echo
}

# Function to simulate payment_intent.payment_failed webhook
simulate_payment_failed() {
    log_info "Simulating payment_intent.payment_failed webhook"
    
    if command -v stripe &> /dev/null; then
        log_info "Using Stripe CLI to trigger webhook..."
        stripe trigger payment_intent.payment_failed
        log_success "✓ payment_intent.payment_failed webhook triggered"
    else
        log_warning "Stripe CLI not found. Install with: brew install stripe/stripe-cli/stripe"
    fi
    echo
}

# Function to simulate payment_intent.canceled webhook
simulate_payment_canceled() {
    log_info "Simulating payment_intent.canceled webhook"
    
    if command -v stripe &> /dev/null; then
        log_info "Using Stripe CLI to trigger webhook..."
        stripe trigger payment_intent.canceled
        log_success "✓ payment_intent.canceled webhook triggered"
    else
        log_warning "Stripe CLI not found"
    fi
    echo
}

# Function to test webhook with unknown event type
test_unknown_event_type() {
    log_info "Test 5: Unknown webhook event type"
    
    # This would require a valid signature, so we'll just note it
    log_info "Testing unknown event types requires valid Stripe signature"
    log_info "Use Stripe CLI: stripe trigger <unknown_event_type>"
    echo
}

# Function to monitor webhook logs
monitor_webhook_logs() {
    log_info "Webhook Monitoring Instructions"
    echo "================================"
    echo
    echo "1. Start webhook forwarding:"
    echo "   stripe listen --forward-to $WEBHOOK_URL"
    echo
    echo "2. Monitor backend logs:"
    echo "   tail -f /path/to/backend/logs/app.log | grep -E '(webhook|payment|stripe)'"
    echo
    echo "3. Test with real Stripe events:"
    echo "   stripe trigger payment_intent.succeeded"
    echo "   stripe trigger payment_intent.payment_failed"
    echo "   stripe trigger payment_intent.canceled"
    echo
}

# Function to check Stripe CLI availability
check_stripe_cli() {
    if command -v stripe &> /dev/null; then
        local version=$(stripe --version)
        log_success "Stripe CLI available: $version"
        
        # Check if logged in
        if stripe config --list &> /dev/null; then
            log_success "Stripe CLI is configured"
        else
            log_warning "Stripe CLI not logged in. Run: stripe login"
        fi
    else
        log_warning "Stripe CLI not found"
        log_info "Install with: brew install stripe/stripe-cli/stripe"
        log_info "Then run: stripe login"
    fi
    echo
}

# Main execution
main() {
    log_info "Starting Webhook Testing for Incomplete Orders"
    log_info "=============================================="
    echo
    
    # Check Stripe CLI
    check_stripe_cli
    
    # Run basic webhook tests
    test_invalid_webhook_signature
    test_missing_webhook_signature
    test_malformed_webhook_payload
    test_webhook_endpoint_availability
    test_unknown_event_type
    
    # Simulate webhook events if Stripe CLI is available
    if command -v stripe &> /dev/null; then
        log_info "Simulating Stripe webhook events..."
        echo
        simulate_payment_succeeded
        simulate_payment_failed
        simulate_payment_canceled
    fi
    
    # Provide monitoring instructions
    monitor_webhook_logs
    
    log_info "=============================================="
    log_success "Webhook testing completed!"
    echo
    log_info "Recommendations:"
    echo "1. Set up Stripe CLI webhook forwarding for real-time testing"
    echo "2. Monitor backend logs during webhook processing"
    echo "3. Test order status updates after webhook events"
    echo "4. Verify database consistency after webhook processing"
}

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        exit 1
    fi
}

# Run the tests
check_dependencies
main "$@"
