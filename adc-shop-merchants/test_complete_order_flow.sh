#!/bin/bash

# Complete Order Flow Testing Script
# Tests the entire order lifecycle from creation to completion

set -e

# Configuration
MERCHANT_BACKEND="http://localhost:8080"
CUSTOMER_BACKEND="http://localhost:8900"
STRIPE_ACCOUNT_ID="acct_1RWGqRCvS6V6yiE9"

# Test Data
SHOP_SLUG="scandine-restaurant"
BRANCH_SLUG="main-branch"
BRANCH_ID="52e05798-2c76-4fe6-9c9c-72a83b7e203e"
TABLE_ID="550e8400-e29b-41d4-a716-************"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${PURPLE}[STEP]${NC} $1"; }

# API call function
api_call() {
    local method=$1
    local url=$2
    local data=$3
    local token=$4
    local description=$5
    
    log_info "API Call: $method $url"
    [ -n "$description" ] && log_info "Description: $description"
    
    local curl_cmd="curl -s -w '\n%{http_code}'"
    
    if [ -n "$token" ]; then
        curl_cmd="$curl_cmd -H 'Authorization: Bearer $token'"
    fi
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        curl_cmd="$curl_cmd -X POST -H 'Content-Type: application/json' -d '$data'"
    elif [ "$method" = "PUT" ] && [ -n "$data" ]; then
        curl_cmd="$curl_cmd -X PUT -H 'Content-Type: application/json' -d '$data'"
    elif [ "$method" = "DELETE" ]; then
        curl_cmd="$curl_cmd -X DELETE"
    fi
    
    curl_cmd="$curl_cmd '$url'"
    
    local response
    response=$(eval $curl_cmd)
    local http_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | head -n -1)
    
    echo "HTTP Status: $http_code"
    echo "Response: $body" | jq '.' 2>/dev/null || echo "Response: $body"
    echo ""
    
    return $http_code
}

# Get JWT token
get_auth_token() {
    log_step "Step 1: Getting Authentication Token"
    
    local login_data='{
        "email": "<EMAIL>",
        "password": "password"
    }'
    
    local response=$(curl -s -X POST "$MERCHANT_BACKEND/api/v1/auth/login" \
        -H "Content-Type: application/json" \
        -d "$login_data")
    
    local token=$(echo "$response" | jq -r '.token // empty')
    
    if [ -n "$token" ] && [ "$token" != "null" ]; then
        log_success "Authentication successful"
        echo "$token"
    else
        log_error "Authentication failed"
        echo "$response" | jq '.' 2>/dev/null || echo "$response"
        exit 1
    fi
}

# Test 1: Create Order with Payment Intent (Customer Backend)
test_create_order_customer() {
    log_step "Step 2: Creating Order via Customer Backend"
    
    local order_data='{
        "branch_id": "'$BRANCH_ID'",
        "table_id": "'$TABLE_ID'",
        "customer_name": "John Doe",
        "customer_phone": "+66812345678",
        "customer_email": "<EMAIL>",
        "items": [
            {
                "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
                "quantity": 2,
                "unit_price": 150.0,
                "special_requests": "Medium spice"
            },
            {
                "menu_item_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                "quantity": 1,
                "unit_price": 200.0,
                "special_requests": "No onions"
            }
        ],
        "special_requests": "Table by the window please"
    }'
    
    api_call "POST" "$CUSTOMER_BACKEND/orders/table" "$order_data" "" "Create table order"
    local status=$?
    
    if [ $status -eq 201 ]; then
        log_success "Order created successfully via customer backend"
        return 0
    else
        log_error "Failed to create order via customer backend"
        return 1
    fi
}

# Test 2: Create Order with Stripe Connect (Merchant Backend)
test_create_order_merchant() {
    log_step "Step 3: Creating Order with Stripe Connect via Merchant Backend"
    
    local token=$1
    
    local order_data='{
        "branch_id": "'$BRANCH_ID'",
        "table_id": "'$TABLE_ID'",
        "customer_name": "Jane Smith",
        "customer_phone": "+66887654321",
        "customer_email": "<EMAIL>",
        "order_type": "dine_in",
        "items": [
            {
                "menu_item_id": "d5c6671b-21bf-4fae-8dc0-02564523fbe2",
                "quantity": 1,
                "unit_price": 150.0,
                "customizations": [
                    {
                        "name": "Spice Level",
                        "value": "Hot",
                        "price": 0
                    }
                ],
                "special_requests": "Extra sauce"
            }
        ],
        "notes": "VIP customer",
        "payment_method": "stripe_connect",
        "connected_account_id": "'$STRIPE_ACCOUNT_ID'",
        "metadata": {
            "source": "test_script",
            "test_type": "complete_flow"
        }
    }'
    
    local response=$(api_call "POST" "$MERCHANT_BACKEND/api/v1/orders/create-with-payment" "$order_data" "$token" "Create order with Stripe Connect")
    local status=$?
    
    if [ $status -eq 200 ] || [ $status -eq 201 ]; then
        log_success "Order with payment created successfully"
        
        # Extract order details
        local order_id=$(echo "$response" | head -n -1 | jq -r '.data.order.id // empty')
        local payment_intent_id=$(echo "$response" | head -n -1 | jq -r '.data.payment_intent.payment_intent_id // empty')
        
        if [ -n "$order_id" ]; then
            echo "$order_id" > /tmp/test_order_id
            log_info "Order ID saved: $order_id"
        fi
        
        if [ -n "$payment_intent_id" ]; then
            echo "$payment_intent_id" > /tmp/test_payment_intent_id
            log_info "Payment Intent ID saved: $payment_intent_id"
        fi
        
        return 0
    else
        log_error "Failed to create order with payment"
        return 1
    fi
}

# Test 3: Get Order Status
test_get_order_status() {
    log_step "Step 4: Checking Order Status"
    
    local token=$1
    local order_id=""
    
    if [ -f /tmp/test_order_id ]; then
        order_id=$(cat /tmp/test_order_id)
    else
        log_warning "No order ID found, skipping status check"
        return 0
    fi
    
    api_call "GET" "$MERCHANT_BACKEND/api/v1/shops/slug/$SHOP_SLUG/branches/slug/$BRANCH_SLUG/orders/$order_id" "" "$token" "Get order status"
    local status=$?
    
    if [ $status -eq 200 ]; then
        log_success "Order status retrieved successfully"
        return 0
    else
        log_error "Failed to get order status"
        return 1
    fi
}

# Test 4: Update Order Status
test_update_order_status() {
    log_step "Step 5: Updating Order Status to 'preparing'"
    
    local token=$1
    local order_id=""
    
    if [ -f /tmp/test_order_id ]; then
        order_id=$(cat /tmp/test_order_id)
    else
        log_warning "No order ID found, skipping status update"
        return 0
    fi
    
    local update_data='{
        "status": "preparing",
        "estimated_time": 25,
        "notes": "Order is being prepared by the kitchen"
    }'
    
    api_call "PUT" "$MERCHANT_BACKEND/api/v1/shops/slug/$SHOP_SLUG/branches/slug/$BRANCH_SLUG/orders/$order_id/status" "$update_data" "$token" "Update order status to preparing"
    local status=$?
    
    if [ $status -eq 200 ]; then
        log_success "Order status updated to 'preparing'"
        return 0
    else
        log_error "Failed to update order status"
        return 1
    fi
}

# Test 5: Complete Order
test_complete_order() {
    log_step "Step 6: Completing Order"
    
    local token=$1
    local order_id=""
    
    if [ -f /tmp/test_order_id ]; then
        order_id=$(cat /tmp/test_order_id)
    else
        log_warning "No order ID found, skipping order completion"
        return 0
    fi
    
    local complete_data='{
        "status": "completed",
        "notes": "Order completed and served to customer"
    }'
    
    api_call "PUT" "$MERCHANT_BACKEND/api/v1/shops/slug/$SHOP_SLUG/branches/slug/$BRANCH_SLUG/orders/$order_id/status" "$complete_data" "$token" "Complete order"
    local status=$?
    
    if [ $status -eq 200 ]; then
        log_success "Order completed successfully"
        return 0
    else
        log_error "Failed to complete order"
        return 1
    fi
}

# Test 6: Payment Status Check
test_payment_status() {
    log_step "Step 7: Checking Payment Status"
    
    local token=$1
    local payment_intent_id=""
    
    if [ -f /tmp/test_payment_intent_id ]; then
        payment_intent_id=$(cat /tmp/test_payment_intent_id)
    else
        log_warning "No payment intent ID found, skipping payment status check"
        return 0
    fi
    
    api_call "GET" "$CUSTOMER_BACKEND/payments/$payment_intent_id/status" "" "" "Check payment status"
    local status=$?
    
    if [ $status -eq 200 ]; then
        log_success "Payment status retrieved successfully"
        return 0
    else
        log_warning "Payment status check failed (expected for test environment)"
        return 0
    fi
}

# Main test execution
main() {
    echo "🍽️  Complete Order Flow Testing"
    echo "================================"
    echo "Merchant Backend: $MERCHANT_BACKEND"
    echo "Customer Backend: $CUSTOMER_BACKEND"
    echo "Stripe Account: $STRIPE_ACCOUNT_ID"
    echo ""
    
    # Get authentication token
    local token
    token=$(get_auth_token)
    
    # Run tests
    local failed_tests=0
    
    test_create_order_customer || failed_tests=$((failed_tests + 1))
    test_create_order_merchant "$token" || failed_tests=$((failed_tests + 1))
    test_get_order_status "$token" || failed_tests=$((failed_tests + 1))
    test_update_order_status "$token" || failed_tests=$((failed_tests + 1))
    test_complete_order "$token" || failed_tests=$((failed_tests + 1))
    test_payment_status "$token" || failed_tests=$((failed_tests + 1))
    
    # Cleanup
    rm -f /tmp/test_order_id /tmp/test_payment_intent_id
    
    # Summary
    echo ""
    echo "📊 Test Summary"
    echo "==============="
    if [ $failed_tests -eq 0 ]; then
        log_success "All tests passed! Complete order flow is working."
    else
        log_error "$failed_tests test(s) failed."
    fi
    
    echo ""
    echo "🎯 Next Steps:"
    echo "1. Test payment confirmation with Stripe test cards"
    echo "2. Test order cancellation flow"
    echo "3. Test concurrent order processing"
    echo "4. Test webhook integration"
}

# Check dependencies
check_dependencies() {
    local missing=()
    
    command -v curl >/dev/null || missing+=("curl")
    command -v jq >/dev/null || missing+=("jq")
    
    if [ ${#missing[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing[*]}"
        exit 1
    fi
}

# Run tests
check_dependencies
main "$@"
