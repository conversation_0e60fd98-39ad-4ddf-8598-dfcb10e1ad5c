# ADC Shop Root Makefile
# This Makefile manages both customer and merchant services

# Variables
CUSTOMER_DIR = adc-shop-customer
MERCHANT_DIR = adc-shop-merchants

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m

# Default target
.PHONY: help
help:
	@echo "$(BLUE)ADC Shop Services$(NC)"
	@echo ""
	@echo "$(GREEN)Available commands:$(NC)"
	@echo "  $(YELLOW)backend$(NC)           - Start customer backend on port 8900"
	@echo "  $(YELLOW)frontend$(NC)          - Start customer frontend on port 3000"
	@echo "  $(YELLOW)dev$(NC)               - Start both backend and frontend"
	@echo "  $(YELLOW)merchant$(NC)          - Start merchant services"
	@echo "  $(YELLOW)customer$(NC)          - Start customer services"
	@echo "  $(YELLOW)stop$(NC)              - Stop all running services"
	@echo "  $(YELLOW)clean$(NC)             - Clean all build artifacts"
	@echo "  $(YELLOW)install$(NC)           - Install all dependencies"
	@echo "  $(YELLOW)test$(NC)              - Run all tests"
	@echo "  $(YELLOW)health$(NC)            - Check service health"
	@echo ""

# Customer service commands
.PHONY: backend
backend:
	@echo "$(GREEN)🚀 Starting customer backend on port 8900...$(NC)"
	@cd $(CUSTOMER_DIR) && $(MAKE) dev-backend

.PHONY: frontend
frontend:
	@echo "$(GREEN)🎨 Starting customer frontend on port 3000...$(NC)"
	@cd $(CUSTOMER_DIR) && $(MAKE) dev-frontend

.PHONY: dev
dev:
	@echo "$(GREEN)🚀 Starting both backend and frontend...$(NC)"
	@cd $(CUSTOMER_DIR) && $(MAKE) dev

# Service-specific commands
.PHONY: customer
customer:
	@echo "$(GREEN)🏪 Starting customer services...$(NC)"
	@cd $(CUSTOMER_DIR) && $(MAKE) dev

.PHONY: merchant
merchant:
	@echo "$(GREEN)🏬 Starting merchant services...$(NC)"
	@cd $(MERCHANT_DIR) && $(MAKE) dev

# Management commands
.PHONY: stop
stop:
	@echo "$(YELLOW)🛑 Stopping all services...$(NC)"
	@cd $(CUSTOMER_DIR) && $(MAKE) stop 2>/dev/null || true
	@cd $(MERCHANT_DIR) && $(MAKE) stop 2>/dev/null || true
	@echo "$(GREEN)✅ All services stopped$(NC)"

.PHONY: clean
clean:
	@echo "$(YELLOW)🧹 Cleaning all build artifacts...$(NC)"
	@cd $(CUSTOMER_DIR) && $(MAKE) clean 2>/dev/null || true
	@cd $(MERCHANT_DIR) && $(MAKE) clean 2>/dev/null || true
	@echo "$(GREEN)✅ Cleanup completed$(NC)"

.PHONY: install
install:
	@echo "$(GREEN)📦 Installing all dependencies...$(NC)"
	@cd $(CUSTOMER_DIR) && $(MAKE) install
	@cd $(MERCHANT_DIR) && $(MAKE) install
	@echo "$(GREEN)✅ All dependencies installed$(NC)"

.PHONY: test
test:
	@echo "$(GREEN)🧪 Running all tests...$(NC)"
	@cd $(CUSTOMER_DIR) && $(MAKE) test
	@cd $(MERCHANT_DIR) && $(MAKE) test
	@echo "$(GREEN)✅ All tests completed$(NC)"

.PHONY: health
health:
	@echo "$(GREEN)🏥 Checking service health...$(NC)"
	@echo "Customer services:"
	@cd $(CUSTOMER_DIR) && $(MAKE) health 2>/dev/null || echo "  ❌ Customer services not responding"
	@echo "Merchant services:"
	@cd $(MERCHANT_DIR) && $(MAKE) health 2>/dev/null || echo "  ❌ Merchant services not responding"

# Quick setup
.PHONY: setup
setup:
	@echo "$(GREEN)🔧 Setting up all services...$(NC)"
	@cd $(CUSTOMER_DIR) && $(MAKE) setup
	@cd $(MERCHANT_DIR) && $(MAKE) setup 2>/dev/null || true
	@echo "$(GREEN)✅ Setup completed$(NC)"

# Show service URLs
.PHONY: urls
urls:
	@echo "$(BLUE)🌐 Service URLs:$(NC)"
	@echo "  Customer Frontend: http://localhost:3000"
	@echo "  Customer Backend: http://localhost:8900"
	@echo "  Customer Health: http://localhost:8900/health"
	@echo "  Merchant Services: http://localhost:3001 (if running)"
