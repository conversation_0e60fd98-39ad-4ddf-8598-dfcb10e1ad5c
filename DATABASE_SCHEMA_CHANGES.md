# Database Schema Changes for Google Maps Integration

## ✅ Schema Updates Complete

This document outlines all the database schema changes made to support Google Maps integration across both merchant and customer backends.

## 🎯 **Overview**

Added comprehensive location support to enable:
- **Google Maps integration** with restaurant markers
- **Location-based search** and filtering
- **Distance calculations** and nearby restaurant discovery
- **Geocoding support** for address validation
- **Spatial indexing** for efficient location queries

## 📊 **Schema Changes**

### **New Fields Added**

Both `shops` and `shop_branches` tables now include:

| Field | Type | Description |
|-------|------|-------------|
| `latitude` | `DECIMAL(10,8)` | Latitude coordinate (-90 to 90) |
| `longitude` | `DECIMAL(11,8)` | Longitude coordinate (-180 to 180) |
| `location_accuracy` | `INTEGER` | Accuracy in meters (0 = unknown) |
| `geocoded_at` | `TIMESTAMP WITH TIME ZONE` | When address was last geocoded |
| `place_id` | `VARCHAR(255)` | Google Places API place ID |
| `formatted_address` | `TEXT` | Google-formatted address string |
| `location_type` | `VARCHAR(50)` | Source: manual, geocoded, gps, imported |

### **Indexes Created**

#### **Spatial Indexes (GIST)**
- `idx_shops_location` - Efficient spatial queries on shops
- `idx_shop_branches_location` - Efficient spatial queries on branches

#### **Coordinate Indexes (B-tree)**
- `idx_shops_lat_lng` - Fast coordinate-based lookups
- `idx_shop_branches_lat_lng` - Fast coordinate-based lookups

#### **Place ID Indexes**
- `idx_shops_place_id` - Google Places API integration
- `idx_shop_branches_place_id` - Google Places API integration

### **Database Functions**

#### **Distance Calculation**
```sql
calculate_distance(lat1, lng1, lat2, lng2) RETURNS DECIMAL(10,2)
```
- Uses Haversine formula for accurate distance calculation
- Returns distance in kilometers
- Optimized for performance

#### **Nearby Search (Merchant Backend)**
```sql
find_nearby_shops(search_lat, search_lng, radius_km, limit_count)
```
- Finds shops within specified radius
- Returns shop details with distance
- Sorted by distance (closest first)

#### **Nearby Search (Customer Backend)**
```sql
find_nearby_shops_customer(search_lat, search_lng, radius_km, limit_count)
```
- Customer-focused shop search
- Includes rating, cuisine type, price range
- Filters for active shops only

## 🏗️ **Model Updates**

### **Address Model (Both Backends)**

Updated `Address` struct with location fields:

```go
type Address struct {
    Street           string     `json:"street" gorm:"type:varchar(255)"`
    City             string     `json:"city" gorm:"type:varchar(100)"`
    State            string     `json:"state" gorm:"type:varchar(100)"`
    ZipCode          string     `json:"zip_code" gorm:"type:varchar(20)"`
    Country          string     `json:"country" gorm:"type:varchar(100)"`
    
    // New location fields
    Latitude         *float64   `json:"latitude" gorm:"type:decimal(10,8)"`
    Longitude        *float64   `json:"longitude" gorm:"type:decimal(11,8)"`
    LocationAccuracy *int       `json:"location_accuracy" gorm:"default:0"`
    GeocodedAt       *time.Time `json:"geocoded_at"`
    PlaceID          string     `json:"place_id" gorm:"type:varchar(255)"`
    FormattedAddress string     `json:"formatted_address" gorm:"type:text"`
    LocationType     string     `json:"location_type" gorm:"type:varchar(50);default:'manual'"`
}
```

## 🔧 **Services Added**

### **LocationService**

Created comprehensive location service for both backends:

#### **Features:**
- **Geocoding** - Convert addresses to coordinates using Google Maps API
- **Distance calculation** - Haversine formula implementation
- **Coordinate validation** - Ensure valid lat/lng values
- **Address formatting** - Build complete address strings
- **Location updates** - Update shop/branch location data

#### **Methods:**
- `GeocodeAddress(request)` - Geocode address to coordinates
- `UpdateShopLocation(shop, geocodeResp)` - Update shop location
- `UpdateShopBranchLocation(branch, geocodeResp)` - Update branch location
- `CalculateDistance(lat1, lng1, lat2, lng2)` - Calculate distance
- `ValidateCoordinates(lat, lng)` - Validate coordinates
- `FormatCoordinates(lat, lng)` - Format for display

## 📁 **Files Created/Modified**

### **Migrations**
- `adc-shop-merchants/restaurant-backend/migrations/008_add_location_fields.sql`
- `adc-shop-customer/customer-backend/migrations/001_add_location_fields.sql`

### **Models**
- `adc-shop-merchants/restaurant-backend/internal/models/base.go` ✅ Updated
- `adc-shop-customer/customer-backend/internal/models/base.go` ✅ Updated

### **Services**
- `adc-shop-merchants/restaurant-backend/internal/services/location_service.go` ✅ New
- `adc-shop-customer/customer-backend/internal/services/location_service.go` ✅ New

### **Scripts**
- `adc-shop-merchants/restaurant-backend/scripts/run_location_migration.sh` ✅ New
- `adc-shop-customer/customer-backend/scripts/run_location_migration.sh` ✅ New

## 🚀 **Running Migrations**

### **Merchant Backend**
```bash
cd adc-shop-merchants/restaurant-backend
./scripts/run_location_migration.sh
```

### **Customer Backend**
```bash
cd adc-shop-customer/customer-backend
./scripts/run_location_migration.sh
```

## 📍 **Sample Data**

The migrations automatically add sample location data:
- **Bangkok center**: 13.7563, 100.5018
- **Silom area**: 13.7440, 100.5255
- **Random Bangkok locations** for other shops

## 🎯 **Integration Points**

### **Frontend Integration**
- Google Maps now receives real coordinate data
- Location-based filtering available
- Distance calculations for nearby restaurants

### **API Enhancements**
- Shop endpoints now include location data
- Distance-based search parameters
- Geocoding endpoints for address validation

### **Performance Optimizations**
- Spatial indexes for fast location queries
- Efficient distance calculations
- Optimized nearby search functions

## 🔮 **Future Enhancements**

### **Planned Features**
- **Automatic geocoding** for new shops
- **Real-time location updates**
- **Advanced spatial queries**
- **Location-based analytics**
- **Delivery radius calculations**

### **API Extensions**
- **Geocoding endpoints** for address validation
- **Nearby search API** with filters
- **Distance matrix calculations**
- **Location-based recommendations**

## ✅ **Verification**

After running migrations, verify:

1. **Tables updated** with new location fields
2. **Indexes created** for spatial queries
3. **Functions available** for distance calculations
4. **Sample data populated** for existing shops
5. **Google Maps integration** working with real coordinates

The schema is now fully prepared for comprehensive Google Maps integration! 🎉
