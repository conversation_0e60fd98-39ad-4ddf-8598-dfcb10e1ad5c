import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials"
import GoogleProvider from "next-auth/providers/google"
import GitHubProvider from "next-auth/providers/github"

// Extend the built-in session types
declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      name?: string
      role: string
    }
  }

  interface User {
    id: string
    email: string
    name?: string
    role: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: string
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    // OAuth Providers
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    }),
    // Credentials Provider for email/password login
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required")
        }

        try {
          // Call customer backend for authentication
          const response = await fetch(`${process.env.NEXT_PUBLIC_CUSTOMER_API_URL}/api/v1/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: credentials.email,
              password: credentials.password,
            }),
          })

          if (!response.ok) {
            throw new Error("Invalid credentials")
          }

          const data = await response.json()

          if (!data.success || !data.data?.user) {
            throw new Error("Invalid credentials")
          }

          return {
            id: data.data.user.id,
            email: data.data.user.email,
            name: data.data.user.name || data.data.user.email,
            role: data.data.user.role || 'customer',
          }
        } catch (error) {
          console.error('Auth error:', error)
          throw error
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
    secret: process.env.NEXTAUTH_SECRET,
  },
  callbacks: {
    async signIn({ account }) {
      // Allow all OAuth sign-ins
      if (account?.provider !== "credentials") {
        return true
      }

      // For credentials, the authorize function handles validation
      return true
    },
    async jwt({ token, user, account, trigger, session }) {
      // Initial sign in
      if (user) {
        token.role = user.role || 'customer'

        // For OAuth providers, create/update user in customer backend
        if (account?.provider !== "credentials") {
          try {
            // Parse name into first and last name
            const nameParts = user.name?.split(' ') || []
            const firstName = nameParts[0] || ''
            const lastName = nameParts.slice(1).join(' ') || ''

            const response = await fetch(`${process.env.NEXT_PUBLIC_CUSTOMER_API_URL}/api/v1/auth/oauth-user`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                email: user.email,
                first_name: firstName,
                last_name: lastName,
                provider: account.provider,
                provider_id: account.providerAccountId,
                role: 'customer',
              }),
            })

            if (response.ok) {
              const userData = await response.json()
              console.log('OAuth user created/updated successfully:', userData.email)
              token.role = userData.role || 'customer'
            } else {
              console.error('Failed to create OAuth user:', response.status, response.statusText)
              token.role = 'customer' // Default role
            }
          } catch (error) {
            console.error('OAuth user creation error:', error)
            token.role = 'customer' // Default role
          }
        }
      }

      // Update session
      if (trigger === "update" && session) {
        token.role = session.role
      }

      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.sub!
        session.user.role = token.role as string
      }
      return session
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url
      // Default redirect to home after successful authentication
      return baseUrl
    },
  },
  pages: {
    signIn: '/auth/signin',
    signOut: '/auth/signout',
    error: '/auth/error',
  },
  events: {
    async signIn({ user }) {
      console.log('Customer signed in:', user.email)
    },
    async signOut({ token }) {
      console.log('Customer signed out:', token?.email)
    },
  },
  debug: process.env.NODE_ENV === 'development',
}
