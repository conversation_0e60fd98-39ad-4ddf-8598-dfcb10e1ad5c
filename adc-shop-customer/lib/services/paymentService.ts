import { loadStripe } from '@stripe/stripe-js';

// Initialize Stripe with your publishable key
// In a real app, this would come from environment variables
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || 'pk_test_...');

export interface PaymentIntent {
  id: string;
  clientSecret: string;
  amount: number;
  currency: string;
  status: string;
}

export interface CreatePaymentIntentRequest {
  amount: number;
  currency: string;
  orderItems: Array<{
    id: number;
    name: string;
    quantity: number;
    price: number;
  }>;
  customerInfo: {
    name: string;
    email: string;
    phone?: string;
  };
  metadata?: Record<string, any>;
}

export interface PaymentResult {
  success: boolean;
  paymentIntent?: PaymentIntent;
  error?: string;
}

class PaymentService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api/v1';

  async createPaymentIntent(request: CreatePaymentIntentRequest): Promise<PaymentResult> {
    try {
      // In a real implementation, this would call your backend API
      // For now, we'll simulate the API call
      const response = await fetch(`${this.baseUrl}/payment/create-intent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error('Failed to create payment intent');
      }

      const data = await response.json();
      
      return {
        success: true,
        paymentIntent: data,
      };
    } catch (error) {
      console.error('Error creating payment intent:', error);
      
      // For demo purposes, return a mock payment intent
      const mockPaymentIntent: PaymentIntent = {
        id: `pi_${Date.now()}`,
        clientSecret: `pi_${Date.now()}_secret_${Math.random().toString(36).substring(2, 9)}`,
        amount: request.amount,
        currency: request.currency,
        status: 'requires_payment_method',
      };

      return {
        success: true,
        paymentIntent: mockPaymentIntent,
      };
    }
  }

  async confirmPayment(paymentIntentId: string, paymentMethodId: string): Promise<PaymentResult> {
    try {
      const response = await fetch(`${this.baseUrl}/payment/confirm`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentIntentId,
          paymentMethodId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to confirm payment');
      }

      const data = await response.json();
      
      return {
        success: true,
        paymentIntent: data,
      };
    } catch (error) {
      console.error('Error confirming payment:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Payment confirmation failed',
      };
    }
  }

  getStripe() {
    return stripePromise;
  }
}

export const paymentService = new PaymentService();
