import { useState, useCallback, useMemo } from 'react';
import { PaginationInfo } from '../services/customerApiClient';

export interface UsePaginationProps {
  initialPage?: number;
  initialLimit?: number;
  onPageChange?: (page: number) => void;
  onLimitChange?: (limit: number) => void;
}

export interface UsePaginationReturn {
  // Current state
  currentPage: number;
  limit: number;
  
  // Pagination info from API
  paginationInfo: PaginationInfo | null;
  
  // Actions
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  nextPage: () => void;
  prevPage: () => void;
  firstPage: () => void;
  lastPage: () => void;
  setPaginationInfo: (info: PaginationInfo | null) => void;
  
  // Computed values
  canGoNext: boolean;
  canGoPrev: boolean;
  totalPages: number;
  totalItems: number;
  startItem: number;
  endItem: number;
  
  // Center pagination
  centerPages: number[];
  showFirst: boolean;
  showLast: boolean;
}

export function usePagination({
  initialPage = 1,
  initialLimit = 20,
  onPageChange,
  onLimitChange,
}: UsePaginationProps = {}): UsePaginationReturn {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [limit, setCurrentLimit] = useState(initialLimit);
  const [paginationInfo, setPaginationInfo] = useState<PaginationInfo | null>(null);

  const setPage = useCallback((page: number) => {
    if (page < 1) return;
    if (paginationInfo && page > paginationInfo.total_pages) return;
    
    setCurrentPage(page);
    onPageChange?.(page);
  }, [paginationInfo, onPageChange]);

  const setLimit = useCallback((newLimit: number) => {
    if (newLimit < 1) return;
    
    setCurrentLimit(newLimit);
    setCurrentPage(1); // Reset to first page when changing limit
    onLimitChange?.(newLimit);
    onPageChange?.(1);
  }, [onPageChange, onLimitChange]);

  const nextPage = useCallback(() => {
    if (paginationInfo?.has_next) {
      setPage(currentPage + 1);
    }
  }, [currentPage, paginationInfo, setPage]);

  const prevPage = useCallback(() => {
    if (paginationInfo?.has_prev) {
      setPage(currentPage - 1);
    }
  }, [currentPage, paginationInfo, setPage]);

  const firstPage = useCallback(() => {
    setPage(1);
  }, [setPage]);

  const lastPage = useCallback(() => {
    if (paginationInfo) {
      setPage(paginationInfo.total_pages);
    }
  }, [paginationInfo, setPage]);

  // Computed values
  const canGoNext = useMemo(() => {
    return paginationInfo?.has_next ?? false;
  }, [paginationInfo]);

  const canGoPrev = useMemo(() => {
    return paginationInfo?.has_prev ?? false;
  }, [paginationInfo]);

  const totalPages = useMemo(() => {
    return paginationInfo?.total_pages ?? 0;
  }, [paginationInfo]);

  const totalItems = useMemo(() => {
    return paginationInfo?.total_items ?? 0;
  }, [paginationInfo]);

  const startItem = useMemo(() => {
    if (!paginationInfo || totalItems === 0) return 0;
    return (currentPage - 1) * limit + 1;
  }, [currentPage, limit, paginationInfo, totalItems]);

  const endItem = useMemo(() => {
    if (!paginationInfo || totalItems === 0) return 0;
    return Math.min(currentPage * limit, totalItems);
  }, [currentPage, limit, paginationInfo, totalItems]);

  const centerPages = useMemo(() => {
    return paginationInfo?.center_pages ?? [];
  }, [paginationInfo]);

  const showFirst = useMemo(() => {
    return paginationInfo?.show_first ?? false;
  }, [paginationInfo]);

  const showLast = useMemo(() => {
    return paginationInfo?.show_last ?? false;
  }, [paginationInfo]);

  return {
    // Current state
    currentPage,
    limit,
    
    // Pagination info
    paginationInfo,
    
    // Actions
    setPage,
    setLimit,
    nextPage,
    prevPage,
    firstPage,
    lastPage,
    setPaginationInfo,
    
    // Computed values
    canGoNext,
    canGoPrev,
    totalPages,
    totalItems,
    startItem,
    endItem,
    
    // Center pagination
    centerPages,
    showFirst,
    showLast,
  };
}

// Hook for managing filters with pagination
export interface UseFilteredPaginationProps<T> extends UsePaginationProps {
  initialFilters?: T;
  onFiltersChange?: (filters: T) => void;
}

export interface UseFilteredPaginationReturn<T> extends UsePaginationReturn {
  filters: T;
  setFilters: (filters: T) => void;
  updateFilter: (key: keyof T, value: T[keyof T]) => void;
  clearFilters: () => void;
  resetPagination: () => void;
}

export function useFilteredPagination<T extends Record<string, any>>({
  initialFilters = {} as T,
  onFiltersChange,
  ...paginationProps
}: UseFilteredPaginationProps<T>): UseFilteredPaginationReturn<T> {
  const [filters, setFiltersState] = useState<T>(initialFilters);
  
  const pagination = usePagination(paginationProps);

  const setFilters = useCallback((newFilters: T) => {
    setFiltersState(newFilters);
    pagination.setPage(1); // Reset to first page when filters change
    onFiltersChange?.(newFilters);
  }, [pagination, onFiltersChange]);

  const updateFilter = useCallback((key: keyof T, value: T[keyof T]) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
  }, [filters, setFilters]);

  const clearFilters = useCallback(() => {
    setFilters(initialFilters);
  }, [initialFilters, setFilters]);

  const resetPagination = useCallback(() => {
    pagination.setPage(1);
    pagination.setPaginationInfo(null);
  }, [pagination]);

  return {
    ...pagination,
    filters,
    setFilters,
    updateFilter,
    clearFilters,
    resetPagination,
  };
}
