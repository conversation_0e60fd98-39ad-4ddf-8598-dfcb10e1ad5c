"use client";

import React, { createContext, useContext, useState } from 'react';
import { CartItem } from './CartContext';

export interface Order {
  id: string;
  status: 'preparing' | 'ready' | 'delivered' | 'cancelled';
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  orderDate: Date;
  estimatedTime?: string;
  image: string;
  paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod?: string;
  paymentIntentId?: string;
}

interface OrderContextType {
  currentOrders: Order[];
  orderHistory: Order[];
  createOrder: (cartItems: CartItem[], total: number, paymentInfo?: {
    paymentStatus?: 'pending' | 'paid' | 'failed' | 'refunded';
    paymentMethod?: string;
    paymentIntentId?: string;
  }) => string;
  updateOrderStatus: (orderId: string, status: Order['status']) => void;
}

const OrderContext = createContext<OrderContextType | undefined>(undefined);

export function OrderProvider({ children }: { children: React.ReactNode }) {
  const [currentOrders, setCurrentOrders] = useState<Order[]>([
    {
      id: "ORD-2024-001",
      status: "preparing",
      items: [
        { name: "Spicy Chicken Sandwich", quantity: 1, price: 7.99 },
        { name: "Crispy Fries", quantity: 1, price: 4.99 }
      ],
      total: 12.98,
      orderDate: new Date(),
      estimatedTime: "15-20 mins",
      image: "https://images.unsplash.com/photo-**********-d2229ba7433a?w=400&h=300&fit=crop"
    },
    {
      id: "ORD-2024-002",
      status: "ready",
      items: [
        { name: "Iced Tea", quantity: 2, price: 2.50 }
      ],
      total: 5.00,
      orderDate: new Date(Date.now() - 10 * 60 * 1000),
      estimatedTime: "Ready for pickup",
      image: "https://images.unsplash.com/photo-**********-c7306c1976bc?w=400&h=300&fit=crop"
    }
  ]);

  const [orderHistory, setOrderHistory] = useState<Order[]>([
    {
      id: "ORD-2024-003",
      status: "delivered",
      items: [
        { name: "Grilled Salmon", quantity: 1, price: 26.99 },
        { name: "Pasta Primavera", quantity: 1, price: 18.99 }
      ],
      total: 45.98,
      orderDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
      image: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400&h=300&fit=crop"
    },
    {
      id: "ORD-2024-004",
      status: "cancelled",
      items: [
        { name: "New York Strip Steak", quantity: 1, price: 32.99 }
      ],
      total: 32.99,
      orderDate: new Date(Date.now() - 48 * 60 * 60 * 1000),
      image: "https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400&h=300&fit=crop"
    }
  ]);

  const createOrder = async (cartItems: CartItem[], total: number, orderDetails: {
    customerName: string;
    customerPhone: string;
    customerEmail?: string;
    tableId?: string;
    orderType: 'dine_in' | 'takeaway' | 'delivery';
    paymentMethod: 'card' | 'cash' | 'stripe_connect';
    notes?: string;
    branchId: string;
  }): Promise<{
    orderId: string;
    paymentIntent?: {
      clientSecret: string;
      paymentIntentId: string;
      connectedAccountId: string;
    };
    requiresPayment: boolean;
  }> => {
    try {
      // Prepare order items for API
      const orderItems = cartItems.map(item => ({
        menu_item_id: item.id,
        quantity: item.quantity,
        unit_price: item.price,
        customizations: item.customizations || [],
        special_requests: item.notes || ''
      }));

      // Create order with payment via API
      const response = await fetch('/api/orders/create-with-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
        body: JSON.stringify({
          branch_id: orderDetails.branchId,
          table_id: orderDetails.tableId,
          customer_name: orderDetails.customerName,
          customer_phone: orderDetails.customerPhone,
          customer_email: orderDetails.customerEmail,
          order_type: orderDetails.orderType,
          items: orderItems,
          notes: orderDetails.notes,
          payment_method: orderDetails.paymentMethod,
          metadata: {
            created_via: 'customer_app',
            total_items: cartItems.length.toString(),
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create order');
      }

      const result = await response.json();
      const orderData = result.data;

      // Create local order for immediate UI update
      const orderId = orderData.order.id;
      const newOrder: Order = {
        id: orderId,
        status: 'preparing',
        items: cartItems.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: item.price
        })),
        total,
        orderDate: new Date(),
        estimatedTime: orderData.estimated_time ? `${orderData.estimated_time} mins` : "15-20 mins",
        image: cartItems[0]?.image || "https://images.unsplash.com/photo-**********-d2229ba7433a?w=400&h=300&fit=crop",
        paymentStatus: orderData.requires_payment ? 'pending' : 'paid',
        paymentMethod: orderDetails.paymentMethod,
        paymentIntentId: orderData.payment_intent?.payment_intent_id,
        connectedAccountId: orderData.connected_account_id,
      };

      setCurrentOrders(prev => [newOrder, ...prev]);

      // Return order creation result
      return {
        orderId,
        paymentIntent: orderData.payment_intent ? {
          clientSecret: orderData.payment_intent.client_secret,
          paymentIntentId: orderData.payment_intent.payment_intent_id,
          connectedAccountId: orderData.connected_account_id,
        } : undefined,
        requiresPayment: orderData.requires_payment,
      };
    } catch (error) {
      console.error('Failed to create order:', error);
      throw error;
    }
  };

  const updateOrderStatus = (orderId: string, status: Order['status']) => {
    setCurrentOrders(prev => {
      const orderIndex = prev.findIndex(order => order.id === orderId);
      if (orderIndex === -1) return prev;

      const updatedOrder = { ...prev[orderIndex], status };
      const newCurrentOrders = [...prev];

      if (status === 'delivered' || status === 'cancelled') {
        // Move to order history
        newCurrentOrders.splice(orderIndex, 1);
        setOrderHistory(prevHistory => [updatedOrder, ...prevHistory]);
        return newCurrentOrders;
      } else {
        // Update in current orders
        newCurrentOrders[orderIndex] = updatedOrder;
        return newCurrentOrders;
      }
    });
  };

  const value = {
    currentOrders,
    orderHistory,
    createOrder,
    updateOrderStatus
  };

  return (
    <OrderContext.Provider value={value}>
      {children}
    </OrderContext.Provider>
  );
}

export function useOrders() {
  const context = useContext(OrderContext);
  if (context === undefined) {
    throw new Error('useOrders must be used within an OrderProvider');
  }
  return context;
}
