# Customer Service Setup Guide

This guide will help you set up the complete customer service stack, including the Golang backend API and Next.js frontend, connected to the same database as the merchant backend.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Customer      │    │   Customer      │    │   Shared        │
│   Frontend      │◄──►│   Backend       │◄──►│   Database      │
│   (Next.js)     │    │   (Golang)      │    │   (PostgreSQL)  │
│   Port: 3000    │    │   Port: 8081    │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- **Go 1.21+** - [Download](https://golang.org/dl/)
- **Node.js 18+** - [Download](https://nodejs.org/)
- **PostgreSQL** - Running with merchant backend database
- **Git** - For version control

### 1. Clone and Setup

```bash
# Navigate to the customer directory
cd adc-shop-customer

# Make scripts executable (if not already)
chmod +x start-customer-services.sh stop-customer-services.sh

# Start all services
./start-customer-services.sh
```

The script will:
- ✅ Check dependencies
- ✅ Create environment files from examples
- ✅ Install all dependencies
- ✅ Build the backend
- ✅ Start both services

### 2. Access the Services

- **Customer App**: http://localhost:3000
- **Customer API**: http://localhost:8081
- **Health Check**: http://localhost:8081/health

### 3. Stop Services

```bash
./stop-customer-services.sh
```

## 🔧 Manual Setup

If you prefer to set up manually:

### Backend Setup

```bash
cd customer-backend

# Copy environment file
cp .env.example .env

# Update database credentials in .env to match merchant backend
# DB_HOST=localhost
# DB_PORT=5432
# DB_USER=restaurant_user
# DB_PASSWORD=restaurant_pass
# DB_NAME=restaurant_db

# Install dependencies
go mod download

# Build the application
go build -o bin/customer-api cmd/server/main.go

# Run the backend
./bin/customer-api
```

### Frontend Setup

```bash
# In the customer root directory
cp .env.local.example .env.local

# Update API URL in .env.local
# NEXT_PUBLIC_CUSTOMER_API_URL=http://localhost:8081

# Install dependencies
npm install

# Start development server
npm run dev
```

## 🗄️ Database Connection

The customer backend connects to the **same database** as the merchant backend:

### Database Configuration

```env
# Same as merchant backend
DB_HOST=localhost
DB_PORT=5432
DB_USER=restaurant_user
DB_PASSWORD=restaurant_pass
DB_NAME=restaurant_db
DB_SSL_MODE=disable
```

### Database Schema

The customer service uses these existing tables:
- `shops` - Shop information
- `shop_branches` - Branch details
- `menu_items` - Menu items
- `menu_categories` - Menu categories
- `menu_item_customizations` - Item customizations

## 🎯 Features

### Customer Backend (Golang)

- **Center Pagination**: Enhanced pagination with center-focused navigation
- **Shop Discovery**: Search, filter, and browse restaurants
- **Menu Browsing**: View menu items with advanced filtering
- **Location Services**: Find nearby restaurants
- **Dietary Filters**: Vegetarian, vegan, gluten-free options
- **Performance Optimized**: Efficient queries and caching

### Customer Frontend (Next.js)

- **Modern UI**: Built with shadcn/ui components
- **Responsive Design**: Mobile-first approach
- **Real-time Search**: Instant search and filtering
- **Shopping Cart**: Add items and manage orders
- **Pagination**: Center pagination for better UX
- **Theme Support**: Consistent with restaurant branding

## 📡 API Endpoints

### Shop Endpoints
```
GET /api/v1/shops                    # List shops with pagination
GET /api/v1/shops/{id}               # Get shop details
GET /api/v1/shops/search?q={query}   # Search shops
GET /api/v1/shops/popular            # Popular shops
GET /api/v1/shops/nearby             # Nearby shops
GET /api/v1/shops/{id}/status        # Shop operating status
```

### Menu Endpoints
```
GET /api/v1/shops/{id}/menu          # Menu items with pagination
GET /api/v1/shops/{id}/menu/popular  # Popular items
GET /api/v1/shops/{id}/menu/search   # Search menu items
GET /api/v1/menu/items/{id}          # Item details
```

## 🔧 Configuration

### Backend Configuration

Key environment variables in `customer-backend/.env`:

```env
# Server
CUSTOMER_PORT=8081
GIN_MODE=debug

# Database (same as merchant)
DB_HOST=localhost
DB_PORT=5432
DB_USER=restaurant_user
DB_PASSWORD=restaurant_pass
DB_NAME=restaurant_db

# Pagination
CUSTOMER_DEFAULT_PAGE_SIZE=20
CUSTOMER_MAX_PAGE_SIZE=100
CUSTOMER_ENABLE_CENTER_PAGING=true

# Caching
CUSTOMER_SHOP_SETTINGS_CACHE=600
```

### Frontend Configuration

Key environment variables in `.env.local`:

```env
# API Configuration
NEXT_PUBLIC_CUSTOMER_API_URL=http://localhost:8081

# Feature Flags
NEXT_PUBLIC_ENABLE_GEOLOCATION=true
NEXT_PUBLIC_ENABLE_PUSH_NOTIFICATIONS=false
```

## 🐳 Docker Setup

### Using Docker Compose

```bash
# Start all services including database
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Individual Docker Builds

```bash
# Build backend
cd customer-backend
docker build -t customer-api .

# Build frontend
docker build -f Dockerfile.frontend -t customer-frontend .
```

## 🧪 Testing

### Backend Tests

```bash
cd customer-backend
go test ./...
```

### Frontend Tests

```bash
npm test
```

### Load Testing

```bash
cd customer-backend
make load-test
```

## 📊 Monitoring

### Health Checks

- Backend: http://localhost:8081/health
- Frontend: http://localhost:3000/api/health

### Logs

```bash
# View live logs
tail -f logs/backend.log logs/frontend.log

# Backend logs only
tail -f logs/backend.log

# Frontend logs only
tail -f logs/frontend.log
```

## 🔍 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Ensure PostgreSQL is running
   - Check database credentials match merchant backend
   - Verify database exists and is accessible

2. **Port Already in Use**
   - Stop existing services: `./stop-customer-services.sh`
   - Check for processes: `lsof -i :3000` or `lsof -i :8081`
   - Kill processes: `kill -9 <PID>`

3. **Frontend Can't Connect to Backend**
   - Verify backend is running on port 8081
   - Check `NEXT_PUBLIC_CUSTOMER_API_URL` in `.env.local`
   - Ensure CORS is properly configured

4. **Build Failures**
   - Update Go to 1.21+: `go version`
   - Update Node.js to 18+: `node --version`
   - Clear caches: `go clean -modcache` and `npm cache clean --force`

### Debug Mode

```bash
# Backend debug mode
cd customer-backend
GIN_MODE=debug LOG_LEVEL=debug ./bin/customer-api

# Frontend debug mode
DEBUG=* npm run dev
```

## 🚀 Production Deployment

### Environment Setup

1. Update environment variables for production
2. Set `GIN_MODE=release` in backend
3. Set `NODE_ENV=production` in frontend
4. Configure proper database connection strings
5. Set up SSL certificates
6. Configure reverse proxy (nginx)

### Build for Production

```bash
# Backend
cd customer-backend
CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o customer-api ./cmd/server

# Frontend
npm run build
npm start
```

## 📚 Additional Resources

- [Go Documentation](https://golang.org/doc/)
- [Next.js Documentation](https://nextjs.org/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Docker Documentation](https://docs.docker.com/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
