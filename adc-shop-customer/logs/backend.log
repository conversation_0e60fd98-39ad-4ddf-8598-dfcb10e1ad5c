Running customer-api...
{"level":"info","msg":"Starting Customer API","time":"2025-06-05T14:25:06+07:00"}
{"level":"info","msg":"Using DATABASE_URL for database connection","time":"2025-06-05T14:25:06+07:00"}
{"level":"info","msg":"Database connection established","time":"2025-06-05T14:25:06+07:00"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> customer-backend/internal/api/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] GET    /shops                    --> customer-backend/internal/api/handlers.(*ShopHandler).GetShops-fm (6 handlers)
[GIN-debug] GET    /shops/search             --> customer-backend/internal/api/handlers.(*ShopHandler).SearchShops-fm (6 handlers)
[GIN-debug] GET    /shops/popular            --> customer-backend/internal/api/handlers.(*ShopHandler).GetPopularShops-fm (6 handlers)
[GIN-debug] GET    /shops/nearby             --> customer-backend/internal/api/handlers.(*ShopHandler).GetNearbyShops-fm (6 handlers)
[GIN-debug] GET    /shops/category/:category --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/filter-options     --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopFilterOptions-fm (6 handlers)
[GIN-debug] GET    /shops/:id                --> customer-backend/internal/api/handlers.(*ShopHandler).GetShop-fm (6 handlers)
[GIN-debug] GET    /shops/:id/status         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatus-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu           --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/popular   --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/new       --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/search    --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategories-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/status  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatusBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu    --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug --> customer-backend/internal/api/handlers.(*ShopHandler).GetBranchBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables --> customer-backend/internal/api/handlers.(*TableHandler).GetTablesByBranch-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/available --> customer-backend/internal/api/handlers.(*TableHandler).GetAvailableTablesByBranch-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/number/:tableNumber --> customer-backend/internal/api/handlers.(*TableHandler).GetTableByNumber-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/:tableId/validate --> customer-backend/internal/api/handlers.(*TableHandler).ValidateTableForOrder-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/table-areas --> customer-backend/internal/api/handlers.(*TableHandler).GetTableAreas-fm (6 handlers)
[GIN-debug] GET    /menu/items/:itemId       --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItem-fm (6 handlers)
[GIN-debug] GET    /tables/:tableId          --> customer-backend/internal/api/handlers.(*TableHandler).GetTableByID-fm (6 handlers)
[GIN-debug] POST   /orders/table             --> customer-backend/internal/api/handlers.(*OrderHandler).CreateTableOrder-fm (6 handlers)
[GIN-debug] POST   /orders/create-with-payment --> customer-backend/internal/api/handlers.(*OrderHandler).CreateOrderWithPayment-fm (6 handlers)
[GIN-debug] GET    /orders/:orderId          --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrderByID-fm (6 handlers)
[GIN-debug] GET    /orders/number/:orderNumber --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrderByNumber-fm (6 handlers)
[GIN-debug] GET    /orders/table/:tableId    --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrdersByTable-fm (6 handlers)
[GIN-debug] GET    /orders/table/:tableId/active --> customer-backend/internal/api/handlers.(*OrderHandler).GetActiveOrdersByTable-fm (6 handlers)
[GIN-debug] GET    /orders/customer          --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrdersByCustomer-fm (6 handlers)
[GIN-debug] PUT    /orders/:orderId/status   --> customer-backend/internal/api/handlers.(*OrderHandler).UpdateOrderStatus-fm (6 handlers)
[GIN-debug] POST   /payments/create-intent   --> customer-backend/internal/api/handlers.(*PaymentHandler).CreatePaymentIntent-fm (6 handlers)
[GIN-debug] POST   /payments/confirm         --> customer-backend/internal/api/handlers.(*PaymentHandler).ConfirmPayment-fm (6 handlers)
[GIN-debug] GET    /payments/:paymentIntentId/status --> customer-backend/internal/api/handlers.(*PaymentHandler).GetPaymentStatus-fm (6 handlers)
[GIN-debug] POST   /payments/:paymentIntentId/cancel --> customer-backend/internal/api/handlers.(*PaymentHandler).CancelPayment-fm (6 handlers)
[GIN-debug] POST   /payments/webhook         --> customer-backend/internal/api/handlers.(*PaymentHandler).HandleWebhook-fm (6 handlers)
[GIN-debug] GET    /payments/config          --> customer-backend/internal/api/handlers.(*PaymentHandler).GetPublishableKey-fm (6 handlers)
[GIN-debug] GET    /cart                     --> customer-backend/internal/api/handlers.(*CartHandler).GetCart-fm (6 handlers)
[GIN-debug] POST   /cart/add                 --> customer-backend/internal/api/handlers.(*CartHandler).AddToCart-fm (6 handlers)
[GIN-debug] PUT    /cart/update              --> customer-backend/internal/api/handlers.(*CartHandler).UpdateQuantity-fm (6 handlers)
[GIN-debug] DELETE /cart/remove              --> customer-backend/internal/api/handlers.(*CartHandler).RemoveFromCart-fm (6 handlers)
[GIN-debug] DELETE /cart/clear               --> customer-backend/internal/api/handlers.(*CartHandler).ClearCart-fm (6 handlers)
[GIN-debug] DELETE /cart/clear-branch        --> customer-backend/internal/api/handlers.(*CartHandler).ClearBranchCart-fm (6 handlers)
[GIN-debug] POST   /cart/sync                --> customer-backend/internal/api/handlers.(*CartHandler).SyncCartOnLogin-fm (6 handlers)
[GIN-debug] GET    /docs/*any                --> customer-backend/internal/api/routes.SetupRoutes.func2 (6 handlers)
{"level":"info","msg":"Customer API starting on port 8900","time":"2025-06-05T14:25:06+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: weerawat-poseeya, branch slug: the-green-terrace, page=1, limit=20","time":"2025-06-05T14:25:24+07:00"}

2025/06/05 14:25:24 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[268.076ms] [33m[rows:1][35m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_1749100308081_x4tt0ris2' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1[0m
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 14:25:24 +07] \"GET /cart HTTP/1.1 200 268.34975ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T14:25:24+07:00"}

2025/06/05 14:25:24 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:496 [33mSLOW SQL >= 200ms
[0m[31;1m[415.842ms] [33m[rows:1][35m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."latitude","shop_branches"."longitude","shop_branches"."location_accuracy","shop_branches"."geocoded_at","shop_branches"."place_id","shop_branches"."formatted_address","shop_branches"."location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'weerawat-poseeya' AND shop_branches.slug = 'the-green-terrace' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1[0m
{"level":"info","msg":"Looking for branch with shop slug: weerawat-poseeya, branch slug: the-green-terrace","time":"2025-06-05T14:25:24+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 14:25:26 +07] \"GET /shops/slug/weerawat-poseeya/branches/slug/the-green-terrace/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 200 2.429580042s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T14:25:26+07:00"}

2025/06/05 14:25:30 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[321.152ms] [33m[rows:1][35m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_1749100308081_x4tt0ris2' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1[0m

2025/06/05 14:25:30 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:99 [35;1mrecord not found
[0m[33m[146.934ms] [34;1m[rows:0][0m SELECT * FROM "cart_items" WHERE (cart_session_id = 'fc20d5d2-cd6c-4645-a346-63e6099661d5' AND menu_item_id = 'a137ea06-13f9-44d0-8110-47bbfa49a06a' AND shop_slug = 'weerawat-poseeya' AND branch_slug = 'the-green-terrace') AND "cart_items"."deleted_at" IS NULL ORDER BY "cart_items"."id" LIMIT 1

2025/06/05 14:25:31 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:105 [33mSLOW SQL >= 200ms
[0m[31;1m[260.419ms] [33m[rows:1][35m INSERT INTO "cart_items" ("cart_session_id","menu_item_id","quantity","name","description","image","price","shop_slug","branch_slug","table_id","table_number","shop_id","branch_id","created_at","updated_at","deleted_at") VALUES ('fc20d5d2-cd6c-4645-a346-63e6099661d5','a137ea06-13f9-44d0-8110-47bbfa49a06a',1,'คะน้าหมูสับ','This popular Thai dish is a flavorful blend of stir-fried Chinese kale (Kana) and minced pork (Moo Sub). It''s seasoned with garlic, oyster sauce, and a dash of soy sauce. The savory pork pairs perfectly with the slightly bitter, but crunchy Kana, offering a delightfully balanced taste in every bite.','https://storage.googleapis.com/scandine-storage-dev/ai-generated/shop/5a76fb56-4c1c-441f-9396-0c3d4f13765a/3f98182f-95c7-4453-a31f-f86dd5310d1b_1748867846.png',15,'weerawat-poseeya','the-green-terrace',NULL,NULL,NULL,NULL,'2025-06-05 14:25:30.941','2025-06-05 14:25:30.941',NULL) RETURNING "id"[0m
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 14:25:31 +07] \"POST /cart/add HTTP/1.1 200 1.007954709s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T14:25:31+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 14:25:33 +07] \"GET /cart HTTP/1.1 200 154.370208ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T14:25:33+07:00"}
{"level":"info","msg":"Getting branch by slugs: shop=weerawat-poseeya, branch=the-green-terrace","time":"2025-06-05T14:25:35+07:00"}
{"level":"info","msg":"Getting branch by slugs: shop=weerawat-poseeya, branch=the-green-terrace","time":"2025-06-05T14:25:35+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 14:25:36 +07] \"GET /shops/slug/weerawat-poseeya/branches/slug/the-green-terrace HTTP/1.1 200 195.708084ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T14:25:36+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 14:25:36 +07] \"GET /shops/slug/weerawat-poseeya/branches/slug/the-green-terrace HTTP/1.1 200 204.254542ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T14:25:36+07:00"}

2025/06/05 14:25:44 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/order_repository.go:27 [33mSLOW SQL >= 200ms
[0m[31;1m[282.132ms] [33m[rows:1][35m INSERT INTO "orders" ("branch_id","table_id","customer_id","customer_name","customer_phone","customer_email","order_number","type","status","notes","subtotal","tax","service_charge","discount","tip","total","payment_status","payment_method","payment_intent_id","connected_account","estimated_time","served_at","completed_at","cancelled_at","cancellation_reason","created_at","updated_at","deleted_at","id") VALUES ('5a76fb56-4c1c-441f-9396-0c3d4f13765a',NULL,NULL,'posriya','**********','<EMAIL>','ORD-**********','dine_in','pending','',20,1.6,2,0,0,23.6,'pending',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2025-06-05 14:25:44.437','2025-06-05 14:25:44.437',NULL,'3e54c5fb-e4e9-4fdd-81a9-f0a55233893c') RETURNING "id"[0m

2025/06/05 14:25:45 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/order_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[249.119ms] [33m[rows:1][35m INSERT INTO "order_items" ("order_id","menu_item_id","name","price","quantity","modifications","notes","total","status","created_at","updated_at","deleted_at","id") VALUES ('3e54c5fb-e4e9-4fdd-81a9-f0a55233893c',NULL,'Menu Item',15,1,NULL,NULL,15,'pending','2025-06-05 14:25:44.928','2025-06-05 14:25:44.928',NULL,'8ede58b1-9278-4a5a-948b-351670c3317a') RETURNING "id"[0m
{"amount":2360,"connected_account":"acct_1RWGqRCvS6V6yiE9","level":"info","msg":"Creating simple payment intent for testing","time":"2025-06-05T14:25:45+07:00"}
{"amount":2360,"connected_account":"acct_1RWGqRCvS6V6yiE9","level":"info","msg":"Payment intent created successfully","order_id":"3e54c5fb-e4e9-4fdd-81a9-f0a55233893c","payment_intent_id":"pi_3RWYBJCtNXkGk5bX0I2hb1VB","platform_fee":59,"time":"2025-06-05T14:25:45+07:00"}
{"amount":2360,"client_secret":"pi_3RWYBJCtNXkGk5bX0I2hb1VB_secret_0MDw6aDydhGhMlIU957RauBDb","currency":"thb","level":"info","msg":"Payment intent created for order","payment_intent_id":"pi_3RWYBJCtNXkGk5bX0I2hb1VB","time":"2025-06-05T14:25:45+07:00"}
{"level":"info","msg":"Order with payment created successfully","order_id":"3e54c5fb-e4e9-4fdd-81a9-f0a55233893c","time":"2025-06-05T14:25:45+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 14:25:45 +07] \"POST /orders/create-with-payment HTTP/1.1 201 1.285516542s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T14:25:45+07:00"}

2025/06/05 14:26:52 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/order_repository.go:38 [33mSLOW SQL >= 200ms
[0m[31;1m[210.103ms] [33m[rows:1][35m INSERT INTO "order_items" ("order_id","menu_item_id","name","price","quantity","modifications","notes","total","status","created_at","updated_at","deleted_at","id") VALUES ('74d0f231-443a-44c8-a921-acfd1772c861',NULL,'Menu Item',15,1,NULL,NULL,15,'pending','2025-06-05 14:26:52.364','2025-06-05 14:26:52.364',NULL,'30a94ce6-4b79-4dc2-9f92-aee1a017270a') RETURNING "id"[0m
{"amount":1770,"connected_account":"acct_1RWGqRCvS6V6yiE9","level":"info","msg":"Creating simple payment intent for testing","time":"2025-06-05T14:26:52+07:00"}
{"amount":1770,"connected_account":"acct_1RWGqRCvS6V6yiE9","level":"info","msg":"Payment intent created successfully","order_id":"74d0f231-443a-44c8-a921-acfd1772c861","payment_intent_id":"pi_3RWYCOCtNXkGk5bX0u8WXWkN","platform_fee":44,"time":"2025-06-05T14:26:52+07:00"}
{"amount":1770,"client_secret":"pi_3RWYCOCtNXkGk5bX0u8WXWkN_secret_jhVVgzghZwSWW1iqT3EaFs9Ph","currency":"thb","level":"info","msg":"Payment intent created for order","payment_intent_id":"pi_3RWYCOCtNXkGk5bX0u8WXWkN","time":"2025-06-05T14:26:52+07:00"}
{"level":"info","msg":"Order with payment created successfully","order_id":"74d0f231-443a-44c8-a921-acfd1772c861","time":"2025-06-05T14:26:52+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 14:26:52 +07] \"POST /orders/create-with-payment HTTP/1.1 201 791.786958ms \"curl/8.7.1\" \"\n","time":"2025-06-05T14:26:52+07:00"}
signal: killed
make[2]: *** [run] Error 1
