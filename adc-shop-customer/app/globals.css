@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.3rem;
  /* Solar Dusk theme variables */
  --background: oklch(0.9885 0.0057 84.5659);
  --foreground: oklch(0.3660 0.0251 49.6085);
  --card: oklch(0.9686 0.0091 78.2818);
  --card-foreground: oklch(0.3660 0.0251 49.6085);
  --popover: oklch(0.9686 0.0091 78.2818);
  --popover-foreground: oklch(0.3660 0.0251 49.6085);
  --primary: oklch(0.5553 0.1455 48.9975);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.8276 0.0752 74.4400);
  --secondary-foreground: oklch(0.4444 0.0096 73.6390);
  --muted: oklch(0.9363 0.0218 83.2637);
  --muted-foreground: oklch(0.5534 0.0116 58.0708);
  --accent: oklch(0.9000 0.0500 74.9889);
  --accent-foreground: oklch(0.4444 0.0096 73.6390);
  --destructive: oklch(0.6991 0.2206 29.2338);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8276 0.0752 74.4400);
  --input: oklch(0.8276 0.0752 74.4400);
  --ring: oklch(0.5553 0.1455 48.9975);
  --chart-1: oklch(0.5553 0.1455 48.9975);
  --chart-2: oklch(0.5534 0.0116 58.0708);
  --chart-3: oklch(0.5538 0.1207 66.4416);
  --chart-4: oklch(0.5534 0.0116 58.0708);
  --chart-5: oklch(0.6806 0.1423 75.8340);
  --sidebar: oklch(0.9363 0.0218 83.2637);
  --sidebar-foreground: oklch(0.5534 0.0116 58.0708);
  --sidebar-primary: oklch(0.5553 0.1455 48.9975);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.9000 0.0500 74.9889);
  --sidebar-accent-foreground: oklch(0.4444 0.0096 73.6390);
  --sidebar-border: oklch(0.8276 0.0752 74.4400);
  --sidebar-ring: oklch(0.5553 0.1455 48.9975);
}

.dark {
  /* Solar Dusk dark theme variables */
  --background: oklch(0.1412 0.0095 49.6085);
  --foreground: oklch(0.9686 0.0091 78.2818);
  --card: oklch(0.1412 0.0095 49.6085);
  --card-foreground: oklch(0.9686 0.0091 78.2818);
  --popover: oklch(0.1412 0.0095 49.6085);
  --popover-foreground: oklch(0.9686 0.0091 78.2818);
  --primary: oklch(0.5553 0.1455 48.9975);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.2685 0.0063 34.2976);
  --secondary-foreground: oklch(0.9686 0.0091 78.2818);
  --muted: oklch(0.2685 0.0063 34.2976);
  --muted-foreground: oklch(0.6806 0.1423 75.8340);
  --accent: oklch(0.2685 0.0063 34.2976);
  --accent-foreground: oklch(0.9686 0.0091 78.2818);
  --destructive: oklch(0.6991 0.2206 29.2338);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.2685 0.0063 34.2976);
  --input: oklch(0.2685 0.0063 34.2976);
  --ring: oklch(0.5553 0.1455 48.9975);
  --chart-1: oklch(0.7049 0.1867 47.6044);
  --chart-2: oklch(0.6847 0.1479 237.3225);
  --chart-3: oklch(0.7952 0.1617 86.0468);
  --chart-4: oklch(0.7161 0.0091 56.2590);
  --chart-5: oklch(0.5534 0.0116 58.0708);
  --sidebar: oklch(0.2685 0.0063 34.2976);
  --sidebar-foreground: oklch(0.6806 0.1423 75.8340);
  --sidebar-primary: oklch(0.5553 0.1455 48.9975);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.2685 0.0063 34.2976);
  --sidebar-accent-foreground: oklch(0.9686 0.0091 78.2818);
  --sidebar-border: oklch(0.2685 0.0063 34.2976);
  --sidebar-ring: oklch(0.5553 0.1455 48.9975);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Be Vietnam Pro', sans-serif;
  }
}
