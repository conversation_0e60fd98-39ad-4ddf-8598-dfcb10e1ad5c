"use client";

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { ChevronLeft, ChevronRight, Clock, CheckCircle, XCircle, Package, CreditCard } from "lucide-react";
import Header from "@/components/Header";

import { useOrders, Order } from "@/lib/context/OrderContext";

export default function OrdersPage() {
  const { currentOrders, orderHistory } = useOrders();
  const searchParams = useSearchParams();
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);

  useEffect(() => {
    if (searchParams.get('payment') === 'success') {
      setShowPaymentSuccess(true);
      // Hide the success message after 5 seconds
      const timer = setTimeout(() => {
        setShowPaymentSuccess(false);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [searchParams]);

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'preparing':
        return <Clock className="h-4 w-4" />;
      case 'ready':
        return <Package className="h-4 w-4" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'preparing':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'ready':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
    }
  };

  const getStatusText = (status: Order['status']) => {
    switch (status) {
      case 'preparing':
        return 'Preparing';
      case 'ready':
        return 'Ready for Pickup';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <p className="text-foreground tracking-light text-[32px] font-bold leading-tight min-w-72">Your Orders</p>
            </div>

            {/* Payment Success Notification */}
            {showPaymentSuccess && (
              <div className="mx-4 mb-6">
                <Card className="bg-green-50 border-green-200">
                  <CardContent className="flex items-center gap-3 p-4">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                    <div>
                      <p className="text-green-800 font-medium">Payment Successful!</p>
                      <p className="text-green-700 text-sm">Your order has been placed and payment has been processed.</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Tab Navigation */}
            <Tabs defaultValue="current" className="pb-3">
              <TabsList className="grid w-full grid-cols-2 px-4">
                <TabsTrigger value="current">Current Orders</TabsTrigger>
                <TabsTrigger value="history">Order History</TabsTrigger>
              </TabsList>

              {/* Current Orders Tab */}
              <TabsContent value="current">
                <div className="flex flex-col px-4 py-6">
                {currentOrders.length === 0 ? (
                  <Card>
                    <CardContent className="flex flex-col items-center gap-6 p-8">
                      <div
                        className="bg-center bg-no-repeat aspect-video bg-cover rounded-xl w-full max-w-[360px]"
                        style={{
                          backgroundImage: `url("https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop")`
                        }}
                      />
                      <div className="flex max-w-[480px] flex-col items-center gap-2">
                        <p className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em] max-w-[480px] text-center">
                          No current orders
                        </p>
                        <p className="text-foreground text-sm font-normal leading-normal max-w-[480px] text-center">
                          You haven&apos;t placed any orders yet. Browse our menu and start ordering!
                        </p>
                        <Button className="mt-4" asChild>
                          <a href="/">Browse Menu</a>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="space-y-4">
                    {currentOrders.map((order) => (
                      <Card key={order.id}>
                        <CardHeader>
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <p className="text-muted-foreground text-sm font-normal">Order {order.id}</p>
                                <Badge className={`flex items-center gap-1 ${getStatusColor(order.status)}`}>
                                  {getStatusIcon(order.status)}
                                  {getStatusText(order.status)}
                                </Badge>
                              </div>
                              <CardTitle className="text-lg mb-1">{getStatusText(order.status)}</CardTitle>
                              <p className="text-muted-foreground text-sm">
                                {order.items.length} items · ${order.total.toFixed(2)}
                              </p>
                              {order.estimatedTime && (
                                <p className="text-primary text-sm font-medium mt-1">
                                  {order.estimatedTime}
                                </p>
                              )}
                            </div>
                            <div
                              className="w-20 h-20 bg-center bg-no-repeat bg-cover rounded-lg"
                              style={{ backgroundImage: `url("${order.image}")` }}
                            />
                          </div>
                        </CardHeader>
                        <CardContent>
                          <Separator className="mb-4" />
                          <h4 className="text-foreground font-medium mb-2">Order Items:</h4>
                          <div className="space-y-1">
                            {order.items.map((item, index) => (
                              <div key={index} className="flex justify-between text-sm">
                                <span className="text-muted-foreground">
                                  {item.quantity}x {item.name}
                                </span>
                                <span className="text-foreground">
                                  ${(item.price * item.quantity).toFixed(2)}
                                </span>
                              </div>
                            ))}
                          </div>

                          {/* Payment Status */}
                          {order.paymentStatus && (
                            <div className="mt-3 pt-3 border-t">
                              <div className="flex items-center gap-2">
                                <CreditCard className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm text-muted-foreground">Payment:</span>
                                <Badge
                                  className={`text-xs ${
                                    order.paymentStatus === 'paid'
                                      ? 'bg-green-100 text-green-800 border-green-200'
                                      : order.paymentStatus === 'pending'
                                      ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                                      : 'bg-red-100 text-red-800 border-red-200'
                                  }`}
                                >
                                  {order.paymentStatus === 'paid' ? 'Paid' :
                                   order.paymentStatus === 'pending' ? 'Pending' :
                                   order.paymentStatus === 'failed' ? 'Failed' : 'Refunded'}
                                </Badge>
                                {order.paymentMethod && (
                                  <span className="text-xs text-muted-foreground">
                                    via {order.paymentMethod}
                                  </span>
                                )}
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
                </div>
              </TabsContent>

              {/* Order History Tab */}
              <TabsContent value="history">
                <div className="flex flex-col">
                <div className="px-4 py-6">
                  <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-[-0.015em] pb-3">
                    Past Orders
                  </h2>

                  {orderHistory.length === 0 ? (
                    <Card>
                      <CardContent className="flex flex-col items-center gap-6 p-8">
                        <p className="text-muted-foreground text-center">No order history found.</p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="space-y-4">
                      {orderHistory.map((order) => (
                        <Card key={order.id}>
                          <CardContent className="flex items-stretch justify-between gap-4 p-4">
                            <div className="flex flex-col gap-1 flex-[2_2_0px]">
                              <div className="flex items-center gap-3 mb-1">
                                <p className="text-muted-foreground text-sm font-normal">Order {order.id}</p>
                                <Badge className={`flex items-center gap-1 ${getStatusColor(order.status)}`}>
                                  {getStatusIcon(order.status)}
                                  {getStatusText(order.status)}
                                </Badge>
                              </div>
                              <p className="text-foreground text-base font-bold">{getStatusText(order.status)}</p>
                              <p className="text-muted-foreground text-sm">
                                {order.items.length} items · ${order.total.toFixed(2)}
                              </p>
                              <p className="text-muted-foreground text-xs">
                                {formatDate(order.orderDate)}
                              </p>
                            </div>
                            <div
                              className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex-1"
                              style={{ backgroundImage: `url("${order.image}")` }}
                            />
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>

                {/* Pagination */}
                <Card className="mx-4">
                  <CardContent className="flex items-center justify-center p-4">
                    <div className="flex items-center gap-1">
                      <Button variant="ghost" size="sm" className="h-10 w-10 p-0">
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-10 w-10 p-0 bg-muted text-foreground font-bold">
                        1
                      </Button>
                      <Button variant="ghost" size="sm" className="h-10 w-10 p-0">
                        2
                      </Button>
                      <Button variant="ghost" size="sm" className="h-10 w-10 p-0">
                        3
                      </Button>
                      <Button variant="ghost" size="sm" className="h-10 w-10 p-0">
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
