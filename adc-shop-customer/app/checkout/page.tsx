"use client";

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Info, ArrowRight } from "lucide-react";
import Header from "@/components/Header";
import { useCart } from "@/lib/context/CartContext";

export default function CheckoutPage() {
  const { cartItems } = useCart();
  const router = useRouter();

  // Group cart items by shop and branch
  const groupedItems = React.useMemo(() => {
    const groups: Record<string, Record<string, typeof cartItems>> = {};

    cartItems.forEach(item => {
      if (item.shopSlug && item.branchSlug) {
        if (!groups[item.shopSlug]) {
          groups[item.shopSlug] = {};
        }
        if (!groups[item.shopSlug][item.branchSlug]) {
          groups[item.shopSlug][item.branchSlug] = [];
        }
        groups[item.shopSlug][item.branchSlug].push(item);
      }
    });

    return groups;
  }, [cartItems]);

  const handleViewBranchCart = (shopSlug: string, branchSlug: string) => {
    // Determine shop type - for now assume 'food'
    const shopType = 'food';
    router.push(`/${shopType}/${shopSlug}/${branchSlug}/checkout`);
  };

  const handleBackToCart = () => {
    router.push('/cart');
  };

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToCart}
                  className="p-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <h1 className="text-foreground tracking-light text-[32px] font-bold leading-tight">Checkout</h1>
              </div>
            </div>

            {/* Information Card */}
            <div className="px-4 mb-6">
              <Card className="border-blue-200 bg-blue-50">
                <CardContent className="pt-6">
                  <div className="flex items-start gap-3">
                    <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="text-blue-900 font-medium mb-2">Branch-Specific Checkout Required</h3>
                      <p className="text-blue-800 text-sm mb-4">
                        Checkout must be done separately for each restaurant branch. This ensures proper order processing,
                        delivery coordination, and payment handling for each location.
                      </p>
                      <p className="text-blue-700 text-xs">
                        Please select a restaurant below to proceed with checkout for that specific branch.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Cart Items Grouped by Branch */}
            {cartItems.length === 0 ? (
              <Card className="mx-4">
                <CardContent className="flex flex-col items-center justify-center py-16">
                  <Info className="h-16 w-16 text-muted-foreground mb-4" />
                  <h2 className="text-foreground text-lg font-medium mb-2">No items to checkout</h2>
                  <p className="text-muted-foreground text-sm mb-6 text-center">
                    Your cart is empty. Add some items to proceed with checkout.
                  </p>
                  <Button onClick={() => router.push('/')}>
                    Browse Restaurants
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4 px-4">
                {Object.entries(groupedItems).map(([shopSlug, branches]) => (
                  <div key={shopSlug} className="space-y-3">
                    {Object.entries(branches).map(([branchSlug, items]) => {
                      const branchTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                      const branchItemCount = items.reduce((sum, item) => sum + item.quantity, 0);

                      return (
                        <Card key={`${shopSlug}-${branchSlug}`} className="overflow-hidden">
                          <CardHeader className="pb-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <CardTitle className="text-lg">
                                  {shopSlug.replace(/-/g, ' ')} - {branchSlug.replace(/-/g, ' ')}
                                </CardTitle>
                                <p className="text-muted-foreground text-sm">
                                  {branchItemCount} item{branchItemCount !== 1 ? 's' : ''} • ${branchTotal.toFixed(2)}
                                </p>
                              </div>
                              <Button
                                onClick={() => handleViewBranchCart(shopSlug, branchSlug)}
                                className="bg-[#e58219] hover:bg-[#d4741a] text-white flex items-center gap-2"
                              >
                                Checkout This Branch
                                <ArrowRight className="h-4 w-4" />
                              </Button>
                            </div>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <div className="space-y-3">
                              {items.slice(0, 3).map((item) => (
                                <div key={item.id} className="flex items-center gap-3">
                                  <div
                                    className="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-12 flex-shrink-0"
                                    style={{ backgroundImage: `url("${item.image}")` }}
                                  />
                                  <div className="flex-1 min-w-0">
                                    <p className="text-foreground text-sm font-medium truncate">{item.name}</p>
                                    <p className="text-muted-foreground text-xs">
                                      Qty: {item.quantity} • ${(item.price * item.quantity).toFixed(2)}
                                    </p>
                                  </div>
                                </div>
                              ))}
                              {items.length > 3 && (
                                <p className="text-muted-foreground text-xs">
                                  +{items.length - 3} more item{items.length - 3 !== 1 ? 's' : ''}
                                </p>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
