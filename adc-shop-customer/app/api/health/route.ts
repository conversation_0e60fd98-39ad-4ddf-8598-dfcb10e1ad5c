import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Check if the customer backend is reachable
    const backendUrl = process.env.NEXT_PUBLIC_CUSTOMER_API_URL || 'http://localhost:8081';

    let backendStatus = 'unknown';
    try {
      const response = await fetch(`${backendUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add timeout
        signal: AbortSignal.timeout(5000),
      });

      if (response.ok) {
        backendStatus = 'healthy';
      } else {
        backendStatus = 'unhealthy';
      }
    } catch {
      backendStatus = 'unreachable';
    }

    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'customer-frontend',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      backend: {
        url: backendUrl,
        status: backendStatus,
      },
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
      },
    };

    return NextResponse.json(healthData, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        service: 'customer-frontend',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
