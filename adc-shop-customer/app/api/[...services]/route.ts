import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth/nextauth.config'

// Proxy function to forward requests to the customer backend
async function proxyRequest(
  request: NextRequest,
  path: string,
  session?: { user: { id: string; email: string; role?: string } }
): Promise<NextResponse> {
  const backendUrl = process.env.NEXT_PUBLIC_CUSTOMER_API_URL || 'http://localhost:8900'

  // Get query parameters from the original request
  const url = new URL(request.url)
  const queryString = url.search // includes the '?' if there are parameters

  const targetUrl = `${backendUrl}/${path}${queryString}`

  // Prepare headers
  const headers = new Headers()

  // Copy relevant headers from the original request
  const headersToForward = [
    'content-type',
    'accept',
    'user-agent',
    'accept-language',
    'accept-encoding',
    'x-session-id', // Forward session ID for guest cart functionality
  ]

  headersToForward.forEach(headerName => {
    const value = request.headers.get(headerName)
    if (value) {
      headers.set(headerName, value)
    }
  })

  // Add authentication headers if session exists
  if (session?.user) {
    headers.set('X-User-ID', session.user.id)
    headers.set('X-User-Email', session.user.email)
    headers.set('X-User-Role', session.user.role || 'customer')
    headers.set('X-Auth-Source', 'nextauth')
  }

  try {
    // Prepare request options
    const requestOptions: RequestInit = {
      method: request.method,
      headers,
    }

    // Add body for non-GET requests
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      requestOptions.body = await request.text()
    }

    // Make the request to the backend
    const response = await fetch(targetUrl, requestOptions)

    // Get response data
    const responseData = await response.text()

    // Create response with same status and headers
    const nextResponse = new NextResponse(responseData, {
      status: response.status,
      statusText: response.statusText,
    })

    // Copy relevant response headers
    const responseHeadersToForward = [
      'content-type',
      'cache-control',
      'etag',
      'last-modified',
    ]

    responseHeadersToForward.forEach(headerName => {
      const value = response.headers.get(headerName)
      if (value) {
        nextResponse.headers.set(headerName, value)
      }
    })

    return nextResponse
  } catch (error) {
    console.error('Proxy request failed:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Handle all HTTP methods
async function handleRequest(request: NextRequest, { params }: { params: Promise<{ services: string[] }> }) {
  const resolvedParams = await params
  const path = resolvedParams.services.join('/')

  // Always try to get session for optional authentication
  // The Golang backend will determine which routes require authentication
  const session = await getServerSession(authOptions)

  return proxyRequest(request, path, session)
}

export async function GET(request: NextRequest, context: { params: Promise<{ services: string[] }> }) {
  return handleRequest(request, context)
}

export async function POST(request: NextRequest, context: { params: Promise<{ services: string[] }> }) {
  return handleRequest(request, context)
}

export async function PUT(request: NextRequest, context: { params: Promise<{ services: string[] }> }) {
  return handleRequest(request, context)
}

export async function PATCH(request: NextRequest, context: { params: Promise<{ services: string[] }> }) {
  return handleRequest(request, context)
}

export async function DELETE(request: NextRequest, context: { params: Promise<{ services: string[] }> }) {
  return handleRequest(request, context)
}
