"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, Users, MapPin, Phone, Mail, ArrowLeft, Edit, X, CheckCircle } from "lucide-react";
import Header from "@/components/Header";
import { useReservations, Reservation } from "@/lib/context/ReservationContext";
import { useRouter } from "next/navigation";
import { useParams } from "next/navigation";

export default function ReservationDetailsPage() {
  const { reservations, cancelReservation, updateReservation } = useReservations();
  const router = useRouter();
  const params = useParams();
  const reservationId = params.id as string;

  const reservation = reservations.find(r => r.id === reservationId);

  if (!reservation) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 items-center justify-center">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-foreground mb-2">Reservation Not Found</h1>
              <p className="text-muted-foreground mb-4">The reservation you're looking for doesn't exist.</p>
              <Button onClick={() => router.push('/reservations')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Reservations
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const getStatusBadge = (status: Reservation['status']) => {
    const statusConfig = {
      confirmed: {
        className: "bg-green-100 text-green-800 border-green-200",
        icon: <CheckCircle className="h-3 w-3" />,
        text: "Confirmed"
      },
      pending: {
        className: "bg-yellow-100 text-yellow-800 border-yellow-200",
        icon: <Clock className="h-3 w-3" />,
        text: "Pending Confirmation"
      },
      cancelled: {
        className: "bg-red-100 text-red-800 border-red-200",
        icon: <X className="h-3 w-3" />,
        text: "Cancelled"
      },
      completed: {
        className: "bg-blue-100 text-blue-800 border-blue-200",
        icon: <CheckCircle className="h-3 w-3" />,
        text: "Completed"
      }
    };

    const config = statusConfig[status];
    return (
      <Badge className={`flex items-center gap-1 ${config.className}`}>
        {config.icon}
        {config.text}
      </Badge>
    );
  };

  const handleCancelReservation = () => {
    if (window.confirm('Are you sure you want to cancel this reservation?')) {
      cancelReservation(reservation.id);
      router.push('/reservations');
    }
  };

  const handleModifyReservation = () => {
    // In a real app, this would navigate to an edit form
    console.log('Modify reservation:', reservation.id);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const canModify = reservation.status === 'confirmed' || reservation.status === 'pending';
  const canCancel = reservation.status === 'confirmed' || reservation.status === 'pending';

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            {/* Header Section */}
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <div className="flex min-w-72 flex-col gap-3">
                <div className="flex items-center gap-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push('/reservations')}
                    className="p-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                  <div>
                    <p className="text-foreground tracking-light text-[32px] font-bold leading-tight">
                      Reservation Details
                    </p>
                    <p className="text-muted-foreground text-sm font-normal leading-normal">
                      View and manage your reservation details.
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-start gap-2">
                {getStatusBadge(reservation.status)}
              </div>
            </div>

            {/* Reservation Information */}
            <h3 className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">
              Reservation Information
            </h3>
            <div className="p-4 grid grid-cols-[20%_1fr] gap-x-6">
              <div className="col-span-2 grid grid-cols-subgrid border-t border-border py-5">
                <p className="text-muted-foreground text-sm font-normal leading-normal">Reservation ID</p>
                <p className="text-foreground text-sm font-normal leading-normal font-mono">{reservation.id}</p>
              </div>
              <div className="col-span-2 grid grid-cols-subgrid border-t border-border py-5">
                <p className="text-muted-foreground text-sm font-normal leading-normal">Restaurant</p>
                <div className="flex flex-col">
                  <p className="text-foreground text-sm font-medium leading-normal">{reservation.restaurant}</p>
                  {reservation.location && (
                    <p className="text-muted-foreground text-xs flex items-center gap-1 mt-1">
                      <MapPin className="h-3 w-3" />
                      {reservation.location}
                    </p>
                  )}
                </div>
              </div>
              <div className="col-span-2 grid grid-cols-subgrid border-t border-border py-5">
                <p className="text-muted-foreground text-sm font-normal leading-normal">Date & Time</p>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-foreground text-sm font-normal leading-normal">
                      {formatDate(reservation.date)}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-foreground text-sm font-normal leading-normal">
                      {reservation.time}
                    </span>
                  </div>
                </div>
              </div>
              <div className="col-span-2 grid grid-cols-subgrid border-t border-border py-5">
                <p className="text-muted-foreground text-sm font-normal leading-normal">Party Size</p>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-foreground text-sm font-normal leading-normal">
                    {reservation.partySize} {reservation.partySize === 1 ? 'person' : 'people'}
                  </span>
                </div>
              </div>
              {reservation.customerName && (
                <div className="col-span-2 grid grid-cols-subgrid border-t border-border py-5">
                  <p className="text-muted-foreground text-sm font-normal leading-normal">Customer Name</p>
                  <p className="text-foreground text-sm font-normal leading-normal">{reservation.customerName}</p>
                </div>
              )}
              {reservation.phoneNumber && (
                <div className="col-span-2 grid grid-cols-subgrid border-t border-border py-5">
                  <p className="text-muted-foreground text-sm font-normal leading-normal">Phone Number</p>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-foreground text-sm font-normal leading-normal">{reservation.phoneNumber}</span>
                  </div>
                </div>
              )}
              {reservation.email && (
                <div className="col-span-2 grid grid-cols-subgrid border-t border-border py-5">
                  <p className="text-muted-foreground text-sm font-normal leading-normal">Email</p>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="text-foreground text-sm font-normal leading-normal">{reservation.email}</span>
                  </div>
                </div>
              )}
            </div>

            {/* Special Requests */}
            <h3 className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">
              Special Requests
            </h3>
            <p className="text-foreground text-base font-normal leading-normal pb-3 pt-1 px-4">
              {reservation.specialRequests || 'No special requests provided.'}
            </p>

            {/* Cancellation Policy */}
            <h3 className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">
              Cancellation Policy
            </h3>
            <p className="text-foreground text-base font-normal leading-normal pb-3 pt-1 px-4">
              You can cancel your reservation up to 24 hours before the scheduled time.
              Cancellations made within 24 hours may be subject to a fee.
            </p>

            {/* Action Buttons */}
            <div className="flex px-4 py-3 justify-end gap-3">
              {canModify && (
                <Button
                  variant="outline"
                  onClick={handleModifyReservation}
                  className="flex items-center gap-2"
                >
                  <Edit className="h-4 w-4" />
                  Modify Reservation
                </Button>
              )}
              {canCancel && (
                <Button
                  variant="destructive"
                  onClick={handleCancelReservation}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  Cancel Reservation
                </Button>
              )}
              {!canModify && !canCancel && (
                <Button
                  variant="outline"
                  onClick={() => router.push('/reservations')}
                >
                  Back to Reservations
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
