"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ShoppingCart, ArrowRight } from "lucide-react";
import Header from "@/components/Header";
import { useCart } from "@/lib/context/CartContext";
import { useRouter } from "next/navigation";

export default function GlobalCartPage() {
  const { cartItems, getTotalItems, getTotalPrice } = useCart();
  const router = useRouter();

  // Group cart items by shop and branch
  const groupedItems = React.useMemo(() => {
    const groups: Record<string, Record<string, typeof cartItems>> = {};

    cartItems.forEach(item => {
      if (item.shopSlug && item.branchSlug) {
        if (!groups[item.shopSlug]) {
          groups[item.shopSlug] = {};
        }
        if (!groups[item.shopSlug][item.branchSlug]) {
          groups[item.shopSlug][item.branchSlug] = [];
        }
        groups[item.shopSlug][item.branchSlug].push(item);
      }
    });

    return groups;
  }, [cartItems]);

  const totalItems = getTotalItems();
  const totalPrice = getTotalPrice();

  const handleViewBranchCart = (shopSlug: string, branchSlug: string) => {
    // Determine shop type - for now assume 'food', but this could be enhanced
    // TODO: Store shop type in cart items or fetch from API
    const shopType = 'food'; // Default to food for now
    // Use the actual branch slug from cart data, fallback to 'downtown' if not provided
    const actualBranchSlug = branchSlug || 'downtown';
    router.push(`/${shopType}/${shopSlug}/${actualBranchSlug}/cart`);
  };

  const handleBrowseShops = () => {
    router.push('/');
  };

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <div>
                <h1 className="text-foreground tracking-light text-[32px] font-bold leading-tight">
                  Your Cart
                </h1>
                <p className="text-muted-foreground text-base">
                  {totalItems > 0
                    ? `${totalItems} items from ${Object.keys(groupedItems).length} restaurant${Object.keys(groupedItems).length !== 1 ? 's' : ''}`
                    : 'Your cart is empty'
                  }
                </p>
              </div>
              {totalItems > 0 && (
                <div className="text-right">
                  <p className="text-muted-foreground text-sm">Total</p>
                  <p className="text-foreground text-2xl font-bold">${totalPrice.toFixed(2)}</p>
                </div>
              )}
            </div>

            {/* Cart Content */}
            {totalItems === 0 ? (
              <Card className="mx-4">
                <CardContent className="flex flex-col items-center justify-center py-16">
                  <ShoppingCart className="h-16 w-16 text-muted-foreground mb-4" />
                  <h2 className="text-foreground text-lg font-medium mb-2">Your cart is empty</h2>
                  <p className="text-muted-foreground text-sm mb-6 text-center">
                    Browse restaurants and add some delicious items to your cart
                  </p>
                  <Button onClick={handleBrowseShops}>
                    Browse Restaurants
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6 px-4">
                {/* Grouped Cart Items by Shop/Branch */}
                {Object.entries(groupedItems).map(([shopSlug, branches]) => (
                  <div key={shopSlug} className="space-y-4">
                    {Object.entries(branches).map(([branchSlug, items]) => {
                      const branchTotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                      const branchItemCount = items.reduce((sum, item) => sum + item.quantity, 0);

                      return (
                        <Card key={`${shopSlug}-${branchSlug}`} className="overflow-hidden">
                          <CardHeader className="pb-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <CardTitle className="text-lg">
                                  {shopSlug.replace(/-/g, ' ')} - {branchSlug.replace(/-/g, ' ')}
                                </CardTitle>
                                <p className="text-muted-foreground text-sm">
                                  {branchItemCount} item{branchItemCount !== 1 ? 's' : ''} • ${branchTotal.toFixed(2)}
                                </p>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewBranchCart(shopSlug, branchSlug)}
                                className="flex items-center gap-2"
                              >
                                View Cart
                                <ArrowRight className="h-4 w-4" />
                              </Button>
                            </div>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <div className="space-y-3">
                              {items.slice(0, 3).map((item) => (
                                <div key={item.id} className="flex items-center gap-3">
                                  <div
                                    className="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-12 flex-shrink-0"
                                    style={{ backgroundImage: `url("${item.image}")` }}
                                  />
                                  <div className="flex-1 min-w-0">
                                    <p className="text-foreground text-sm font-medium truncate">{item.name}</p>
                                    <p className="text-muted-foreground text-xs">
                                      Qty: {item.quantity} • ${(item.price * item.quantity).toFixed(2)}
                                    </p>
                                  </div>
                                </div>
                              ))}
                              {items.length > 3 && (
                                <p className="text-muted-foreground text-xs">
                                  +{items.length - 3} more item{items.length - 3 !== 1 ? 's' : ''}
                                </p>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                ))}

                {/* Summary Card */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Cart Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Total Items</span>
                        <span className="font-medium">{totalItems}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Restaurants</span>
                        <span className="font-medium">{Object.keys(groupedItems).length}</span>
                      </div>
                      <div className="flex justify-between text-lg font-bold">
                        <span>Total</span>
                        <span>${totalPrice.toFixed(2)}</span>
                      </div>
                    </div>
                    <div className="mt-4 space-y-2">
                      <p className="text-muted-foreground text-xs">
                        💡 Tip: You can checkout each restaurant separately by viewing their individual carts.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
