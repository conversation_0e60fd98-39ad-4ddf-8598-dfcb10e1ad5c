"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ShoppingCart, Minus, Plus, Trash2 } from "lucide-react";
import Header from "@/components/Header";
import { useCart } from "@/lib/context/CartContext";
import { useOrders } from "@/lib/context/OrderContext";
import { useRouter } from "next/navigation";
import React from "react";

interface BranchCartPageProps {
  params: Promise<{
    "shop-type": string;
    "shop-slug": string;
    "branch-slug": string;
  }>;
}

export default function BranchCartPage({ params }: BranchCartPageProps) {
  const resolvedParams = React.use(params);
  const shopType = resolvedParams["shop-type"];
  const shopSlug = resolvedParams["shop-slug"];
  const branchSlug = resolvedParams["branch-slug"];

  const {
    get<PERSON>ranchCartItems,
    updateQuantity,
    removeFrom<PERSON>art,
    clearBranchCart,
    getBranchTotalItems,
    getBranchTotalPrice,
    isLoading,
    error
  } = useCart();
  const { createOrder } = useOrders();
  const router = useRouter();

  // Get cart items for this specific branch
  const cartItems = getBranchCartItems(shopSlug, branchSlug);

  const handleProceedToPayment = () => {
    if (cartItems.length > 0) {
      // Navigate to checkout page with branch context
      router.push(`/${shopType}/${shopSlug}/${branchSlug}/checkout`);
    }
  };

  const handleContinueShopping = () => {
    // Navigate back to the menu page
    router.push(`/${shopType}/${shopSlug}/${branchSlug}`);
  };

  const handleUpdateQuantity = async (itemId: string, quantity: number) => {
    try {
      await updateQuantity(itemId, quantity);
    } catch (error) {
      console.error('Failed to update quantity:', error);
      // TODO: Show toast notification for error
    }
  };

  const handleRemoveItem = async (itemId: string) => {
    try {
      await removeFromCart(itemId);
    } catch (error) {
      console.error('Failed to remove item:', error);
      // TODO: Show toast notification for error
    }
  };

  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const tax = subtotal * 0.08;
  const deliveryFee = 2.99;
  const discount = -2.00;
  const total = subtotal + tax + deliveryFee + discount;

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header shopType={shopType} shopSlug={shopSlug} branchSlug={branchSlug} />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <p className="text-foreground tracking-light text-[32px] font-bold leading-tight min-w-72">
                Your Cart - {branchSlug.replace(/-/g, ' ')}
              </p>
            </div>

            {/* Error Display */}
            {error && (
              <div className="mx-4 mb-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                <p className="text-destructive text-sm">{error}</p>
              </div>
            )}

            {/* Loading Overlay */}
            {isLoading && (
              <div className="mx-4 mb-4 p-4 bg-muted/50 rounded-lg">
                <p className="text-muted-foreground text-sm">Updating cart...</p>
              </div>
            )}

            {/* Cart Items */}
            {cartItems.length === 0 ? (
              <Card className="mx-4">
                <CardContent className="flex flex-col items-center justify-center py-16">
                  <ShoppingCart className="h-16 w-16 text-muted-foreground mb-4" />
                  <p className="text-foreground text-lg font-medium mb-2">Your cart is empty</p>
                  <p className="text-muted-foreground text-sm mb-6">Add some delicious items from our menu</p>
                  <Button onClick={handleContinueShopping}>
                    Browse Menu
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4 px-4">
                {cartItems.map((item) => (
                  <Card key={item.id}>
                    <CardContent className="flex items-center gap-4 p-4">
                      <div
                        className="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-14 flex-shrink-0"
                        style={{ backgroundImage: `url("${item.image}")` }}
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-foreground text-base font-medium leading-normal truncate">{item.name}</p>
                        <p className="text-muted-foreground text-sm font-normal leading-normal line-clamp-2">{item.description}</p>
                      </div>
                      <div className="flex items-center gap-4 flex-shrink-0">
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                            disabled={isLoading}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <span className="text-foreground text-sm font-medium min-w-[2rem] text-center">
                            {item.quantity}
                          </span>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                            disabled={isLoading}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex items-center gap-2">
                          <p className="text-foreground text-base font-normal leading-normal min-w-[4rem] text-right">
                            ${(item.price * item.quantity).toFixed(2)}
                          </p>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                            onClick={() => handleRemoveItem(item.id)}
                            disabled={isLoading}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {/* Order Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Order Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <p className="text-muted-foreground text-sm">Subtotal</p>
                      <p className="text-foreground text-sm">${subtotal.toFixed(2)}</p>
                    </div>
                    <div className="flex justify-between">
                      <p className="text-muted-foreground text-sm">Tax</p>
                      <p className="text-foreground text-sm">${tax.toFixed(2)}</p>
                    </div>
                    <div className="flex justify-between">
                      <p className="text-muted-foreground text-sm">Delivery Fee</p>
                      <p className="text-foreground text-sm">${deliveryFee.toFixed(2)}</p>
                    </div>
                    <div className="flex justify-between">
                      <p className="text-muted-foreground text-sm">Discount</p>
                      <p className="text-foreground text-sm">${discount.toFixed(2)}</p>
                    </div>
                    <Separator />
                    <div className="flex justify-between">
                      <p className="text-foreground text-base font-bold">Total</p>
                      <p className="text-foreground text-base font-bold">${total.toFixed(2)}</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Action Buttons */}
                <Card>
                  <CardFooter className="flex flex-col gap-3 p-6">
                    <Button
                      className="w-full bg-[#e58219] hover:bg-[#d4741a] text-white"
                      onClick={handleProceedToPayment}
                    >
                      Proceed to Payment
                    </Button>
                    <Button variant="outline" className="w-full" onClick={handleContinueShopping}>
                      Continue Shopping
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
