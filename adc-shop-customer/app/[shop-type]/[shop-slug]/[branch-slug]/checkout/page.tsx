// Checkout page for a specific shop branch
// URL: /[shop-type]/[shop-slug]/[branch-slug]/checkout

import { notFound } from 'next/navigation';
import { VALID_SHOP_TYPES, ShopType, isValidShopType, getShopTypeConfig } from '@/lib/config/shop-types';
import React from 'react';

interface BranchCheckoutPageProps {
  params: Promise<{
    'shop-type': string;
    'shop-slug': string;
    'branch-slug': string;
  }>;
}

export default async function BranchCheckoutPage({ params }: BranchCheckoutPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];
  const branchSlug = resolvedParams['branch-slug'];

  // Validate shop type
  if (!isValidShopType(shopType)) {
    notFound();
  }

  // For now, we'll import and use the existing checkout page component
  // In the future, we can create branch-specific checkout components
  const { default: CheckoutPage } = await import('../../../../checkout/page');

  // Create a wrapper component that includes branch context
  const BranchCheckoutWrapper = () => (
    <div>
      {/* TODO: Add branch-specific checkout logic */}
      {/* Pass shop and branch context to checkout component */}
      <CheckoutPage />
    </div>
  );

  return <BranchCheckoutWrapper />;
}

// Generate metadata for the page
export async function generateMetadata({ params }: BranchCheckoutPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];
  const branchSlug = resolvedParams['branch-slug'];

  if (!isValidShopType(shopType)) {
    return {
      title: 'Checkout - Shop Not Found',
      description: 'The requested shop could not be found.',
    };
  }

  const config = getShopTypeConfig(shopType);
  const shopName = shopSlug.replace(/-/g, ' ');
  const branchName = branchSlug.replace(/-/g, ' ');

  return {
    title: `Checkout - ${shopName} (${branchName}) | ${config.label}`,
    description: `Complete your order from ${shopName} - ${branchName} branch.`,
  };
}
