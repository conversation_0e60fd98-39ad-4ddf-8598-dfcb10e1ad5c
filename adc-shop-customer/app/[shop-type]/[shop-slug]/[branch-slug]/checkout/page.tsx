// Checkout page for a specific shop branch
// URL: /[shop-type]/[shop-slug]/[branch-slug]/checkout

import { notFound } from 'next/navigation';
import { ShopType, isValidShopType, getShopTypeConfig } from '@/lib/config/shop-types';
import BranchCheckoutForm from '@/components/checkout/BranchCheckoutForm';

interface BranchCheckoutPageProps {
  params: Promise<{
    'shop-type': string;
    'shop-slug': string;
    'branch-slug': string;
  }>;
}

export default async function BranchCheckoutPage({ params }: BranchCheckoutPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];
  const branchSlug = resolvedParams['branch-slug'];

  // Validate shop type
  if (!isValidShopType(shopType)) {
    notFound();
  }

  return (
    <BranchCheckoutForm
      shopType={shopType}
      shopSlug={shopSlug}
      branchSlug={branchSlug}
    />
  );
}

// Generate metadata for the page
export async function generateMetadata({ params }: BranchCheckoutPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];
  const branchSlug = resolvedParams['branch-slug'];

  if (!isValidShopType(shopType)) {
    return {
      title: 'Checkout - Shop Not Found',
      description: 'The requested shop could not be found.',
    };
  }

  const config = getShopTypeConfig(shopType);
  const shopName = shopSlug.replace(/-/g, ' ');
  const branchName = branchSlug.replace(/-/g, ' ');

  return {
    title: `Checkout - ${shopName} (${branchName}) | ${config.label}`,
    description: `Complete your order from ${shopName} - ${branchName} branch.`,
  };
}
