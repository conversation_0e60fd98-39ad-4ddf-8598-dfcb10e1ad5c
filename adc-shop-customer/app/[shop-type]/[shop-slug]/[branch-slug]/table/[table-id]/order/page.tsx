'use client';

import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useGetMenuQuery } from '@/lib/api/menuApi';
import { useGetShopQuery } from '@/lib/api/shopApi';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ShoppingCart, MapPin, Clock, Users } from 'lucide-react';
import Image from 'next/image';
import { useCart } from '@/hooks/useCart';
import { toast } from 'sonner';

interface TableOrderPageProps {}

export default function TableOrderPage({}: TableOrderPageProps) {
  const params = useParams();
  const router = useRouter();
  const shopSlug = params['shop-slug'] as string;
  const branchSlug = params['branch-slug'] as string;
  const tableId = params['table-id'] as string;
  
  const [selectedCategory, setSelectedCategory] = useState<string>('featured');
  const [tableInfo, setTableInfo] = useState<any>(null);
  
  const { cart, addToCart, getCartItemCount } = useCart();

  // Fetch shop and menu data
  const { data: shopData, isLoading: shopLoading } = useGetShopQuery({ 
    shopSlug, 
    branchSlug 
  });
  
  const { data: menuData, isLoading: menuLoading } = useGetMenuQuery({ 
    shopSlug, 
    branchSlug 
  });

  // Set table context in cart
  useEffect(() => {
    if (tableId && shopData?.branch) {
      const tableContext = {
        tableId,
        shopSlug,
        branchSlug,
        shopId: shopData.shop.id,
        branchId: shopData.branch.id,
        tableName: `Table ${tableId.slice(-4)}`, // Use last 4 chars as table display
      };
      
      // Store table context in localStorage for cart persistence
      localStorage.setItem('tableContext', JSON.stringify(tableContext));
      setTableInfo(tableContext);
    }
  }, [tableId, shopSlug, branchSlug, shopData]);

  const handleAddToCart = (item: any) => {
    if (!tableInfo) {
      toast.error('Table information not available');
      return;
    }

    addToCart({
      ...item,
      tableContext: tableInfo,
    });
    
    toast.success(`${item.name} added to cart`);
  };

  const goToCart = () => {
    router.push(`/food/${shopSlug}/${branchSlug}/table/${tableId}/cart`);
  };

  if (shopLoading || menuLoading) {
    return (
      <div className="min-h-screen bg-[#f4f2f1] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#e58219] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading menu...</p>
        </div>
      </div>
    );
  }

  if (!shopData || !menuData) {
    return (
      <div className="min-h-screen bg-[#f4f2f1] flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Restaurant not found</h2>
          <p className="text-gray-600">The restaurant or table you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  const categories = menuData.categories || [];
  const allCategories = [
    { id: 'featured', name: 'Featured', items: menuData.featured || [] },
    ...categories
  ];

  return (
    <div className="min-h-screen bg-[#f4f2f1]">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">{shopData.shop.name}</h1>
              <div className="flex items-center gap-4 mt-1 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4" />
                  <span>{shopData.branch.name}</span>
                </div>
                {tableInfo && (
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    <span>{tableInfo.tableName}</span>
                  </div>
                )}
              </div>
            </div>
            
            {/* Cart Button */}
            <Button 
              onClick={goToCart}
              className="bg-[#e58219] hover:bg-[#d4751a] text-white relative"
            >
              <ShoppingCart className="w-4 h-4 mr-2" />
              Cart
              {getCartItemCount() > 0 && (
                <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full min-w-[20px] h-5 flex items-center justify-center">
                  {getCartItemCount()}
                </Badge>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Table Info Banner */}
      {tableInfo && (
        <div className="bg-[#e5ccb2] border-b">
          <div className="max-w-4xl mx-auto px-4 py-3">
            <div className="flex items-center justify-center gap-2 text-[#8b4513]">
              <Users className="w-5 h-5" />
              <span className="font-medium">Ordering for {tableInfo.tableName}</span>
            </div>
          </div>
        </div>
      )}

      {/* Menu Content */}
      <div className="max-w-4xl mx-auto px-4 py-6">
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
          {/* Category Tabs */}
          <TabsList className="grid w-full grid-cols-4 mb-6 bg-white">
            {allCategories.slice(0, 4).map((category) => (
              <TabsTrigger 
                key={category.id} 
                value={category.id}
                className="data-[state=active]:bg-[#e5ccb2] data-[state=active]:text-[#8b4513]"
              >
                {category.name}
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Menu Items */}
          {allCategories.map((category) => (
            <TabsContent key={category.id} value={category.id}>
              <div className="grid gap-4 md:grid-cols-2">
                {category.items?.map((item: any) => (
                  <Card key={item.id} className="overflow-hidden hover:shadow-md transition-shadow">
                    <div className="flex">
                      {item.image && (
                        <div className="w-24 h-24 relative flex-shrink-0">
                          <Image
                            src={item.image}
                            alt={item.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                      )}
                      <CardContent className="flex-1 p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h3 className="font-semibold text-gray-800">{item.name}</h3>
                          <span className="text-lg font-bold text-[#e58219]">
                            ฿{item.price}
                          </span>
                        </div>
                        
                        {item.description && (
                          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                            {item.description}
                          </p>
                        )}
                        
                        <div className="flex justify-between items-center">
                          <div className="flex gap-1">
                            {item.tags?.map((tag: string) => (
                              <Badge key={tag} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          
                          <Button
                            onClick={() => handleAddToCart(item)}
                            size="sm"
                            className="bg-[#e58219] hover:bg-[#d4751a] text-white"
                          >
                            Add to Cart
                          </Button>
                        </div>
                      </CardContent>
                    </div>
                  </Card>
                ))}
              </div>
              
              {(!category.items || category.items.length === 0) && (
                <div className="text-center py-12">
                  <p className="text-gray-500">No items available in this category</p>
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  );
}
