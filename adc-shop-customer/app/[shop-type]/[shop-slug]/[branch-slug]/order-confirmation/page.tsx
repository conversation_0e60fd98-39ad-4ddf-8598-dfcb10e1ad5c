'use client';

import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, Users, MapPin, Phone, Mail, ArrowLeft, ShoppingBag } from 'lucide-react';
import Header from '@/components/Header';
import { ShopType, isValidShopType } from '@/lib/config/shop-types';
import { notFound } from 'next/navigation';

interface OrderData {
  id: string;
  orderNumber: string;
  status: string;
  total: number;
  estimatedTime: string;
  customerInfo: {
    name: string;
    phone: string;
    email: string;
  };
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  totals: {
    subtotal: number;
    tax: number;
    serviceCharge: number;
    total: number;
  };
  paymentStatus: string;
  createdAt: string;
}

interface BranchData {
  id: string;
  name: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  phone: string;
  shop: {
    name: string;
    logo?: string;
  };
}

export default function OrderConfirmationPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const shopType = params['shop-type'] as string;
  const shopSlug = params['shop-slug'] as string;
  const branchSlug = params['branch-slug'] as string;
  const paymentIntentId = searchParams.get('paymentIntentId');
  const orderId = searchParams.get('orderId');
  
  const [orderData, setOrderData] = useState<OrderData | null>(null);
  const [branchData, setBranchData] = useState<BranchData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Validate shop type
  if (!isValidShopType(shopType)) {
    notFound();
  }

  useEffect(() => {
    const fetchOrderData = async () => {
      try {
        setIsLoading(true);
        
        // Fetch branch data
        const branchResponse = await fetch(`/api/shops/slug/${shopSlug}/branches/slug/${branchSlug}`);
        if (!branchResponse.ok) {
          throw new Error('Failed to fetch branch data');
        }
        const branchResult = await branchResponse.json();
        setBranchData(branchResult.data);

        // For now, create mock order data since we don't have order retrieval API yet
        // In production, you would fetch the actual order using paymentIntentId or orderId
        const mockOrder: OrderData = {
          id: orderId || `order-${Date.now()}`,
          orderNumber: `ORD-${Date.now()}`,
          status: 'confirmed',
          total: 17.70,
          estimatedTime: '15-20 minutes',
          customerInfo: {
            name: 'Customer',
            phone: '+66 XX XXX XXXX',
            email: '<EMAIL>',
          },
          items: [
            { name: 'Menu Item', quantity: 1, price: 15.00 },
          ],
          totals: {
            subtotal: 15.00,
            tax: 1.20,
            serviceCharge: 1.50,
            total: 17.70,
          },
          paymentStatus: 'paid',
          createdAt: new Date().toISOString(),
        };
        
        setOrderData(mockOrder);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load order data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrderData();
  }, [shopSlug, branchSlug, paymentIntentId, orderId]);

  const handleNewOrder = () => {
    router.push(`/${shopType}/${shopSlug}/${branchSlug}`);
  };

  const handleViewOrders = () => {
    router.push(`/${shopType}/${shopSlug}/${branchSlug}/orders`);
  };

  const handleBackToMenu = () => {
    router.push(`/${shopType}/${shopSlug}/${branchSlug}`);
  };

  if (isLoading) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#e58219]"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <Card className="max-w-md">
              <CardContent className="pt-6 text-center">
                <h2 className="text-lg font-semibold mb-2">Error Loading Order</h2>
                <p className="text-muted-foreground mb-4">{error}</p>
                <Button onClick={handleBackToMenu}>Back to Menu</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (!orderData || !branchData) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <Card className="max-w-md">
              <CardContent className="pt-6 text-center">
                <h2 className="text-lg font-semibold mb-2">Order Not Found</h2>
                <p className="text-muted-foreground mb-4">We couldn't find your order details.</p>
                <Button onClick={handleBackToMenu}>Back to Menu</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#f4f2f0]">
      <div className="layout-container flex h-full grow flex-col">
        <Header />
        
        {/* Success Banner */}
        <div className="bg-green-50 border-b border-green-200">
          <div className="max-w-4xl mx-auto px-4 py-6">
            <div className="flex items-center justify-center gap-3 text-green-700">
              <CheckCircle className="w-8 h-8" />
              <div className="text-center">
                <h2 className="text-lg font-semibold">Order Confirmed!</h2>
                <p className="text-sm">Order #{orderData.orderNumber}</p>
                <p className="text-sm">Estimated time: {orderData.estimatedTime}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="grid gap-6 lg:grid-cols-3">
            {/* Order Details */}
            <div className="lg:col-span-2 space-y-6">
              {/* Order Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="w-5 h-5 text-[#e58219]" />
                    Order Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-3">
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      {orderData.status.charAt(0).toUpperCase() + orderData.status.slice(1)}
                    </Badge>
                    <span className="text-gray-600">
                      Your order is being prepared by the kitchen
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Order Items */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingBag className="w-5 h-5 text-[#e58219]" />
                    Order Items
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {orderData.items.map((item, index) => (
                      <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-0">
                        <div className="flex items-center gap-3">
                          <span className="bg-[#e5ccb2] text-[#8a745c] px-2 py-1 rounded text-sm font-medium">
                            {item.quantity}x
                          </span>
                          <span className="font-medium">{item.name}</span>
                        </div>
                        <span className="font-semibold">฿{item.price.toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Payment Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Payment Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>฿{orderData.totals.subtotal.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax (8%):</span>
                      <span>฿{orderData.totals.tax.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Service Charge:</span>
                      <span>฿{orderData.totals.serviceCharge.toFixed(2)}</span>
                    </div>
                    <div className="border-t pt-2 flex justify-between font-semibold text-lg">
                      <span>Total:</span>
                      <span className="text-[#e58219]">฿{orderData.totals.total.toFixed(2)}</span>
                    </div>
                    <div className="mt-3">
                      <Badge className="bg-green-100 text-green-800 border-green-200">
                        Payment {orderData.paymentStatus}
                      </Badge>
                      {paymentIntentId && (
                        <p className="text-sm text-gray-500 mt-1">
                          Payment ID: {paymentIntentId}
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Restaurant Info & Actions */}
            <div className="space-y-6">
              {/* Restaurant Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-5 h-5 text-[#e58219]" />
                    Restaurant Details
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      {branchData.shop.logo && (
                        <img
                          src={branchData.shop.logo}
                          alt={branchData.shop.name}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                      )}
                      <div>
                        <h3 className="font-semibold">{branchData.shop.name}</h3>
                        <p className="text-sm text-gray-600">{branchData.name} Branch</p>
                      </div>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-start gap-2">
                        <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                        <span>
                          {branchData.address.street}, {branchData.address.city}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-gray-400" />
                        <span>{branchData.phone}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button 
                  onClick={handleNewOrder}
                  className="w-full bg-[#e58219] hover:bg-[#d4751a] text-white"
                >
                  Order Again
                </Button>
                <Button 
                  onClick={handleViewOrders}
                  variant="outline"
                  className="w-full border-[#e5ccb2] text-[#8a745c] hover:bg-[#f4f2f0]"
                >
                  View All Orders
                </Button>
                <Button 
                  onClick={handleBackToMenu}
                  variant="ghost"
                  className="w-full text-[#8a745c] hover:bg-[#f4f2f0]"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Menu
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
