// Orders page for a specific shop branch
// URL: /[shop-type]/[shop-slug]/[branch-slug]/orders

import { notFound } from 'next/navigation';
import { VALID_SHOP_TYPES, ShopType, isValidShopType, getShopTypeConfig } from '@/lib/config/shop-types';
import ShopNavigation from '@/components/ShopNavigation';
import React from 'react';

interface BranchOrdersPageProps {
  params: Promise<{
    'shop-type': string;
    'shop-slug': string;
    'branch-slug': string;
  }>;
}

export default async function BranchOrdersPage({ params }: BranchOrdersPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];
  const branchSlug = resolvedParams['branch-slug'];

  // Validate shop type
  if (!isValidShopType(shopType)) {
    notFound();
  }

  // For now, we'll import and use the existing orders page component
  // In the future, we can create branch-specific order components
  const { default: OrdersPage } = await import('../../../../orders/page');

  return (
    <div>
      {/* Shop Navigation with branch context */}
      <ShopNavigation
        shopType={shopType}
        shopSlug={shopSlug}
        branchSlug={branchSlug}
        // TODO: Fetch actual shop and branch data and pass it here
        // shopName={shop?.name}
        // branchName={branch?.name}
        // shopRating={shop?.rating}
        // shopAddress={branch?.address}
        // shopPhone={branch?.phone}
        // isOpen={branch?.is_open}
      />

      {/* Orders content - TODO: Filter orders by branch */}
      <OrdersPage />
    </div>
  );
}

// Generate metadata for the page
export async function generateMetadata({ params }: BranchOrdersPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];
  const branchSlug = resolvedParams['branch-slug'];

  if (!isValidShopType(shopType)) {
    return {
      title: 'Orders - Shop Not Found',
      description: 'The requested shop could not be found.',
    };
  }

  const config = getShopTypeConfig(shopType);
  const shopName = shopSlug.replace(/-/g, ' ');
  const branchName = branchSlug.replace(/-/g, ' ');

  return {
    title: `Orders - ${shopName} (${branchName}) | ${config.label}`,
    description: `View your orders from ${shopName} - ${branchName} branch.`,
  };
}
