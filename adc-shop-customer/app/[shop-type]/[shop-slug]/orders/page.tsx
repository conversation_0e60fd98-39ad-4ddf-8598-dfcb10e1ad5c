// Legacy orders page for a specific shop - redirects to main branch
// URL: /[shop-type]/[shop-slug]/orders

import { notFound, redirect } from 'next/navigation';
import { VALID_SHOP_TYPES, ShopType, isValidShopType } from '@/lib/config/shop-types';

interface ShopOrdersPageProps {
  params: Promise<{
    'shop-type': string;
    'shop-slug': string;
  }>;
}

export default async function ShopOrdersPage({ params }: ShopOrdersPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];

  // Validate shop type
  if (!isValidShopType(shopType)) {
    notFound();
  }

  // Redirect to the main branch orders page
  // TODO: In the future, fetch the actual default branch or show branch selection
  redirect(`/${shopType}/${shopSlug}/main/orders`);
}

// Generate metadata for the page
export async function generateMetadata({ params }: ShopOrdersPageProps) {
  const resolvedParams = await params;
  const shopType = resolvedParams['shop-type'] as ShopType;
  const shopSlug = resolvedParams['shop-slug'];

  if (!isValidShopType(shopType)) {
    return {
      title: 'Orders - Shop Not Found',
      description: 'The requested shop could not be found.',
    };
  }

  const config = getShopTypeConfig(shopType);
  const shopName = shopSlug.replace(/-/g, ' ');

  return {
    title: `Orders - ${shopName} | ${config.label}`,
    description: `View your orders from ${shopName}.`,
  };
}
