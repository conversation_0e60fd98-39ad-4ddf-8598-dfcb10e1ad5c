'use client';

import { useState, useCallback } from 'react';
import GoogleMapReact from 'google-map-react';
import { MapPin, Star } from 'lucide-react';

// Restaurant marker component
const RestaurantMarker = ({
  shop,
  onClick,
  ...props
}: {
  shop: ShopLocation;
  onClick: (shop: ShopLocation) => void;
  lat?: number;
  lng?: number;
}) => (
  <div
    className="relative cursor-pointer transform -translate-x-1/2 -translate-y-full"
    onClick={() => onClick(shop)}
  >
    {/* Marker pin */}
    <div className="bg-orange-500 text-white p-2 rounded-full shadow-lg hover:bg-orange-600 transition-all duration-200 hover:scale-110 border-2 border-white">
      <MapPin className="h-5 w-5" />
    </div>

    {/* Info popup on hover */}
    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 hover:opacity-100 transition-opacity pointer-events-none z-10">
      <div className="bg-white rounded-lg shadow-xl p-3 min-w-48 border border-gray-200">
        <h3 className="font-semibold text-sm text-gray-900">{shop.name}</h3>
        <div className="flex items-center gap-1 mt-1">
          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
          <span className="text-xs text-gray-600">{shop.rating || 'N/A'}</span>
          <span className="text-xs text-gray-400">•</span>
          <span className="text-xs text-gray-600">{shop.cuisineType}</span>
          <span className="text-xs text-gray-400">•</span>
          <span className="text-xs text-gray-600">{shop.priceRange}</span>
        </div>
        <p className="text-xs text-gray-500 mt-1 line-clamp-2">{shop.address}</p>
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${shop.isOpen ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-xs text-gray-600">{shop.isOpen ? 'Open' : 'Closed'}</span>
          </div>
        </div>
        {/* Arrow pointing down */}
        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-white"></div>
      </div>
    </div>
  </div>
);

// User location marker
const UserMarker = ({ ...props }: { lat?: number; lng?: number }) => (
  <div className="relative transform -translate-x-1/2 -translate-y-1/2">
    <div className="bg-blue-500 border-4 border-white rounded-full shadow-lg w-6 h-6 flex items-center justify-center">
      <div className="w-3 h-3 bg-white rounded-full flex items-center justify-center">
        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse" />
      </div>
    </div>
  </div>
);

// Shop location interface
export interface ShopLocation {
  id: string;
  slug?: string;
  name: string;
  description?: string;
  address: string;
  lat: number;
  lng: number;
  rating?: number;
  priceRange?: string;
  cuisineType?: string;
  isOpen?: boolean;
  distance?: number;
  image?: string;
  phone?: string;
  website?: string;
  branches?: any[]; // Add branches for URL generation
}

interface GoogleMapProps {
  shops: ShopLocation[];
  center?: { lat: number; lng: number };
  zoom?: number;
  height?: string;
  width?: string;
  showUserLocation?: boolean;
  onShopClick?: (shop: ShopLocation) => void;
  onMapClick?: (lat: number, lng: number) => void;
  className?: string;
}

export default function GoogleMapComponent({
  shops = [],
  center = { lat: 13.7563, lng: 100.5018 }, // Bangkok default
  zoom = 13,
  height = '400px',
  width = '100%',
  showUserLocation = true,
  onShopClick,
  onMapClick,
  className = '',
}: GoogleMapProps) {
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);

  // Get user location
  const getUserLocation = useCallback(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setUserLocation(location);
        },
        (error) => {
          console.warn('Could not get user location:', error);
        }
      );
    }
  }, []);

  // Handle map ready
  const handleApiLoaded = useCallback(() => {
    console.log('Google Maps API loaded successfully');
    setMapLoaded(true);

    // Get user location when map is ready
    if (showUserLocation) {
      getUserLocation();
    }
  }, [showUserLocation, getUserLocation]);

  // Handle shop marker click
  const handleShopClick = useCallback((shop: ShopLocation) => {
    console.log('Shop clicked:', shop);
    onShopClick?.(shop);
  }, [onShopClick]);

  // Handle map click
  const handleMapClick = useCallback((event: any) => {
    const { lat, lng } = event;
    console.log('Map clicked at:', lat, lng);
    onMapClick?.(lat, lng);
  }, [onMapClick]);

  // Map options
  const mapOptions = {
    zoomControl: true,
    mapTypeControl: false,
    scaleControl: true,
    streetViewControl: true,
    rotateControl: false,
    fullscreenControl: true,
    styles: [
      {
        featureType: 'poi',
        elementType: 'labels',
        stylers: [{ visibility: 'off' }]
      }
    ]
  };

  return (
    <div
      className={`relative ${className}`}
      style={{ height, width }}
    >
      <GoogleMapReact
        key={`${center.lat}-${center.lng}`}
        bootstrapURLKeys={{
          key: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '',
          libraries: ['places']
        }}
        defaultCenter={center}
        defaultZoom={zoom}
        options={mapOptions}
        onGoogleApiLoaded={handleApiLoaded}
        onClick={handleMapClick}
        yesIWantToUseGoogleMapApiInternals
      >
        {/* Restaurant markers */}
        {shops.map((shop) => (
          <RestaurantMarker
            key={shop.id}
            lat={shop.lat}
            lng={shop.lng}
            shop={shop}
            onClick={handleShopClick}
          />
        ))}

        {/* User location marker */}
        {showUserLocation && userLocation && (
          <UserMarker
            lat={userLocation.lat}
            lng={userLocation.lng}
          />
        )}
      </GoogleMapReact>

      {/* Loading overlay */}
      {!mapLoaded && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-600">Loading map...</p>
          </div>
        </div>
      )}

      {/* Map info overlay */}
      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-md">
        <p className="text-sm font-medium text-gray-800">
          {shops.length} restaurant{shops.length !== 1 ? 's' : ''} found
        </p>
      </div>
    </div>
  );
}
