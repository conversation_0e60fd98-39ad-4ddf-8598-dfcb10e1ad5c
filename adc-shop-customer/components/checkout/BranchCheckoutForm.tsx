"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, ShoppingCart, AlertCircle, Loader2 } from "lucide-react";
import Header from "@/components/Header";
import { useCart } from "@/lib/context/CartContext";
import { useOrders } from "@/lib/context/OrderContext";
import { ShopType } from '@/lib/config/shop-types';
import CustomerInfoForm from './CustomerInfoForm';
import OrderSummary from './OrderSummary';
import StripeConnectPayment from '@/components/payment/StripeConnectPayment';

interface BranchCheckoutFormProps {
  shopType: ShopType;
  shopSlug: string;
  branchSlug: string;
}

interface ShopBranch {
  id: string;
  name: string;
  slug: string;
  shop_id: string;
  email?: string;
  phone?: string;
  address: {
    street: string;
    city: string;
    state: string;
    zip_code: string;
    country: string;
  };
  settings: {
    tax_rate?: number;
    service_charge_rate?: number;
    payment_methods?: string[];
    online_ordering?: boolean;
    delivery_enabled?: boolean;
    pickup_enabled?: boolean;
  };
  shop: {
    id: string;
    name: string;
    slug: string;
    description: string;
    logo?: string;
    cover_image?: string;
  };
}

interface CustomerInfo {
  name: string;
  phone: string;
  email: string;
  orderType: 'dine_in' | 'takeaway' | 'delivery';
  notes?: string;
  tableId?: string;
}

interface PaymentIntent {
  clientSecret: string;
  paymentIntentId: string;
  connectedAccountId: string;
  amount: number;
  currency: string;
}

export default function BranchCheckoutForm({ shopType, shopSlug, branchSlug }: BranchCheckoutFormProps) {
  const router = useRouter();
  const { cartItems, getBranchCartItems, getBranchTotalPrice, clearBranchCart } = useCart();
  const { createOrder } = useOrders();

  const [branchData, setBranchData] = useState<ShopBranch | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    phone: '',
    email: '',
    orderType: 'takeaway',
    notes: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentIntent, setPaymentIntent] = useState<PaymentIntent | null>(null);
  const [showPayment, setShowPayment] = useState(false);

  // Get branch-specific cart items
  const branchCartItems = getBranchCartItems(shopSlug, branchSlug);
  const subtotal = getBranchTotalPrice(shopSlug, branchSlug);

  useEffect(() => {
    fetchBranchData();
  }, [shopSlug, branchSlug]);

  const fetchBranchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/shops/slug/${shopSlug}/branches/slug/${branchSlug}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch branch data');
      }

      const data = await response.json();
      
      if (data.success && data.data?.shop) {
        setBranchData(data.data.shop);
      } else {
        throw new Error(data.message || 'Branch not found');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load branch data');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateTotals = () => {
    const taxRate = branchData?.settings?.tax_rate || 0.08;
    const serviceChargeRate = branchData?.settings?.service_charge_rate || 0.10;
    
    const tax = subtotal * taxRate;
    const serviceCharge = customerInfo.orderType === 'dine_in' ? subtotal * serviceChargeRate : 0;
    const deliveryFee = customerInfo.orderType === 'delivery' ? 2.99 : 0;
    const total = subtotal + tax + serviceCharge + deliveryFee;

    return {
      subtotal,
      tax,
      serviceCharge,
      deliveryFee,
      total,
    };
  };

  const handleBackToCart = () => {
    router.push(`/${shopType}/${shopSlug}/${branchSlug}/cart`);
  };

  const handleCustomerInfoChange = (info: Partial<CustomerInfo>) => {
    setCustomerInfo(prev => ({ ...prev, ...info }));
  };

  const validateForm = (): string | null => {
    if (!customerInfo.name.trim()) return 'Please enter your name';
    if (!customerInfo.phone.trim()) return 'Please enter your phone number';
    if (!customerInfo.email.trim()) return 'Please enter your email address';
    if (branchCartItems.length === 0) return 'Your cart is empty';
    return null;
  };

  const handleSubmitOrder = async () => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    if (!branchData) {
      setError('Branch data not loaded');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const totals = calculateTotals();
      
      // Create order with payment
      const orderResult = await createOrder(branchCartItems, totals.total, {
        customerName: customerInfo.name,
        customerPhone: customerInfo.phone,
        customerEmail: customerInfo.email,
        tableId: customerInfo.tableId,
        orderType: customerInfo.orderType,
        paymentMethod: 'stripe_connect',
        notes: customerInfo.notes,
        branchId: branchData.id,
      });

      if (orderResult.requiresPayment && orderResult.paymentIntent) {
        // Show payment form
        setPaymentIntent(orderResult.paymentIntent);
        setShowPayment(true);
      } else {
        // Order created successfully without payment (e.g., cash payment)
        await clearBranchCart(shopSlug, branchSlug);
        router.push(`/${shopType}/${shopSlug}/${branchSlug}/order-confirmation?orderId=${orderResult.orderId}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create order');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePaymentSuccess = async (paymentIntentId: string) => {
    try {
      // Clear cart and redirect to confirmation
      await clearBranchCart(shopSlug, branchSlug);
      router.push(`/${shopType}/${shopSlug}/${branchSlug}/order-confirmation?paymentIntentId=${paymentIntentId}`);
    } catch (err) {
      setError('Payment succeeded but failed to clear cart. Please contact support.');
    }
  };

  const handlePaymentError = (errorMessage: string) => {
    setError(`Payment failed: ${errorMessage}`);
    setShowPayment(false);
    setPaymentIntent(null);
  };

  const handlePaymentCancel = () => {
    setShowPayment(false);
    setPaymentIntent(null);
  };

  if (isLoading) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <div className="flex items-center gap-3">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Loading checkout...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error && !branchData) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <Card className="max-w-md">
              <CardContent className="pt-6 text-center">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h2 className="text-lg font-semibold mb-2">Error Loading Checkout</h2>
                <p className="text-muted-foreground mb-4">{error}</p>
                <Button onClick={() => router.back()}>Go Back</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (branchCartItems.length === 0) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="flex flex-1 justify-center items-center">
            <Card className="max-w-md">
              <CardContent className="pt-6 text-center">
                <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h2 className="text-lg font-semibold mb-2">Your cart is empty</h2>
                <p className="text-muted-foreground mb-4">
                  Add some items from {branchData?.shop.name} - {branchData?.name} to proceed with checkout.
                </p>
                <Button onClick={() => router.push(`/${shopType}/${shopSlug}/${branchSlug}/menu`)}>
                  Browse Menu
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  // Show payment form if payment is required
  if (showPayment && paymentIntent) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-background">
        <div className="layout-container flex h-full grow flex-col">
          <Header />
          <div className="px-40 flex flex-1 justify-center py-5">
            <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
              <div className="flex flex-wrap justify-between gap-3 p-4">
                <div className="flex items-center gap-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handlePaymentCancel}
                    className="p-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                  <h1 className="text-foreground tracking-light text-[32px] font-bold leading-tight">
                    Payment
                  </h1>
                </div>
              </div>

              <div className="px-4">
                <StripeConnectPayment
                  paymentIntent={paymentIntent}
                  orderDetails={{
                    orderId: paymentIntent.paymentIntentId,
                    customerName: customerInfo.name,
                    total: calculateTotals().total,
                  }}
                  onPaymentSuccess={handlePaymentSuccess}
                  onPaymentError={handlePaymentError}
                  onCancel={handlePaymentCancel}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const totals = calculateTotals();

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background">
      <div className="layout-container flex h-full grow flex-col">
        <Header />

        {/* Main Content */}
        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToCart}
                  className="p-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <h1 className="text-foreground tracking-light text-[32px] font-bold leading-tight">
                  Checkout
                </h1>
              </div>
            </div>

            {/* Branch Info */}
            {branchData && (
              <div className="px-4 mb-6">
                <Card className="bg-[#f4f2f0] border-[#e5ccb2]">
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-4">
                      {branchData.shop.logo && (
                        <img
                          src={branchData.shop.logo}
                          alt={branchData.shop.name}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                      )}
                      <div>
                        <h3 className="text-[#181510] font-semibold text-lg">
                          {branchData.shop.name} - {branchData.name}
                        </h3>
                        <p className="text-[#8a745c] text-sm">
                          {branchData.address.street}, {branchData.address.city}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="px-4 mb-6">
                <Card className="border-red-200 bg-red-50">
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                      <p className="text-red-800 text-sm">{error}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 px-4">
              {/* Customer Information Form */}
              <div className="space-y-6">
                <CustomerInfoForm
                  customerInfo={customerInfo}
                  onChange={handleCustomerInfoChange}
                  branchSettings={branchData?.settings}
                />
              </div>

              {/* Order Summary */}
              <div className="space-y-6">
                <OrderSummary
                  items={branchCartItems}
                  totals={totals}
                  onSubmit={handleSubmitOrder}
                  isSubmitting={isSubmitting}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
