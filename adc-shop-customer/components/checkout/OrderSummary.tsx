"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ShoppingCart, Loader2, CreditCard } from "lucide-react";
import { CartItem } from "@/lib/context/CartContext";

interface OrderTotals {
  subtotal: number;
  tax: number;
  serviceCharge: number;
  deliveryFee: number;
  total: number;
}

interface OrderSummaryProps {
  items: CartItem[];
  totals: OrderTotals;
  onSubmit: () => void;
  isSubmitting: boolean;
}

export default function OrderSummary({ 
  items, 
  totals, 
  onSubmit, 
  isSubmitting 
}: OrderSummaryProps) {
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);

  return (
    <div className="space-y-6">
      {/* Order Items */}
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardHeader>
          <CardTitle className="text-[#181510] text-lg flex items-center gap-2">
            <ShoppingCart className="h-5 w-5 text-[#8a745c]" />
            Your Order ({itemCount} item{itemCount !== 1 ? 's' : ''})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {items.map((item) => (
              <div key={item.id} className="flex items-start gap-3">
                <div
                  className="bg-center bg-no-repeat aspect-square bg-cover rounded-lg size-16 flex-shrink-0"
                  style={{ backgroundImage: `url("${item.image}")` }}
                />
                <div className="flex-1 min-w-0">
                  <h4 className="text-[#181510] font-medium text-sm leading-tight">
                    {item.name}
                  </h4>
                  <p className="text-[#8a745c] text-xs mt-1 line-clamp-2">
                    {item.description}
                  </p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-[#8a745c] text-xs">
                      Qty: {item.quantity}
                    </span>
                    <span className="text-[#181510] font-semibold text-sm">
                      ${(item.price * item.quantity).toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Order Summary */}
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardHeader>
          <CardTitle className="text-[#181510] text-lg">Order Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {/* Subtotal */}
            <div className="flex justify-between items-center">
              <span className="text-[#8a745c] text-sm">Subtotal</span>
              <span className="text-[#181510] font-medium">
                ${totals.subtotal.toFixed(2)}
              </span>
            </div>

            {/* Tax */}
            <div className="flex justify-between items-center">
              <span className="text-[#8a745c] text-sm">Tax (8%)</span>
              <span className="text-[#181510] font-medium">
                ${totals.tax.toFixed(2)}
              </span>
            </div>

            {/* Service Charge (if applicable) */}
            {totals.serviceCharge > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-[#8a745c] text-sm">Service Charge (10%)</span>
                <span className="text-[#181510] font-medium">
                  ${totals.serviceCharge.toFixed(2)}
                </span>
              </div>
            )}

            {/* Delivery Fee (if applicable) */}
            {totals.deliveryFee > 0 && (
              <div className="flex justify-between items-center">
                <span className="text-[#8a745c] text-sm">Delivery Fee</span>
                <span className="text-[#181510] font-medium">
                  ${totals.deliveryFee.toFixed(2)}
                </span>
              </div>
            )}

            <Separator className="bg-[#e5e1dc]" />

            {/* Total */}
            <div className="flex justify-between items-center">
              <span className="text-[#181510] font-semibold text-lg">Total</span>
              <span className="text-[#181510] font-bold text-xl">
                ${totals.total.toFixed(2)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Information */}
      <Card className="bg-[#f0f9ff] border-[#bae6fd]">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <CreditCard className="h-5 w-5 text-[#0369a1] mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-[#0c4a6e] font-medium mb-2">Payment Method</h3>
              <p className="text-[#075985] text-sm mb-2">
                Secure payment processing via Stripe Connect
              </p>
              <ul className="text-[#0369a1] text-xs space-y-1">
                <li>• Credit and debit cards accepted</li>
                <li>• Your payment information is encrypted and secure</li>
                <li>• You'll receive an email receipt after payment</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardContent className="pt-6">
          <Button
            onClick={onSubmit}
            disabled={isSubmitting || items.length === 0}
            className="w-full bg-[#e58219] hover:bg-[#d4741a] text-white font-semibold py-4 text-lg h-auto"
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-5 w-5 animate-spin" />
                Processing Order...
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2">
                <CreditCard className="h-5 w-5" />
                Place Order - ${totals.total.toFixed(2)}
              </div>
            )}
          </Button>

          <p className="text-[#8a745c] text-xs text-center mt-3">
            By placing this order, you agree to our terms of service and privacy policy.
          </p>
        </CardContent>
      </Card>

      {/* Order Time Estimate */}
      <Card className="bg-[#fefce8] border-[#fde047]">
        <CardContent className="pt-6">
          <div className="text-center">
            <h3 className="text-[#713f12] font-medium mb-2">Estimated Preparation Time</h3>
            <p className="text-[#a16207] text-sm mb-2">
              Your order will be ready in approximately
            </p>
            <div className="text-[#713f12] font-bold text-2xl">
              15-20 minutes
            </div>
            <p className="text-[#a16207] text-xs mt-2">
              We'll send you updates via SMS and email
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
