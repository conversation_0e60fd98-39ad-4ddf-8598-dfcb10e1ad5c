"use client";

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { User, Mail, Phone, MessageSquare, Utensils, Package, Truck } from "lucide-react";

interface CustomerInfo {
  name: string;
  phone: string;
  email: string;
  orderType: 'dine_in' | 'takeaway' | 'delivery';
  notes?: string;
  tableId?: string;
}

interface BranchSettings {
  delivery_enabled?: boolean;
  pickup_enabled?: boolean;
  online_ordering?: boolean;
}

interface CustomerInfoFormProps {
  customerInfo: CustomerInfo;
  onChange: (info: Partial<CustomerInfo>) => void;
  branchSettings?: BranchSettings;
}

export default function CustomerInfoForm({ 
  customerInfo, 
  onChange, 
  branchSettings 
}: CustomerInfoFormProps) {
  const handleInputChange = (field: keyof CustomerInfo, value: string) => {
    onChange({ [field]: value });
  };

  const getAvailableOrderTypes = () => {
    const types = [
      {
        value: 'takeaway' as const,
        label: 'Takeaway',
        description: 'Pick up your order',
        icon: Package,
        enabled: branchSettings?.pickup_enabled !== false,
      },
      {
        value: 'dine_in' as const,
        label: 'Dine In',
        description: 'Eat at the restaurant',
        icon: Utensils,
        enabled: true, // Always available
      },
      {
        value: 'delivery' as const,
        label: 'Delivery',
        description: 'Deliver to your address',
        icon: Truck,
        enabled: branchSettings?.delivery_enabled === true,
      },
    ];

    return types.filter(type => type.enabled);
  };

  const availableOrderTypes = getAvailableOrderTypes();

  return (
    <div className="space-y-6">
      {/* Customer Information */}
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardHeader>
          <CardTitle className="text-[#181510] text-lg flex items-center gap-2">
            <User className="h-5 w-5 text-[#8a745c]" />
            Customer Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="customer-name" className="text-[#181510] font-medium">
              Full Name *
            </Label>
            <Input
              id="customer-name"
              type="text"
              placeholder="Enter your full name"
              value={customerInfo.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="bg-white border-[#e5e1dc] focus:border-[#e58219] focus:ring-[#e58219]"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="customer-phone" className="text-[#181510] font-medium">
              Phone Number *
            </Label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#8a745c]" />
              <Input
                id="customer-phone"
                type="tel"
                placeholder="Enter your phone number"
                value={customerInfo.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className="pl-10 bg-white border-[#e5e1dc] focus:border-[#e58219] focus:ring-[#e58219]"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="customer-email" className="text-[#181510] font-medium">
              Email Address *
            </Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[#8a745c]" />
              <Input
                id="customer-email"
                type="email"
                placeholder="Enter your email address"
                value={customerInfo.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="pl-10 bg-white border-[#e5e1dc] focus:border-[#e58219] focus:ring-[#e58219]"
                required
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Type */}
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardHeader>
          <CardTitle className="text-[#181510] text-lg">Order Type</CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup
            value={customerInfo.orderType}
            onValueChange={(value) => handleInputChange('orderType', value)}
            className="space-y-3"
          >
            {availableOrderTypes.map((type) => {
              const IconComponent = type.icon;
              return (
                <div key={type.value} className="flex items-center space-x-3">
                  <RadioGroupItem 
                    value={type.value} 
                    id={type.value}
                    className="border-[#e58219] text-[#e58219]"
                  />
                  <Label 
                    htmlFor={type.value} 
                    className="flex items-center gap-3 cursor-pointer flex-1 p-3 rounded-lg border border-[#e5e1dc] hover:bg-[#f4f2f0] transition-colors"
                  >
                    <IconComponent className="h-5 w-5 text-[#8a745c]" />
                    <div>
                      <div className="font-medium text-[#181510]">{type.label}</div>
                      <div className="text-sm text-[#8a745c]">{type.description}</div>
                    </div>
                  </Label>
                </div>
              );
            })}
          </RadioGroup>
        </CardContent>
      </Card>

      {/* Special Requests */}
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardHeader>
          <CardTitle className="text-[#181510] text-lg flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-[#8a745c]" />
            Special Requests
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="special-notes" className="text-[#181510] font-medium">
              Additional Notes (Optional)
            </Label>
            <Textarea
              id="special-notes"
              placeholder="Any special requests or dietary requirements..."
              value={customerInfo.notes || ''}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              className="bg-white border-[#e5e1dc] focus:border-[#e58219] focus:ring-[#e58219] min-h-[100px]"
              rows={4}
            />
            <p className="text-xs text-[#8a745c]">
              Let us know about any allergies, dietary preferences, or special instructions.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Delivery Address (if delivery is selected) */}
      {customerInfo.orderType === 'delivery' && (
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader>
            <CardTitle className="text-[#181510] text-lg flex items-center gap-2">
              <Truck className="h-5 w-5 text-[#8a745c]" />
              Delivery Address
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="delivery-address" className="text-[#181510] font-medium">
                Full Address *
              </Label>
              <Textarea
                id="delivery-address"
                placeholder="Enter your complete delivery address..."
                className="bg-white border-[#e5e1dc] focus:border-[#e58219] focus:ring-[#e58219]"
                rows={3}
                required
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="delivery-city" className="text-[#181510] font-medium">
                  City *
                </Label>
                <Input
                  id="delivery-city"
                  type="text"
                  placeholder="City"
                  className="bg-white border-[#e5e1dc] focus:border-[#e58219] focus:ring-[#e58219]"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="delivery-postal" className="text-[#181510] font-medium">
                  Postal Code *
                </Label>
                <Input
                  id="delivery-postal"
                  type="text"
                  placeholder="Postal Code"
                  className="bg-white border-[#e5e1dc] focus:border-[#e58219] focus:ring-[#e58219]"
                  required
                />
              </div>
            </div>
            <div className="bg-[#fff3cd] border border-[#ffeaa7] rounded-lg p-3">
              <p className="text-sm text-[#856404]">
                <strong>Delivery Fee:</strong> $2.99 will be added to your order total.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Table Information (if dine-in is selected) */}
      {customerInfo.orderType === 'dine_in' && (
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader>
            <CardTitle className="text-[#181510] text-lg flex items-center gap-2">
              <Utensils className="h-5 w-5 text-[#8a745c]" />
              Table Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="table-number" className="text-[#181510] font-medium">
                Table Number (Optional)
              </Label>
              <Input
                id="table-number"
                type="text"
                placeholder="Enter table number if known"
                value={customerInfo.tableId || ''}
                onChange={(e) => handleInputChange('tableId', e.target.value)}
                className="bg-white border-[#e5e1dc] focus:border-[#e58219] focus:ring-[#e58219]"
              />
              <p className="text-xs text-[#8a745c]">
                If you're already seated, please provide your table number. Otherwise, staff will assign you a table.
              </p>
            </div>
            <div className="mt-4 bg-[#d1ecf1] border border-[#bee5eb] rounded-lg p-3">
              <p className="text-sm text-[#0c5460]">
                <strong>Service Charge:</strong> A 10% service charge will be added for dine-in orders.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
