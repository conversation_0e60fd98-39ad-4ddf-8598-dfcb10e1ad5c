'use client';

import React from 'react';
import { MapPin, Navigation, Settings } from 'lucide-react';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';

interface MapPlaceholderProps {
  height?: string;
  width?: string;
  className?: string;
}

const MapPlaceholder: React.FC<MapPlaceholderProps> = ({
  height = '400px',
  width = '100%',
  className = '',
}) => {
  return (
    <div 
      className={`relative bg-gradient-to-br from-blue-50 to-green-50 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center ${className}`}
      style={{ height, width }}
    >
      <div className="text-center p-8 max-w-md">
        <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <MapPin className="w-8 h-8 text-orange-600" />
        </div>
        
        <h3 className="text-xl font-semibold text-gray-800 mb-2">
          Google Maps Integration Ready
        </h3>
        
        <p className="text-gray-600 mb-4 text-sm">
          To enable the interactive map, you need to set up your Google Maps API key.
        </p>
        
        <div className="space-y-3">
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-xs font-bold">1</span>
                </div>
                <div className="text-left">
                  <p className="text-sm font-medium text-gray-800">Run the setup script</p>
                  <code className="text-xs bg-gray-100 px-2 py-1 rounded mt-1 block">
                    ./scripts/setup-google-maps.sh
                  </code>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-white/80 backdrop-blur-sm">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-green-600 text-xs font-bold">2</span>
                </div>
                <div className="text-left">
                  <p className="text-sm font-medium text-gray-800">Restart the development server</p>
                  <code className="text-xs bg-gray-100 px-2 py-1 rounded mt-1 block">
                    npm run dev
                  </code>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <Settings className="w-4 h-4 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-800">Features Available</span>
          </div>
          <ul className="text-xs text-yellow-700 space-y-1 text-left">
            <li>• Interactive restaurant map</li>
            <li>• Real-time location detection</li>
            <li>• Turn-by-turn directions</li>
            <li>• Distance calculations</li>
            <li>• Address search & geocoding</li>
          </ul>
        </div>
      </div>
      
      {/* Decorative map elements */}
      <div className="absolute top-4 left-4 w-3 h-3 bg-red-400 rounded-full opacity-60"></div>
      <div className="absolute top-8 right-8 w-2 h-2 bg-blue-400 rounded-full opacity-60"></div>
      <div className="absolute bottom-6 left-8 w-4 h-4 bg-green-400 rounded-full opacity-60"></div>
      <div className="absolute bottom-4 right-4 w-2 h-2 bg-orange-400 rounded-full opacity-60"></div>
      
      {/* Grid pattern overlay */}
      <div 
        className="absolute inset-0 opacity-10"
        style={{
          backgroundImage: `
            linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px'
        }}
      ></div>
    </div>
  );
};

export default MapPlaceholder;
