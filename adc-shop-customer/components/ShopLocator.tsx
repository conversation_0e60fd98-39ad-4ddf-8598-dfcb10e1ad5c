'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Search, MapPin, Navigation, Filter, Star } from 'lucide-react';
import GoogleMap from './GoogleMap';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import {
  ShopLocation,
  getCurrentLocation,
  calculateDistance,
  formatDistance,
  geocodeAddress
} from '@/lib/google-maps';

interface ShopLocatorProps {
  onShopSelect?: (shop: ShopLocation) => void;
  className?: string;
}

// Mock shop data - replace with actual API call
const mockShops: ShopLocation[] = [
  {
    id: '1',
    name: '<PERSON><PERSON>',
    address: '392/14 Siam Square Soi 5, Pathum Wan, Bangkok',
    lat: 13.7460,
    lng: 100.5340,
    rating: 4.5,
    priceRange: '฿฿',
    cuisineType: 'Thai',
    isOpen: true,
  },
  {
    id: '2',
    name: '<PERSON><PERSON> Sam<PERSON>',
    address: '313 Maha Chai Rd, Samran Rat, Phra Nakhon, Bangkok',
    lat: 13.7540,
    lng: 100.5020,
    rating: 4.3,
    priceRange: '฿',
    cuisineType: 'Thai',
    isOpen: true,
  },
  {
    id: '3',
    name: 'Gaggan Anand',
    address: '68/1 Soi Langsuan, Ploenchit Rd, Lumpini, Pathum Wan, Bangkok',
    lat: 13.7440,
    lng: 100.5420,
    rating: 4.8,
    priceRange: '฿฿฿฿',
    cuisineType: 'Modern Indian',
    isOpen: false,
  },
  {
    id: '4',
    name: 'Jay Fai',
    address: '327 Maha Chai Rd, Samran Rat, Phra Nakhon, Bangkok',
    lat: 13.7550,
    lng: 100.5010,
    rating: 4.6,
    priceRange: '฿฿฿',
    cuisineType: 'Thai Seafood',
    isOpen: true,
  },
  {
    id: '5',
    name: 'Chinatown Yaowarat',
    address: 'Yaowarat Rd, Samphanthawong, Bangkok',
    lat: 13.7398,
    lng: 100.5067,
    rating: 4.4,
    priceRange: '฿฿',
    cuisineType: 'Chinese',
    isOpen: true,
  },
  {
    id: '6',
    name: 'Chatuchak Weekend Market Food',
    address: '587, 10 Kamphaeng Phet 2 Rd, Chatuchak, Bangkok',
    lat: 13.7990,
    lng: 100.5500,
    rating: 4.2,
    priceRange: '฿',
    cuisineType: 'Street Food',
    isOpen: true,
  },
  {
    id: '7',
    name: 'Blue Elephant',
    address: '233 S Sathorn Rd, Thung Maha Mek, Sathon, Bangkok',
    lat: 13.7200,
    lng: 100.5300,
    rating: 4.5,
    priceRange: '฿฿฿',
    cuisineType: 'Royal Thai',
    isOpen: true,
  },
  {
    id: '8',
    name: 'Terminal 21 Food Court',
    address: '88 Soi Sukhumvit 19, Khlong Toei Nuea, Watthana, Bangkok',
    lat: 13.7380,
    lng: 100.5610,
    rating: 4.1,
    priceRange: '฿',
    cuisineType: 'International',
    isOpen: true,
  },
];

const ShopLocator: React.FC<ShopLocatorProps> = ({ onShopSelect, className = '' }) => {
  const [shops, setShops] = useState<ShopLocation[]>(mockShops);
  const [filteredShops, setFilteredShops] = useState<ShopLocation[]>(mockShops);
  const [searchQuery, setSearchQuery] = useState('');
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [selectedShop, setSelectedShop] = useState<ShopLocation | null>(null);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [sortBy, setSortBy] = useState<'distance' | 'rating' | 'name'>('distance');
  const [filterOpen, setFilterOpen] = useState(false);
  const [cuisineFilter, setCuisineFilter] = useState<string>('');
  const [priceFilter, setPriceFilter] = useState<string>('');

  // Get user location
  const getUserLocation = useCallback(async () => {
    setIsLoadingLocation(true);
    try {
      const position = await getCurrentLocation();
      const location = {
        lat: position.coords.latitude,
        lng: position.coords.longitude,
      };
      setUserLocation(location);

      // Calculate distances for all shops
      const shopsWithDistance = shops.map(shop => ({
        ...shop,
        distance: calculateDistance(location, { lat: shop.lat, lng: shop.lng }),
      }));

      setShops(shopsWithDistance);
      setFilteredShops(shopsWithDistance);
    } catch (error) {
      console.error('Error getting location:', error);
    } finally {
      setIsLoadingLocation(false);
    }
  }, [shops]);

  // Search shops by address
  const searchByAddress = useCallback(async (address: string) => {
    if (!address.trim()) return;

    try {
      const location = await geocodeAddress(address);
      if (location) {
        setUserLocation({ lat: location.lat(), lng: location.lng() });

        // Calculate distances from searched location
        const shopsWithDistance = shops.map(shop => ({
          ...shop,
          distance: calculateDistance(
            { lat: location.lat(), lng: location.lng() },
            { lat: shop.lat, lng: shop.lng }
          ),
        }));

        setShops(shopsWithDistance);
        setFilteredShops(shopsWithDistance);
      }
    } catch (error) {
      console.error('Error searching address:', error);
    }
  }, [shops]);

  // Filter and sort shops
  const filterAndSortShops = useCallback(() => {
    let filtered = [...shops];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(shop =>
        shop.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        shop.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        shop.cuisineType?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply cuisine filter
    if (cuisineFilter) {
      filtered = filtered.filter(shop => shop.cuisineType === cuisineFilter);
    }

    // Apply price filter
    if (priceFilter) {
      filtered = filtered.filter(shop => shop.priceRange === priceFilter);
    }

    // Sort shops
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'distance':
          return (a.distance || 0) - (b.distance || 0);
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

    setFilteredShops(filtered);
  }, [shops, searchQuery, cuisineFilter, priceFilter, sortBy]);

  // Handle shop selection
  const handleShopSelect = useCallback((shop: ShopLocation) => {
    setSelectedShop(shop);
    if (onShopSelect) {
      onShopSelect(shop);
    }
  }, [onShopSelect]);

  // Get unique cuisine types for filter
  const cuisineTypes = [...new Set(shops.map(shop => shop.cuisineType).filter(Boolean))];
  const priceRanges = [...new Set(shops.map(shop => shop.priceRange).filter(Boolean))];

  // Effects
  useEffect(() => {
    filterAndSortShops();
  }, [filterAndSortShops]);

  useEffect(() => {
    // Auto-get user location on mount
    getUserLocation();
  }, []);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Find Restaurants Near You
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search Bar */}
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search restaurants or enter address..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    searchByAddress(searchQuery);
                  }
                }}
                className="pl-10"
              />
            </div>
            <Button
              onClick={() => searchByAddress(searchQuery)}
              variant="outline"
            >
              Search
            </Button>
            <Button
              onClick={getUserLocation}
              disabled={isLoadingLocation}
              variant="outline"
            >
              <Navigation className="w-4 h-4 mr-2" />
              {isLoadingLocation ? 'Locating...' : 'My Location'}
            </Button>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-2 items-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilterOpen(!filterOpen)}
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'distance' | 'rating' | 'name')}
              className="px-3 py-1 border rounded text-sm"
            >
              <option value="distance">Sort by Distance</option>
              <option value="rating">Sort by Rating</option>
              <option value="name">Sort by Name</option>
            </select>

            {filterOpen && (
              <div className="flex gap-2">
                <select
                  value={cuisineFilter}
                  onChange={(e) => setCuisineFilter(e.target.value)}
                  className="px-3 py-1 border rounded text-sm"
                >
                  <option value="">All Cuisines</option>
                  {cuisineTypes.map(cuisine => (
                    <option key={cuisine} value={cuisine}>{cuisine}</option>
                  ))}
                </select>

                <select
                  value={priceFilter}
                  onChange={(e) => setPriceFilter(e.target.value)}
                  className="px-3 py-1 border rounded text-sm"
                >
                  <option value="">All Prices</option>
                  {priceRanges.map(price => (
                    <option key={price} value={price}>{price}</option>
                  ))}
                </select>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Map and Results */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Map */}
        <div className="order-2 lg:order-1">
          <GoogleMap
            shops={filteredShops}
            center={userLocation || undefined}
            height="500px"
            showUserLocation={true}
            onShopClick={handleShopSelect}
            className="rounded-lg border"
          />
        </div>

        {/* Shop List */}
        <div className="order-1 lg:order-2 space-y-3 max-h-[500px] overflow-y-auto">
          <h3 className="font-semibold text-lg">
            {filteredShops.length} Restaurant{filteredShops.length !== 1 ? 's' : ''} Found
          </h3>

          {filteredShops.map((shop) => (
            <Card
              key={shop.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                selectedShop?.id === shop.id ? 'ring-2 ring-orange-500' : ''
              }`}
              onClick={() => handleShopSelect(shop)}
            >
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-semibold text-lg">{shop.name}</h4>
                  {shop.isOpen !== undefined && (
                    <Badge variant={shop.isOpen ? "default" : "secondary"}>
                      {shop.isOpen ? 'Open' : 'Closed'}
                    </Badge>
                  )}
                </div>

                <p className="text-sm text-gray-600 mb-2">{shop.address}</p>

                <div className="flex items-center gap-4 text-sm">
                  {shop.rating && (
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span>{shop.rating.toFixed(1)}</span>
                    </div>
                  )}

                  {shop.cuisineType && (
                    <Badge variant="outline">{shop.cuisineType}</Badge>
                  )}

                  {shop.priceRange && (
                    <span className="text-gray-600">{shop.priceRange}</span>
                  )}

                  {shop.distance && (
                    <span className="text-gray-600">
                      {formatDistance(shop.distance)}
                    </span>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}

          {filteredShops.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <MapPin className="w-12 h-12 mx-auto mb-2 opacity-50" />
              <p>No restaurants found matching your criteria.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ShopLocator;
