import * as React from "react"
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react"

import { cn } from "@/lib/utils"
import { ButtonProps, buttonVariants } from "@/components/ui/button"

const Pagination = ({ className, ...props }: React.ComponentProps<"nav">) => (
  <nav
    role="navigation"
    aria-label="pagination"
    className={cn("mx-auto flex w-full justify-center", className)}
    {...props}
  />
)
Pagination.displayName = "Pagination"

const PaginationContent = React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    className={cn("flex flex-row items-center gap-1", className)}
    {...props}
  />
))
PaginationContent.displayName = "PaginationContent"

const PaginationItem = React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ className, ...props }, ref) => (
  <li ref={ref} className={cn("", className)} {...props} />
))
PaginationItem.displayName = "PaginationItem"

type PaginationLinkProps = {
  isActive?: boolean
} & Pick<ButtonProps, "size"> &
  React.ComponentProps<"a">

const PaginationLink = ({
  className,
  isActive,
  size = "icon",
  ...props
}: PaginationLinkProps) => (
  <a
    aria-current={isActive ? "page" : undefined}
    className={cn(
      buttonVariants({
        variant: isActive ? "outline" : "ghost",
        size,
      }),
      className
    )}
    {...props}
  />
)
PaginationLink.displayName = "PaginationLink"

const PaginationPrevious = ({
  className,
  ...props
}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label="Go to previous page"
    size="default"
    className={cn("gap-1 pl-2.5", className)}
    {...props}
  >
    <ChevronLeft className="h-4 w-4" />
    <span>Previous</span>
  </PaginationLink>
)
PaginationPrevious.displayName = "PaginationPrevious"

const PaginationNext = ({
  className,
  ...props
}: React.ComponentProps<typeof PaginationLink>) => (
  <PaginationLink
    aria-label="Go to next page"
    size="default"
    className={cn("gap-1 pr-2.5", className)}
    {...props}
  >
    <span>Next</span>
    <ChevronRight className="h-4 w-4" />
  </PaginationLink>
)
PaginationNext.displayName = "PaginationNext"

const PaginationEllipsis = ({
  className,
  ...props
}: React.ComponentProps<"span">) => (
  <span
    aria-hidden
    className={cn("flex h-9 w-9 items-center justify-center", className)}
    {...props}
  >
    <MoreHorizontal className="h-4 w-4" />
    <span className="sr-only">More pages</span>
  </span>
)
PaginationEllipsis.displayName = "PaginationEllipsis"

// Center Pagination Component
interface CenterPaginationProps {
  currentPage: number;
  totalPages: number;
  centerPages: number[];
  showFirst: boolean;
  showLast: boolean;
  onPageChange: (page: number) => void;
  canGoPrev: boolean;
  canGoNext: boolean;
  className?: string;
}

const CenterPagination = ({
  currentPage,
  totalPages,
  centerPages,
  showFirst,
  showLast,
  onPageChange,
  canGoPrev,
  canGoNext,
  className,
}: CenterPaginationProps) => {
  return (
    <Pagination className={className}>
      <PaginationContent>
        {/* Previous button */}
        <PaginationItem>
          <PaginationPrevious
            onClick={() => canGoPrev && onPageChange(currentPage - 1)}
            className={cn(!canGoPrev && "pointer-events-none opacity-50")}
          />
        </PaginationItem>

        {/* First page */}
        {showFirst && (
          <>
            <PaginationItem>
              <PaginationLink
                onClick={() => onPageChange(1)}
                isActive={currentPage === 1}
              >
                1
              </PaginationLink>
            </PaginationItem>
            {centerPages.length > 0 && centerPages[0] > 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}
          </>
        )}

        {/* Center pages */}
        {centerPages.map((page) => (
          <PaginationItem key={page}>
            <PaginationLink
              onClick={() => onPageChange(page)}
              isActive={currentPage === page}
            >
              {page}
            </PaginationLink>
          </PaginationItem>
        ))}

        {/* Last page */}
        {showLast && (
          <>
            {centerPages.length > 0 && centerPages[centerPages.length - 1] < totalPages - 1 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}
            <PaginationItem>
              <PaginationLink
                onClick={() => onPageChange(totalPages)}
                isActive={currentPage === totalPages}
              >
                {totalPages}
              </PaginationLink>
            </PaginationItem>
          </>
        )}

        {/* Next button */}
        <PaginationItem>
          <PaginationNext
            onClick={() => canGoNext && onPageChange(currentPage + 1)}
            className={cn(!canGoNext && "pointer-events-none opacity-50")}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
};

// Pagination Info Component
interface PaginationInfoProps {
  startItem: number;
  endItem: number;
  totalItems: number;
  className?: string;
}

const PaginationInfo = ({ startItem, endItem, totalItems, className }: PaginationInfoProps) => (
  <div className={cn("text-sm text-muted-foreground", className)}>
    Showing {startItem} to {endItem} of {totalItems} results
  </div>
);

// Page Size Selector Component
interface PageSizeSelectorProps {
  currentSize: number;
  options: number[];
  onSizeChange: (size: number) => void;
  className?: string;
}

const PageSizeSelector = ({ currentSize, options, onSizeChange, className }: PageSizeSelectorProps) => (
  <div className={cn("flex items-center space-x-2", className)}>
    <span className="text-sm text-muted-foreground">Show</span>
    <select
      value={currentSize}
      onChange={(e) => onSizeChange(Number(e.target.value))}
      className="border border-input bg-background px-3 py-1 text-sm rounded-md"
    >
      {options.map((size) => (
        <option key={size} value={size}>
          {size}
        </option>
      ))}
    </select>
    <span className="text-sm text-muted-foreground">per page</span>
  </div>
);

export {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  CenterPagination,
  PaginationInfo,
  PageSizeSelector,
}
