"use client";

import { useState } from 'react';
import {
  useStripe,
  useElements,
  PaymentElement,
  AddressElement,
} from '@stripe/react-stripe-js';
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { CreditCard, User, Mail, Phone } from "lucide-react";
import { PaymentIntent } from "@/lib/services/paymentService";
import { useOrders } from "@/lib/context/OrderContext";
import { useCart } from "@/lib/context/CartContext";

interface CheckoutFormProps {
  paymentIntent: PaymentIntent;
  onSuccess: () => void;
  onError: (error: string) => void;
}

export default function CheckoutForm({ paymentIntent, onSuccess, onError }: CheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const { createOrder } = useOrders();
  const { cartItems, getTotalPrice } = useCart();

  const [isProcessing, setIsProcessing] = useState(false);
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    email: '',
    phone: '',
  });

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);

    try {
      // Validate customer info
      if (!customerInfo.name || !customerInfo.email) {
        onError('Please fill in all required fields');
        setIsProcessing(false);
        return;
      }

      // Confirm the payment
      const { error, paymentIntent: confirmedPaymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/orders?payment=success`,
          receipt_email: customerInfo.email,
        },
        redirect: 'if_required',
      });

      if (error) {
        onError(error.message || 'Payment failed');
      } else if (confirmedPaymentIntent && confirmedPaymentIntent.status === 'succeeded') {
        // Payment succeeded, create the order
        const total = getTotalPrice() + (getTotalPrice() * 0.08) + 2.99 - 2.00; // Include tax, delivery, discount
        const orderId = createOrder(cartItems, total, {
          paymentStatus: 'paid',
          paymentMethod: typeof confirmedPaymentIntent.payment_method === 'object'
            ? confirmedPaymentIntent.payment_method?.type || 'card'
            : 'card',
          paymentIntentId: confirmedPaymentIntent.id,
        });

        // Call success callback
        onSuccess();
      } else {
        onError('Payment was not completed');
      }
    } catch (err) {
      onError('An unexpected error occurred');
      console.error('Payment error:', err);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCustomerInfoChange = (field: string, value: string) => {
    setCustomerInfo(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Customer Information */}
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardHeader>
          <CardTitle className="text-[#181510] text-lg flex items-center gap-2">
            <User className="h-5 w-5 text-[#8a745c]" />
            Customer Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <div>
              <Label htmlFor="name" className="text-[#181510] text-sm font-medium">
                Full Name *
              </Label>
              <Input
                id="name"
                type="text"
                value={customerInfo.name}
                onChange={(e) => handleCustomerInfoChange('name', e.target.value)}
                className="mt-1 bg-white border-[#e5e1dc] focus:border-[#e58219] focus:ring-[#e58219]"
                placeholder="Enter your full name"
                required
              />
            </div>
            <div>
              <Label htmlFor="email" className="text-[#181510] text-sm font-medium">
                Email Address *
              </Label>
              <Input
                id="email"
                type="email"
                value={customerInfo.email}
                onChange={(e) => handleCustomerInfoChange('email', e.target.value)}
                className="mt-1 bg-white border-[#e5e1dc] focus:border-[#e58219] focus:ring-[#e58219]"
                placeholder="Enter your email"
                required
              />
            </div>
            <div>
              <Label htmlFor="phone" className="text-[#181510] text-sm font-medium">
                Phone Number
              </Label>
              <Input
                id="phone"
                type="tel"
                value={customerInfo.phone}
                onChange={(e) => handleCustomerInfoChange('phone', e.target.value)}
                className="mt-1 bg-white border-[#e5e1dc] focus:border-[#e58219] focus:ring-[#e58219]"
                placeholder="Enter your phone number"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Billing Address */}
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardHeader>
          <CardTitle className="text-[#181510] text-lg">Billing Address</CardTitle>
        </CardHeader>
        <CardContent>
          <AddressElement
            options={{
              mode: 'billing',
              allowedCountries: ['US'],
            }}
          />
        </CardContent>
      </Card>

      {/* Payment Method */}
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardHeader>
          <CardTitle className="text-[#181510] text-lg flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-[#8a745c]" />
            Payment Method
          </CardTitle>
        </CardHeader>
        <CardContent>
          <PaymentElement
            options={{
              layout: 'tabs',
            }}
          />
        </CardContent>
      </Card>

      {/* Submit Button */}
      <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
        <CardContent className="pt-6">
          <Button
            type="submit"
            disabled={!stripe || isProcessing}
            className="w-full bg-[#e58219] hover:bg-[#d4741a] text-white font-medium py-3 text-base"
          >
            {isProcessing ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Processing Payment...
              </div>
            ) : (
              `Pay $${(paymentIntent.amount / 100).toFixed(2)}`
            )}
          </Button>

          <p className="text-[#8a745c] text-xs text-center mt-3">
            Your payment information is secure and encrypted
          </p>
        </CardContent>
      </Card>
    </form>
  );
}
