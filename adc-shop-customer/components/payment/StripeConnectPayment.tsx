'use client';

import React, { useState, useEffect } from 'react';
import { loadStripe, Stripe, StripeElements } from '@stripe/stripe-js';
import {
  Elements,
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard, CheckCircle, XCircle } from 'lucide-react';

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface PaymentIntentData {
  clientSecret: string;
  paymentIntentId: string;
  connectedAccountId: string;
  amount: number;
  currency: string;
}

interface StripeConnectPaymentProps {
  paymentIntent: PaymentIntentData;
  orderDetails: {
    orderId: string;
    orderNumber?: string;
    customerName: string;
    total: number;
  };
  onPaymentSuccess: (paymentIntentId: string) => void;
  onPaymentError: (error: string) => void;
  onCancel?: () => void;
}

// Payment form component
const PaymentForm: React.FC<{
  paymentIntent: PaymentIntentData;
  orderDetails: StripeConnectPaymentProps['orderDetails'];
  onPaymentSuccess: (paymentIntentId: string) => void;
  onPaymentError: (error: string) => void;
  onCancel?: () => void;
}> = ({ paymentIntent, orderDetails, onPaymentSuccess, onPaymentError, onCancel }) => {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'succeeded' | 'failed'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);
    setPaymentStatus('processing');
    setErrorMessage('');

    const cardElement = elements.getElement(CardElement);

    if (!cardElement) {
      setErrorMessage('Card element not found');
      setIsProcessing(false);
      setPaymentStatus('failed');
      return;
    }

    try {
      // Confirm payment with Stripe
      const { error, paymentIntent: confirmedPaymentIntent } = await stripe.confirmCardPayment(
        paymentIntent.clientSecret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: orderDetails.customerName,
            },
          },
        }
      );

      if (error) {
        console.error('Payment failed:', error);
        setErrorMessage(error.message || 'Payment failed');
        setPaymentStatus('failed');
        onPaymentError(error.message || 'Payment failed');
      } else if (confirmedPaymentIntent?.status === 'succeeded') {
        setPaymentStatus('succeeded');
        onPaymentSuccess(confirmedPaymentIntent.id);
      } else {
        setErrorMessage('Payment was not completed');
        setPaymentStatus('failed');
        onPaymentError('Payment was not completed');
      }
    } catch (err) {
      console.error('Payment error:', err);
      setErrorMessage('An unexpected error occurred');
      setPaymentStatus('failed');
      onPaymentError('An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  if (paymentStatus === 'succeeded') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-green-700 mb-2">Payment Successful!</h3>
            <p className="text-gray-600 mb-4">
              Your payment of {formatAmount(paymentIntent.amount, paymentIntent.currency)} has been processed successfully.
            </p>
            <p className="text-sm text-gray-500">
              Order: {orderDetails.orderNumber || orderDetails.orderId}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (paymentStatus === 'failed') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center">
            <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-700 mb-2">Payment Failed</h3>
            <p className="text-gray-600 mb-4">{errorMessage}</p>
            <div className="space-y-2">
              <Button 
                onClick={() => {
                  setPaymentStatus('idle');
                  setErrorMessage('');
                }}
                className="w-full"
              >
                Try Again
              </Button>
              {onCancel && (
                <Button variant="outline" onClick={onCancel} className="w-full">
                  Cancel Order
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Complete Payment
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Order Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Order Summary</h4>
            <div className="flex justify-between text-sm">
              <span>Customer:</span>
              <span>{orderDetails.customerName}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Order:</span>
              <span>{orderDetails.orderNumber || orderDetails.orderId}</span>
            </div>
            <div className="flex justify-between font-semibold text-lg mt-2 pt-2 border-t">
              <span>Total:</span>
              <span>{formatAmount(paymentIntent.amount, paymentIntent.currency)}</span>
            </div>
          </div>

          {/* Card Element */}
          <div className="border rounded-lg p-4">
            <CardElement
              options={{
                style: {
                  base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                      color: '#aab7c4',
                    },
                  },
                  invalid: {
                    color: '#9e2146',
                  },
                },
              }}
            />
          </div>

          {/* Error Message */}
          {errorMessage && (
            <Alert variant="destructive">
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="space-y-2">
            <Button
              type="submit"
              disabled={!stripe || isProcessing}
              className="w-full"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing Payment...
                </>
              ) : (
                `Pay ${formatAmount(paymentIntent.amount, paymentIntent.currency)}`
              )}
            </Button>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isProcessing}
                className="w-full"
              >
                Cancel
              </Button>
            )}
          </div>

          {/* Security Notice */}
          <p className="text-xs text-gray-500 text-center">
            Your payment is secured by Stripe. We never store your card details.
          </p>
        </form>
      </CardContent>
    </Card>
  );
};

// Main component
const StripeConnectPayment: React.FC<StripeConnectPaymentProps> = ({
  paymentIntent,
  orderDetails,
  onPaymentSuccess,
  onPaymentError,
  onCancel,
}) => {
  const [stripeInstance, setStripeInstance] = useState<Stripe | null>(null);

  useEffect(() => {
    const initializeStripe = async () => {
      try {
        const stripe = await loadStripe(
          process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
          {
            stripeAccount: paymentIntent.connectedAccountId,
          }
        );
        setStripeInstance(stripe);
      } catch (error) {
        console.error('Failed to initialize Stripe:', error);
        onPaymentError('Failed to initialize payment system');
      }
    };

    initializeStripe();
  }, [paymentIntent.connectedAccountId, onPaymentError]);

  if (!stripeInstance) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading payment system...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Elements stripe={stripeInstance}>
      <PaymentForm
        paymentIntent={paymentIntent}
        orderDetails={orderDetails}
        onPaymentSuccess={onPaymentSuccess}
        onPaymentError={onPaymentError}
        onCancel={onCancel}
      />
    </Elements>
  );
};

export default StripeConnectPayment;
