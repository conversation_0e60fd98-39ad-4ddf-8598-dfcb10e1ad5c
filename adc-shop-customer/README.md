# ADC Shop Customer

Customer-facing application for the ADC Shop platform.

## Quick Start

The easiest way to start both frontend and backend services:

```bash
# Quick start (installs dependencies and starts both services)
make quick-start

# Or step by step:
make setup    # Install dependencies and setup environment
make dev      # Start both services in development mode
```

## Available Commands

### Development
- `make dev` - Start both frontend and backend in development mode
- `make dev-frontend` - Start only frontend in development mode
- `make dev-backend` - Start only backend in development mode

### Build & Production
- `make build` - Build both frontend and backend
- `make start` - Start both services in production mode
- `make stop` - Stop all running services
- `make restart` - Restart all services

### Dependencies & Setup
- `make install` - Install all dependencies
- `make setup` - Initial setup (install deps, setup env files)
- `make setup-maps` - Setup Google Maps API key
- `make clean` - Clean build artifacts and logs

### Testing & Quality
- `make test` - Run all tests
- `make lint` - Lint all code
- `make format` - Format all code

### Monitoring
- `make health` - Check service health
- `make logs` - Show live logs
- `make urls` - Show service URLs

### Docker (Optional)
- `make docker-build` - Build Docker images
- `make docker-run` - Run with Docker Compose
- `make docker-stop` - Stop Docker containers

## Service URLs

When running, the services will be available at:
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8081
- **Health Check**: http://localhost:8081/health
- **Interactive Map**: http://localhost:3000/map

## Project Structure

```
adc-shop-customer/
├── app/                    # Next.js app directory
├── components/             # React components
├── lib/                    # Utilities and configurations
├── customer-backend/       # Go backend service
├── public/                 # Static assets
├── Makefile               # Build and development commands
└── README.md              # This file
```

## Technology Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **shadcn/ui** - UI component library
- **RTK Query** - API state management
- **NextAuth.js** - Authentication
- **Google Maps** - Interactive maps and location services

### Backend
- **Go** - Backend language
- **Gin** - HTTP web framework
- **PostgreSQL** - Database
- **JWT** - Authentication tokens

## Development

### Prerequisites
- Node.js 18+
- Go 1.21+
- PostgreSQL (for backend)

### Environment Setup

1. **Backend Environment** (customer-backend/.env):
```env
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=customer_db
JWT_SECRET=your_jwt_secret
PORT=8081
```

2. **Frontend Environment** (.env.local):
```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret
NEXT_PUBLIC_API_URL=http://localhost:8081
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
```

### Manual Setup (Alternative to Makefile)

If you prefer to run commands manually:

```bash
# Install frontend dependencies
npm install

# Install backend dependencies
cd customer-backend && go mod download

# Start backend
cd customer-backend && go run cmd/server/main.go

# Start frontend (in another terminal)
npm run dev
```

## Google Maps Integration

### Quick Setup
```bash
# Automated setup using Google Cloud CLI
make setup-maps

# Restart development server
make dev
```

### Features
- **Interactive Restaurant Map** - View restaurants on an interactive map
- **Real-time Location** - Find restaurants near your current location
- **Turn-by-turn Directions** - Get directions to any restaurant
- **Smart Search** - Search by name, address, or cuisine type
- **Distance Calculations** - See exact distances to restaurants
- **Custom Markers** - Distinctive markers for restaurants and user location

### Manual Setup
If the automated setup doesn't work, see [Google Maps Setup Guide](docs/GOOGLE_MAPS_SETUP.md) for detailed instructions.

## API Documentation

The backend API documentation is available at:
- Swagger UI: http://localhost:8081/docs (when running)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `make test`
5. Run linting: `make lint`
6. Submit a pull request

## License

This project is proprietary software for ADC Shop platform.
