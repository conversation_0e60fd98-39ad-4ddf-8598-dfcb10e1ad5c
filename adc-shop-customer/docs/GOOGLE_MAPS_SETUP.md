# Google Maps Integration Setup Guide

This guide will help you set up Google Maps integration for the ADC Shop Customer application.

## 🚀 Quick Setup (Recommended)

### Prerequisites
- Google Cloud CLI (`gcloud`) installed and configured
- Active Google Cloud Project with billing enabled
- Node.js and npm installed

### Automated Setup
Run the automated setup script:

```bash
./scripts/setup-google-maps.sh
```

This script will:
1. ✅ Check if gcloud is installed and authenticated
2. ✅ Enable required Google APIs
3. ✅ Create a Google Maps API key
4. ✅ Configure API key restrictions
5. ✅ Update your `.env.local` file automatically

### Manual Restart
After running the script, restart your development server:

```bash
npm run dev
```

## 🛠️ Manual Setup

If you prefer to set up manually or the script doesn't work:

### 1. Install Google Cloud CLI
```bash
# macOS
brew install google-cloud-sdk

# Or download from: https://cloud.google.com/sdk/docs/install
```

### 2. Authenticate with Google Cloud
```bash
gcloud auth login
gcloud config set project YOUR_PROJECT_ID
```

### 3. Enable Required APIs
```bash
gcloud services enable maps-backend.googleapis.com
gcloud services enable places-backend.googleapis.com
gcloud services enable geocoding-backend.googleapis.com
gcloud services enable directions-backend.googleapis.com
gcloud services enable distance-matrix-backend.googleapis.com
```

### 4. Create API Key
```bash
gcloud alpha services api-keys create \
  --display-name="ADC Shop Maps Key" \
  --api-target=service=maps-backend.googleapis.com \
  --api-target=service=places-backend.googleapis.com \
  --api-target=service=geocoding-backend.googleapis.com
```

### 5. Get API Key Value
```bash
# List your API keys
gcloud alpha services api-keys list

# Get the key string (replace KEY_ID with actual ID)
gcloud alpha services api-keys get-key-string KEY_ID
```

### 6. Update Environment File
Add to your `.env.local`:
```env
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_actual_api_key_here
```

## 🔒 Security Configuration

### API Key Restrictions (Recommended)
For production, add HTTP referrer restrictions:

```bash
gcloud alpha services api-keys update KEY_ID \
  --allowed-referrer="yourdomain.com/*" \
  --allowed-referrer="*.yourdomain.com/*"
```

For development:
```bash
gcloud alpha services api-keys update KEY_ID \
  --allowed-referrer="localhost:3000/*" \
  --allowed-referrer="127.0.0.1:3000/*"
```

## 📋 Features Included

### 🗺️ Interactive Map Component
- **File**: `components/GoogleMap.tsx`
- Real-time restaurant markers
- User location detection
- Custom marker icons
- Info windows with restaurant details
- Map bounds fitting

### 🔍 Shop Locator
- **File**: `components/ShopLocator.tsx`
- Search by name or address
- Filter by cuisine and price
- Sort by distance, rating, or name
- Real-time location updates

### 📍 Location Services
- **File**: `lib/google-maps.ts`
- Geocoding and reverse geocoding
- Distance calculations
- Current location detection
- Places search integration

### 🎯 Map Page
- **File**: `app/map/page.tsx`
- Full-featured map interface
- Restaurant selection
- Turn-by-turn directions
- Responsive design

## 🧪 Testing the Integration

### 1. Check API Key
Visit: http://localhost:3000/map

You should see either:
- ✅ Interactive map with restaurant markers (API key working)
- ⚠️ Setup placeholder with instructions (API key needed)

### 2. Test Features
- **Location Detection**: Click "My Location" button
- **Search**: Enter an address in the search bar
- **Markers**: Click on restaurant markers for details
- **Directions**: Select a restaurant and click "Directions"

### 3. Browser Console
Check for any API errors:
```javascript
// Open browser console (F12) and look for:
// ✅ No Google Maps errors
// ❌ "Google Maps JavaScript API error: InvalidKeyMapError"
```

## 💰 Cost Management

### API Usage Monitoring
Monitor your usage in Google Cloud Console:
- [Google Cloud Console - APIs & Services](https://console.cloud.google.com/apis/dashboard)

### Set Billing Alerts
1. Go to [Billing](https://console.cloud.google.com/billing)
2. Set up budget alerts
3. Recommended: $10-50/month for development

### API Quotas
Default quotas (per day):
- Maps JavaScript API: 25,000 requests
- Geocoding API: 40,000 requests
- Places API: 1,000 requests

## 🐛 Troubleshooting

### Common Issues

#### "API key not valid" Error
```bash
# Check if APIs are enabled
gcloud services list --enabled | grep maps

# Verify API key restrictions
gcloud alpha services api-keys describe KEY_ID
```

#### Map Not Loading
1. Check browser console for errors
2. Verify API key in `.env.local`
3. Ensure development server is restarted
4. Check network connectivity

#### Location Not Working
1. Ensure HTTPS in production (required for geolocation)
2. Check browser permissions
3. Test with different browsers

### Debug Mode
Enable debug logging by adding to `.env.local`:
```env
NEXT_PUBLIC_DEBUG_MAPS=true
```

## 📚 API Documentation

### Google Maps APIs Used
- [Maps JavaScript API](https://developers.google.com/maps/documentation/javascript)
- [Places API](https://developers.google.com/maps/documentation/places/web-service)
- [Geocoding API](https://developers.google.com/maps/documentation/geocoding)
- [Directions API](https://developers.google.com/maps/documentation/directions)

### Component Props

#### GoogleMap Component
```typescript
interface GoogleMapProps {
  shops?: ShopLocation[];
  center?: { lat: number; lng: number };
  zoom?: number;
  height?: string;
  width?: string;
  showUserLocation?: boolean;
  onShopClick?: (shop: ShopLocation) => void;
  onMapClick?: (lat: number, lng: number) => void;
  className?: string;
}
```

#### ShopLocation Interface
```typescript
interface ShopLocation {
  id: string;
  name: string;
  address: string;
  lat: number;
  lng: number;
  rating?: number;
  priceRange?: string;
  cuisineType?: string;
  isOpen?: boolean;
  distance?: number;
}
```

## 🔄 Updates and Maintenance

### Updating API Key
1. Generate new key in Google Cloud Console
2. Update `.env.local`
3. Restart development server

### Adding New Features
The Google Maps integration is modular and extensible:
- Add new marker types in `lib/google-maps.ts`
- Create custom map styles
- Integrate with additional Google APIs
- Add real-time tracking features

## 📞 Support

### Resources
- [Google Maps Platform Support](https://developers.google.com/maps/support)
- [Stack Overflow - Google Maps](https://stackoverflow.com/questions/tagged/google-maps)
- [Google Cloud Support](https://cloud.google.com/support)

### Project-Specific Issues
For issues specific to this implementation, check:
1. Browser console errors
2. Network tab for failed API requests
3. Google Cloud Console for quota/billing issues
