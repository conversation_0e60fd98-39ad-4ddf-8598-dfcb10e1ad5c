#!/bin/bash

# Google Maps API Setup Script
# This script helps you set up Google Maps API using Google Cloud CLI

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if gcloud is installed
check_gcloud() {
    if ! command -v gcloud &> /dev/null; then
        print_error "Google Cloud CLI (gcloud) is not installed."
        echo "Please install it from: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    print_status "Google Cloud CLI is installed ✅"
}

# Check if user is authenticated
check_auth() {
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null 2>&1; then
        print_warning "You are not authenticated with Google Cloud."
        echo "Please run: gcloud auth login"
        exit 1
    fi
    
    ACCOUNT=$(gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1)
    print_status "Authenticated as: $ACCOUNT ✅"
}

# Get or set project
setup_project() {
    CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null || echo "")
    
    if [ -z "$CURRENT_PROJECT" ]; then
        print_warning "No project is currently set."
        echo "Available projects:"
        gcloud projects list --format="table(projectId,name,projectNumber)"
        echo ""
        read -p "Enter your project ID: " PROJECT_ID
        gcloud config set project $PROJECT_ID
    else
        print_status "Current project: $CURRENT_PROJECT"
        read -p "Use this project? (y/n): " USE_CURRENT
        if [[ $USE_CURRENT != "y" && $USE_CURRENT != "Y" ]]; then
            echo "Available projects:"
            gcloud projects list --format="table(projectId,name,projectNumber)"
            echo ""
            read -p "Enter your project ID: " PROJECT_ID
            gcloud config set project $PROJECT_ID
        else
            PROJECT_ID=$CURRENT_PROJECT
        fi
    fi
    
    print_status "Using project: $PROJECT_ID ✅"
}

# Enable required APIs
enable_apis() {
    print_header "🔧 Enabling required Google APIs..."
    
    APIS=(
        "maps-backend.googleapis.com"
        "places-backend.googleapis.com"
        "geocoding-backend.googleapis.com"
        "directions-backend.googleapis.com"
        "distance-matrix-backend.googleapis.com"
    )
    
    for API in "${APIS[@]}"; do
        print_status "Enabling $API..."
        gcloud services enable $API
    done
    
    print_status "All APIs enabled ✅"
}

# Create API key
create_api_key() {
    print_header "🔑 Creating Google Maps API Key..."
    
    # Create API key
    API_KEY_NAME="adc-shop-maps-key-$(date +%s)"
    
    print_status "Creating API key: $API_KEY_NAME"
    
    # Create the API key
    gcloud alpha services api-keys create \
        --display-name="$API_KEY_NAME" \
        --api-target=service=maps-backend.googleapis.com \
        --api-target=service=places-backend.googleapis.com \
        --api-target=service=geocoding-backend.googleapis.com \
        --api-target=service=directions-backend.googleapis.com \
        --api-target=service=distance-matrix-backend.googleapis.com \
        --format="value(name)" > /tmp/api_key_name.txt
    
    API_KEY_RESOURCE=$(cat /tmp/api_key_name.txt)
    
    # Get the actual API key value
    API_KEY=$(gcloud alpha services api-keys get-key-string $API_KEY_RESOURCE --format="value(keyString)")
    
    print_status "API Key created successfully! ✅"
    echo ""
    print_header "📋 Your Google Maps API Key:"
    echo "$API_KEY"
    echo ""
    
    # Save to environment file
    ENV_FILE="../.env.local"
    if [ -f "$ENV_FILE" ]; then
        # Check if NEXT_PUBLIC_GOOGLE_MAPS_API_KEY already exists
        if grep -q "NEXT_PUBLIC_GOOGLE_MAPS_API_KEY" "$ENV_FILE"; then
            # Update existing key
            sed -i.bak "s/NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=.*/NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=$API_KEY/" "$ENV_FILE"
            print_status "Updated existing API key in $ENV_FILE"
        else
            # Add new key
            echo "" >> "$ENV_FILE"
            echo "# Google Maps API Key" >> "$ENV_FILE"
            echo "NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=$API_KEY" >> "$ENV_FILE"
            print_status "Added API key to $ENV_FILE"
        fi
    else
        print_warning "$ENV_FILE not found. Please add this to your environment file:"
        echo "NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=$API_KEY"
    fi
    
    # Clean up
    rm -f /tmp/api_key_name.txt
}

# Add restrictions (optional)
add_restrictions() {
    print_header "🔒 API Key Security (Optional)"
    echo "For production, you should add restrictions to your API key:"
    echo "1. HTTP referrer restrictions for web apps"
    echo "2. IP address restrictions for server apps"
    echo ""
    echo "You can do this in the Google Cloud Console:"
    echo "https://console.cloud.google.com/apis/credentials"
    echo ""
    read -p "Would you like to add HTTP referrer restrictions now? (y/n): " ADD_RESTRICTIONS
    
    if [[ $ADD_RESTRICTIONS == "y" || $ADD_RESTRICTIONS == "Y" ]]; then
        echo "Enter allowed referrers (one per line, empty line to finish):"
        echo "Example: localhost:3000/*, yourdomain.com/*"
        
        REFERRERS=()
        while true; do
            read -p "Referrer: " REFERRER
            if [ -z "$REFERRER" ]; then
                break
            fi
            REFERRERS+=("$REFERRER")
        done
        
        if [ ${#REFERRERS[@]} -gt 0 ]; then
            REFERRER_FLAGS=""
            for REF in "${REFERRERS[@]}"; do
                REFERRER_FLAGS="$REFERRER_FLAGS --allowed-referrer=$REF"
            done
            
            gcloud alpha services api-keys update $API_KEY_RESOURCE \
                --clear-restrictions \
                --browser-key-restrictions $REFERRER_FLAGS
            
            print_status "Added referrer restrictions ✅"
        fi
    fi
}

# Main execution
main() {
    print_header "🗺️  Google Maps API Setup for ADC Shop Customer"
    echo ""
    
    check_gcloud
    check_auth
    setup_project
    enable_apis
    create_api_key
    add_restrictions
    
    print_header "🎉 Setup Complete!"
    echo ""
    print_status "Next steps:"
    echo "1. Restart your Next.js development server"
    echo "2. The Google Maps components are ready to use"
    echo "3. Check the console for any API quota limits"
    echo ""
    print_warning "Important:"
    echo "- Keep your API key secure"
    echo "- Monitor your API usage in Google Cloud Console"
    echo "- Set up billing alerts to avoid unexpected charges"
    echo ""
}

# Run main function
main "$@"
