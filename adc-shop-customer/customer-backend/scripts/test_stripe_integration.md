# Stripe Connect Integration Test Guide

## Overview
The QR ordering system now includes full Stripe Connect integration for processing payments from customers to merchant connected accounts.

## Setup Instructions

### 1. Get Real Stripe API Keys
To test with real Stripe API keys, follow these steps:

```bash
# Login to Stripe CLI
stripe login

# Get your test API keys
stripe config --list

# Or get them from Stripe Dashboard
# https://dashboard.stripe.com/test/apikeys
```

### 2. Update Environment Variables
Update the `.env` file with your real Stripe keys:

```env
# Replace with your actual Stripe test keys
STRIPE_SECRET_KEY=sk_test_your_actual_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_actual_webhook_secret_here
STRIPE_PLATFORM_FEE=250  # 2.5% platform fee
STRIPE_CURRENCY=thb      # Thai Baht
```

### 3. Create Connected Account
For testing, you'll need a connected account ID:

```bash
# Create a test connected account
stripe accounts create \
  --type=express \
  --country=TH \
  --email=<EMAIL>
```

## API Endpoints

### 1. Get Payment Configuration
```bash
curl "http://localhost:8900/payments/config"
```

### 2. Create Payment Intent
```bash
curl -X POST "http://localhost:8900/payments/create-intent" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "366fe867-ac54-469a-9216-6249f93bfb8f",
    "connected_account_id": "acct_your_connected_account_id"
  }'
```

### 3. Get Payment Status
```bash
curl "http://localhost:8900/payments/pi_your_payment_intent_id/status"
```

### 4. Confirm Payment
```bash
curl -X POST "http://localhost:8900/payments/confirm" \
  -H "Content-Type: application/json" \
  -d '{
    "payment_intent_id": "pi_your_payment_intent_id"
  }'
```

### 5. Cancel Payment
```bash
curl -X POST "http://localhost:8900/payments/pi_your_payment_intent_id/cancel"
```

## Webhook Testing

### 1. Start Webhook Forwarding
```bash
# Forward webhooks to local server
stripe listen --forward-to localhost:8900/payments/webhook
```

### 2. Test Webhook Events
```bash
# Trigger test payment success event
stripe trigger payment_intent.succeeded

# Trigger test payment failure event
stripe trigger payment_intent.payment_failed
```

## Integration Features

### ✅ Implemented Features

1. **Payment Intent Creation**
   - Creates Stripe Payment Intent for orders
   - Supports Stripe Connect (transfers to merchant accounts)
   - Automatic platform fee calculation (2.5% default)
   - Order metadata included in payment intent

2. **Payment Processing**
   - Payment confirmation
   - Payment status tracking
   - Payment cancellation
   - Real-time webhook processing

3. **Stripe Connect Support**
   - Connected account integration
   - Platform fee collection
   - Transfer to merchant accounts
   - Multi-merchant support

4. **Webhook Handling**
   - Payment success events
   - Payment failure events
   - Payment cancellation events
   - Signature verification

5. **Error Handling**
   - Comprehensive error logging
   - Proper HTTP status codes
   - Stripe error message forwarding

### 🔄 Payment Flow

1. **Customer places order** → Order created in database
2. **Frontend requests payment** → `POST /payments/create-intent`
3. **Payment Intent created** → Stripe Payment Intent with Connect transfer
4. **Customer pays** → Frontend uses Stripe.js with client_secret
5. **Payment processed** → Webhook updates order status
6. **Funds transferred** → Automatic transfer to merchant account (minus platform fee)

### 💰 Platform Fee Structure

- **Platform Fee**: 2.5% (configurable via `STRIPE_PLATFORM_FEE`)
- **Stripe Fees**: Standard Stripe processing fees
- **Merchant Receives**: Order total - Platform fee - Stripe fees

### 🔐 Security Features

- **Webhook signature verification**
- **API key validation**
- **Order ownership validation**
- **Connected account verification**

## Testing with Mock Data

Since we're using test keys, here's what the successful response would look like:

```json
{
  "success": true,
  "data": {
    "payment_intent_id": "pi_1234567890abcdef",
    "client_secret": "pi_1234567890abcdef_secret_xyz",
    "amount": 37440,
    "currency": "thb",
    "status": "requires_payment_method",
    "publishable_key": "pk_test_your_publishable_key",
    "connected_account_id": "acct_your_connected_account"
  }
}
```

## Next Steps

1. **Get real Stripe API keys** from your Stripe dashboard
2. **Create connected accounts** for each merchant
3. **Test the complete payment flow** with real payment methods
4. **Set up webhook endpoints** in production
5. **Configure platform fee structure** as needed

The Stripe Connect integration is fully implemented and ready for testing with valid API keys!
