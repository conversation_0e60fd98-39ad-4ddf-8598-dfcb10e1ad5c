#!/bin/bash

# Script to run cart tables migration
# This script applies the cart tables migration to the database

set -e

# Database connection details
DB_URL="postgresql://postgres.sqzzpwirpwdlxzuvztey:<EMAIL>:5432/postgres"

# Migration file
MIGRATION_FILE="migrations/003_add_cart_tables.sql"

echo "🚀 Running cart tables migration..."
echo "Migration file: $MIGRATION_FILE"
echo "Database: $DB_URL"
echo ""

# Check if migration file exists
if [ ! -f "$MIGRATION_FILE" ]; then
    echo "❌ Migration file not found: $MIGRATION_FILE"
    exit 1
fi

# Run the migration
echo "📝 Applying migration..."
psql "$DB_URL" -f "$MIGRATION_FILE"

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Cart tables migration completed successfully!"
    echo ""
    echo "Created tables:"
    echo "  - cart_sessions"
    echo "  - cart_items"
    echo ""
    echo "Created indexes and constraints for optimal performance."
else
    echo ""
    echo "❌ Migration failed!"
    exit 1
fi
