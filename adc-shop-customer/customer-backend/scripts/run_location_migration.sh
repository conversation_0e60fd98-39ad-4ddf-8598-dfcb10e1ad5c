#!/bin/bash

# Script to run location fields migration for customer backend
# This adds latitude, longitude, and location-related fields to the database

set -e

echo "🗺️  Running location fields migration for customer backend..."

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ ERROR: DATABASE_URL environment variable is not set"
    echo "Please set DATABASE_URL in your .env file or environment"
    exit 1
fi

echo "📊 Database URL: ${DATABASE_URL}"

# Run the migration
echo "🔄 Executing migration: 001_add_location_fields.sql"

# Use psql to run the migration
psql "$DATABASE_URL" -f migrations/001_add_location_fields.sql

if [ $? -eq 0 ]; then
    echo "✅ Location fields migration completed successfully!"
    echo ""
    echo "📋 Added fields:"
    echo "   - latitude (DECIMAL(10,8))"
    echo "   - longitude (DECIMAL(11,8))"
    echo "   - location_accuracy (INTEGER)"
    echo "   - geocoded_at (TIMESTAMP)"
    echo "   - place_id (VARCHAR(255))"
    echo "   - formatted_address (TEXT)"
    echo "   - location_type (VARCHAR(50))"
    echo ""
    echo "🔍 Created indexes:"
    echo "   - idx_shops_location (GIST index for spatial queries)"
    echo "   - idx_shop_branches_location (GIST index for spatial queries)"
    echo "   - idx_shops_lat_lng (B-tree index for coordinate queries)"
    echo "   - idx_shop_branches_lat_lng (B-tree index for coordinate queries)"
    echo ""
    echo "🔧 Created functions:"
    echo "   - calculate_distance() - Haversine distance calculation"
    echo "   - find_nearby_shops_customer() - Find shops within radius (customer-focused)"
    echo ""
    echo "📍 Sample location data has been added to existing shops"
    echo ""
    echo "🎯 Next steps:"
    echo "   1. Update your application to use the new location fields"
    echo "   2. Test the Google Maps integration"
    echo "   3. Implement location-based search functionality"
    echo "   4. Add distance-based filtering"
else
    echo "❌ Migration failed!"
    exit 1
fi
