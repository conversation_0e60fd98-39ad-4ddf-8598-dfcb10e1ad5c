package pagination

import (
	"customer-backend/internal/types"
	"math"
	"strconv"
	"strings"
)

// CenterPaginationConfig holds configuration for center pagination
type CenterPaginationConfig struct {
	CenterSize    int  `json:"center_size"`
	ShowFirst     bool `json:"show_first"`
	ShowLast      bool `json:"show_last"`
	ShowEllipsis  bool `json:"show_ellipsis"`
	MaxVisiblePages int `json:"max_visible_pages"`
}

// DefaultCenterConfig returns default center pagination configuration
func DefaultCenterConfig() CenterPaginationConfig {
	return CenterPaginationConfig{
		CenterSize:      5,
		ShowFirst:       true,
		ShowLast:        true,
		ShowEllipsis:    true,
		MaxVisiblePages: 7,
	}
}

// CalculateCenterPagination calculates center pagination with enhanced UX
func CalculateCenterPagination(currentPage, totalPages int, config CenterPaginationConfig) *types.PaginationInfo {
	if totalPages <= 0 {
		return &types.PaginationInfo{
			CurrentPage:  1,
			TotalPages:   0,
			HasNext:      false,
			HasPrev:      false,
			CenterPages:  []int{},
			ShowFirst:    false,
			ShowLast:     false,
		}
	}

	// Ensure current page is within bounds
	if currentPage < 1 {
		currentPage = 1
	}
	if currentPage > totalPages {
		currentPage = totalPages
	}

	paginationInfo := &types.PaginationInfo{
		CurrentPage: currentPage,
		TotalPages:  totalPages,
		HasNext:     currentPage < totalPages,
		HasPrev:     currentPage > 1,
	}

	// Calculate center pages
	centerPages := calculateCenterPages(currentPage, totalPages, config)
	paginationInfo.CenterPages = centerPages

	// Determine if we should show first/last page links
	if len(centerPages) > 0 {
		paginationInfo.ShowFirst = config.ShowFirst && centerPages[0] > 1
		paginationInfo.ShowLast = config.ShowLast && centerPages[len(centerPages)-1] < totalPages
	}

	return paginationInfo
}

// calculateCenterPages calculates the center page numbers to display
func calculateCenterPages(currentPage, totalPages int, config CenterPaginationConfig) []int {
	if totalPages <= config.MaxVisiblePages {
		// Show all pages if total is small
		pages := make([]int, totalPages)
		for i := 0; i < totalPages; i++ {
			pages[i] = i + 1
		}
		return pages
	}

	// Calculate the range around current page
	halfCenter := config.CenterSize / 2
	start := currentPage - halfCenter
	end := currentPage + halfCenter

	// Adjust start and end to stay within bounds
	if start < 1 {
		start = 1
		end = start + config.CenterSize - 1
	}
	if end > totalPages {
		end = totalPages
		start = end - config.CenterSize + 1
		if start < 1 {
			start = 1
		}
	}

	// Generate page numbers
	pages := make([]int, 0, end-start+1)
	for i := start; i <= end; i++ {
		pages = append(pages, i)
	}

	return pages
}

// CreatePaginationInfo creates complete pagination information
func CreatePaginationInfo(total int64, currentPage, limit int, config CenterPaginationConfig) *types.PaginationInfo {
	totalPages := int(math.Ceil(float64(total) / float64(limit)))
	
	paginationInfo := CalculateCenterPagination(currentPage, totalPages, config)
	paginationInfo.TotalItems = total
	paginationInfo.ItemsPerPage = limit

	return paginationInfo
}

// GenerateCursor generates a cursor for cursor-based pagination
func GenerateCursor(page int, sortField, sortValue string) string {
	return encodeBase64(page, sortField, sortValue)
}

// ParseCursor parses a cursor for cursor-based pagination
func ParseCursor(cursor string) (page int, sortField, sortValue string, err error) {
	return decodeBase64(cursor)
}

// Helper functions for cursor encoding/decoding
func encodeBase64(page int, sortField, sortValue string) string {
	data := strconv.Itoa(page) + "|" + sortField + "|" + sortValue
	// Simple base64-like encoding (in production, use proper base64)
	return strings.ReplaceAll(data, "|", "_")
}

func decodeBase64(cursor string) (int, string, string, error) {
	data := strings.ReplaceAll(cursor, "_", "|")
	parts := strings.Split(data, "|")
	
	if len(parts) != 3 {
		return 0, "", "", nil
	}
	
	page, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, "", "", err
	}
	
	return page, parts[1], parts[2], nil
}

// PaginationBuilder helps build pagination queries
type PaginationBuilder struct {
	page   int
	limit  int
	total  int64
	config CenterPaginationConfig
}

// NewPaginationBuilder creates a new pagination builder
func NewPaginationBuilder() *PaginationBuilder {
	return &PaginationBuilder{
		config: DefaultCenterConfig(),
	}
}

// WithPage sets the current page
func (pb *PaginationBuilder) WithPage(page int) *PaginationBuilder {
	pb.page = page
	return pb
}

// WithLimit sets the page limit
func (pb *PaginationBuilder) WithLimit(limit int) *PaginationBuilder {
	pb.limit = limit
	return pb
}

// WithTotal sets the total count
func (pb *PaginationBuilder) WithTotal(total int64) *PaginationBuilder {
	pb.total = total
	return pb
}

// WithConfig sets the pagination configuration
func (pb *PaginationBuilder) WithConfig(config CenterPaginationConfig) *PaginationBuilder {
	pb.config = config
	return pb
}

// Build creates the pagination information
func (pb *PaginationBuilder) Build() *types.PaginationInfo {
	return CreatePaginationInfo(pb.total, pb.page, pb.limit, pb.config)
}

// CalculateOffset calculates the database offset for the current page
func (pb *PaginationBuilder) CalculateOffset() int {
	if pb.page <= 0 {
		pb.page = 1
	}
	return (pb.page - 1) * pb.limit
}
