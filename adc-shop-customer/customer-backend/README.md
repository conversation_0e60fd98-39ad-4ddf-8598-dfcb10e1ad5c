# Customer Backend API

A Golang-based REST API service designed specifically for customer-facing restaurant applications. This service provides optimized endpoints for menu browsing, shop discovery, and customer interactions with center pagination and proper request/response formatting.

## Features

- **Center Pagination**: Enhanced pagination with center-focused page navigation for better UX
- **Shop Settings Integration**: Retrieves and formats shop settings optimized for customer view
- **Menu Management**: Comprehensive menu item browsing with advanced filtering
- **Search & Discovery**: Powerful search capabilities for shops and menu items
- **Location-Based Services**: Nearby shop discovery with distance calculations
- **Dietary Filters**: Support for vegetarian, vegan, gluten-free, and allergen filtering
- **Performance Optimized**: Efficient database queries with proper indexing
- **Standardized Responses**: Consistent API response format across all endpoints

## Architecture

```
customer-backend/
├── cmd/
│   └── server/          # Application entry point
├── internal/
│   ├── api/
│   │   ├── handlers/    # HTTP request handlers
│   │   ├── middleware/  # HTTP middleware
│   │   └── routes/      # Route definitions
│   ├── config/          # Configuration management
│   ├── database/        # Database connection
│   ├── repositories/    # Data access layer
│   ├── services/        # Business logic layer
│   └── types/           # Type definitions
└── pkg/
    ├── logger/          # Logging utilities
    └── pagination/      # Pagination utilities
```

## Quick Start

### Prerequisites

- Go 1.21 or higher
- PostgreSQL database
- Make (optional, for using Makefile commands)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd customer-backend
```

2. Install dependencies:
```bash
go mod download
```

3. Copy environment configuration:
```bash
cp .env.example .env
```

4. Update the `.env` file with your database credentials and configuration.

5. Build and run:
```bash
make build
make run
```

Or run directly:
```bash
go run cmd/server/main.go
```

### Using Docker

1. Build the Docker image:
```bash
make docker-build
```

2. Run the container:
```bash
make docker-run
```

## API Endpoints

### Shop Endpoints

- `GET /api/v1/shops` - Get paginated list of shops
- `GET /api/v1/shops/{id}` - Get shop details
- `GET /api/v1/shops/search?q={query}` - Search shops
- `GET /api/v1/shops/popular` - Get popular shops
- `GET /api/v1/shops/nearby?latitude={lat}&longitude={lng}` - Get nearby shops
- `GET /api/v1/shops/category/{category}` - Get shops by category
- `GET /api/v1/shops/{id}/status` - Get shop operating status

### Menu Endpoints

- `GET /api/v1/shops/{shopId}/menu` - Get menu items for a shop
- `GET /api/v1/shops/{shopId}/menu/popular` - Get popular menu items
- `GET /api/v1/shops/{shopId}/menu/new` - Get new menu items
- `GET /api/v1/shops/{shopId}/menu/vegetarian` - Get vegetarian items
- `GET /api/v1/shops/{shopId}/menu/search?q={query}` - Search menu items
- `GET /api/v1/shops/{shopId}/menu/categories` - Get menu categories
- `GET /api/v1/shops/{shopId}/menu/categories/{categoryId}` - Get items by category
- `GET /api/v1/menu/items/{itemId}` - Get menu item details

## Query Parameters

### Pagination
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)

### Filtering
- `search` - Search query string
- `category` - Filter by category
- `is_available` - Filter by availability
- `is_popular` - Filter popular items
- `is_vegetarian` - Filter vegetarian items
- `is_vegan` - Filter vegan items
- `is_gluten_free` - Filter gluten-free items
- `price_min` - Minimum price
- `price_max` - Maximum price
- `min_rating` - Minimum rating

### Sorting
- `sort_by` - Sort field (default: varies by endpoint)
- `sort_order` - Sort order: `asc` or `desc`

## Response Format

All API responses follow a standardized format:

```json
{
  "success": true,
  "data": {
    // Response data
  },
  "message": "Success message",
  "pagination": {
    "current_page": 1,
    "total_pages": 10,
    "total_items": 200,
    "items_per_page": 20,
    "has_next": true,
    "has_prev": false,
    "center_pages": [1, 2, 3, 4, 5],
    "show_first": false,
    "show_last": true
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "version": "1.0",
    "process_time": "15ms"
  }
}
```

## Center Pagination

The API implements center pagination for better user experience:

- Shows a configurable number of pages around the current page
- Automatically adjusts the range to stay within bounds
- Provides indicators for showing first/last page links
- Supports both offset-based and cursor-based pagination

## Configuration

Key configuration options in `.env`:

```env
# Server
CUSTOMER_PORT=8081
GIN_MODE=debug

# Database
DATABASE_URL=postgres://user:pass@localhost:5432/db

# Pagination
CUSTOMER_DEFAULT_PAGE_SIZE=20
CUSTOMER_MAX_PAGE_SIZE=100
CUSTOMER_ENABLE_CENTER_PAGING=true

# Caching
CUSTOMER_SHOP_SETTINGS_CACHE=600
```

## Development

### Available Make Commands

```bash
make build         # Build the application
make run           # Run the application
make dev           # Run with hot reload (requires air)
make test          # Run tests
make test-coverage # Run tests with coverage
make fmt           # Format code
make lint          # Lint code
make clean         # Clean build artifacts
```

### Adding New Endpoints

1. Define types in `internal/types/`
2. Add repository methods in `internal/repositories/`
3. Implement business logic in `internal/services/`
4. Create handlers in `internal/api/handlers/`
5. Register routes in `internal/api/routes/`

## Performance Considerations

- Database queries are optimized with proper indexing
- Pagination limits prevent large result sets
- Response caching for frequently accessed data
- Connection pooling for database connections
- Efficient JSON serialization

## Security

- CORS middleware for cross-origin requests
- Request ID tracking for debugging
- Input validation and sanitization
- Rate limiting (configurable)
- Secure headers in responses

## Monitoring

- Health check endpoint: `GET /health`
- Request logging with response times
- Error tracking and reporting
- Metrics collection (optional)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and linting
6. Submit a pull request

## License

This project is licensed under the MIT License.
