-- Migration: Add cart tables for customer cart functionality
-- This migration creates the cart_sessions and cart_items tables

-- Create cart_sessions table
CREATE TABLE IF NOT EXISTS cart_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Create indexes for cart_sessions
CREATE INDEX IF NOT EXISTS idx_cart_sessions_user_id ON cart_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_cart_sessions_session_id ON cart_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_cart_sessions_deleted_at ON cart_sessions(deleted_at);
CREATE INDEX IF NOT EXISTS idx_cart_sessions_expires_at ON cart_sessions(expires_at);

-- Create cart_items table
CREATE TABLE IF NOT EXISTS cart_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cart_session_id UUID NOT NULL REFERENCES cart_sessions(id) ON DELETE CASCADE,
    menu_item_id UUID NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
    
    -- Cached menu item details for performance and consistency
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image VARCHAR(500),
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    
    -- Shop and branch context for proper order routing
    shop_slug VARCHAR(255) NOT NULL,
    branch_slug VARCHAR(255) NOT NULL,
    
    -- Table context for QR ordering (optional)
    table_id UUID NULL,
    table_number VARCHAR(100) NULL,
    shop_id UUID NULL,
    branch_id UUID NULL,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Create indexes for cart_items
CREATE INDEX IF NOT EXISTS idx_cart_items_cart_session_id ON cart_items(cart_session_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_menu_item_id ON cart_items(menu_item_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_shop_branch ON cart_items(shop_slug, branch_slug);
CREATE INDEX IF NOT EXISTS idx_cart_items_table_id ON cart_items(table_id);
CREATE INDEX IF NOT EXISTS idx_cart_items_deleted_at ON cart_items(deleted_at);

-- Create unique constraint to prevent duplicate items in the same cart session
-- (same menu item, shop, and branch combination)
CREATE UNIQUE INDEX IF NOT EXISTS idx_cart_items_unique_item 
ON cart_items(cart_session_id, menu_item_id, shop_slug, branch_slug) 
WHERE deleted_at IS NULL;

-- Add trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_cart_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for both tables
DROP TRIGGER IF EXISTS update_cart_sessions_updated_at ON cart_sessions;
CREATE TRIGGER update_cart_sessions_updated_at 
    BEFORE UPDATE ON cart_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_cart_updated_at_column();

DROP TRIGGER IF EXISTS update_cart_items_updated_at ON cart_items;
CREATE TRIGGER update_cart_items_updated_at 
    BEFORE UPDATE ON cart_items 
    FOR EACH ROW EXECUTE FUNCTION update_cart_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE cart_sessions IS 'Cart sessions for both logged-in users and guest users';
COMMENT ON COLUMN cart_sessions.user_id IS 'User ID for logged-in users, NULL for guest users';
COMMENT ON COLUMN cart_sessions.session_id IS 'Session identifier for guest users';
COMMENT ON COLUMN cart_sessions.expires_at IS 'When the cart session expires';

COMMENT ON TABLE cart_items IS 'Items stored in cart sessions';
COMMENT ON COLUMN cart_items.cart_session_id IS 'Reference to the cart session';
COMMENT ON COLUMN cart_items.menu_item_id IS 'Reference to the menu item';
COMMENT ON COLUMN cart_items.quantity IS 'Number of items in cart';
COMMENT ON COLUMN cart_items.shop_slug IS 'Shop identifier for order routing';
COMMENT ON COLUMN cart_items.branch_slug IS 'Branch identifier for order routing';
COMMENT ON COLUMN cart_items.table_id IS 'Table ID for QR ordering (optional)';
COMMENT ON COLUMN cart_items.table_number IS 'Table number/name for QR ordering (optional)';
