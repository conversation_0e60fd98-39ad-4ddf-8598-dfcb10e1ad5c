-- Migration: Add tables and orders for QR ordering
-- Created: 2024-01-01

-- Create table_areas table
CREATE TABLE IF NOT EXISTS table_areas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT fk_table_areas_branch FOREIGN KEY (branch_id) REFERENCES shop_branches(id) ON DELETE CASCADE
);

-- Create index on branch_id for table_areas
CREATE INDEX IF NOT EXISTS idx_table_areas_branch_id ON table_areas(branch_id);
CREATE INDEX IF NOT EXISTS idx_table_areas_deleted_at ON table_areas(deleted_at);

-- Create tables table
CREATE TABLE IF NOT EXISTS tables (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    branch_id UUID NOT NULL,
    number INTEGER NOT NULL,
    name VARCHAR(100),
    capacity INTEGER NOT NULL,
    min_capacity INTEGER,
    max_capacity INTEGER,
    area_id UUID,
    status VARCHAR(50) DEFAULT 'available',
    qr_code_url TEXT,
    image_url TEXT,
    is_active BOOLEAN DEFAULT true,
    
    -- Position fields (embedded with pos_ prefix)
    pos_x DECIMAL(10,2),
    pos_y DECIMAL(10,2),
    pos_width DECIMAL(10,2),
    pos_height DECIMAL(10,2),
    pos_angle DECIMAL(10,2),
    
    shape VARCHAR(20) DEFAULT 'square',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT fk_tables_branch FOREIGN KEY (branch_id) REFERENCES shop_branches(id) ON DELETE CASCADE,
    CONSTRAINT fk_tables_area FOREIGN KEY (area_id) REFERENCES table_areas(id) ON DELETE SET NULL,
    CONSTRAINT unique_table_number_per_branch UNIQUE (branch_id, number)
);

-- Create indexes for tables
CREATE INDEX IF NOT EXISTS idx_tables_branch_id ON tables(branch_id);
CREATE INDEX IF NOT EXISTS idx_tables_area_id ON tables(area_id);
CREATE INDEX IF NOT EXISTS idx_tables_status ON tables(status);
CREATE INDEX IF NOT EXISTS idx_tables_is_active ON tables(is_active);
CREATE INDEX IF NOT EXISTS idx_tables_deleted_at ON tables(deleted_at);

-- Create orders table
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shop_id UUID NOT NULL,
    branch_id UUID NOT NULL,
    table_id UUID,
    
    -- Customer information
    customer_name VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(50) NOT NULL,
    customer_email VARCHAR(255),
    
    -- Order details
    order_number VARCHAR(50) UNIQUE NOT NULL,
    order_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    items JSONB NOT NULL,
    special_requests TEXT,
    
    -- Pricing
    subtotal DECIMAL(10,2) NOT NULL,
    service_charge DECIMAL(10,2) DEFAULT 0,
    tax DECIMAL(10,2) DEFAULT 0,
    discount DECIMAL(10,2) DEFAULT 0,
    total DECIMAL(10,2) NOT NULL,
    
    -- Timestamps
    ordered_at TIMESTAMP WITH TIME ZONE NOT NULL,
    confirmed_at TIMESTAMP WITH TIME ZONE,
    ready_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT fk_orders_shop FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE,
    CONSTRAINT fk_orders_branch FOREIGN KEY (branch_id) REFERENCES shop_branches(id) ON DELETE CASCADE,
    CONSTRAINT fk_orders_table FOREIGN KEY (table_id) REFERENCES tables(id) ON DELETE SET NULL
);

-- Create indexes for orders
CREATE INDEX IF NOT EXISTS idx_orders_shop_id ON orders(shop_id);
CREATE INDEX IF NOT EXISTS idx_orders_branch_id ON orders(branch_id);
CREATE INDEX IF NOT EXISTS idx_orders_table_id ON orders(table_id);
CREATE INDEX IF NOT EXISTS idx_orders_customer_phone ON orders(customer_phone);
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_order_type ON orders(order_type);
CREATE INDEX IF NOT EXISTS idx_orders_ordered_at ON orders(ordered_at);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_deleted_at ON orders(deleted_at);

-- Add comments for documentation
COMMENT ON TABLE table_areas IS 'Dining areas within restaurant branches';
COMMENT ON TABLE tables IS 'Restaurant tables for QR ordering';
COMMENT ON TABLE orders IS 'Customer orders including table orders';

COMMENT ON COLUMN tables.status IS 'Table status: available, occupied, reserved, maintenance';
COMMENT ON COLUMN tables.shape IS 'Table shape: square, round, rectangle';
COMMENT ON COLUMN orders.order_type IS 'Order type: table_order, takeaway, delivery';
COMMENT ON COLUMN orders.status IS 'Order status: pending, confirmed, preparing, ready, completed, cancelled';
COMMENT ON COLUMN orders.items IS 'JSON array of order items with details';

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables
CREATE TRIGGER update_table_areas_updated_at BEFORE UPDATE ON table_areas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tables_updated_at BEFORE UPDATE ON tables FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
