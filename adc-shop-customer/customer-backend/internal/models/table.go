package models

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Table represents a restaurant table
type Table struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	BranchID    uuid.UUID      `json:"branch_id" gorm:"type:uuid;not null;index"`
	Number      int            `json:"number" gorm:"not null"`
	Name        string         `json:"name" gorm:"size:100"`
	Capacity    int            `json:"capacity" gorm:"not null"`
	MinCapacity *int           `json:"min_capacity,omitempty"`
	MaxCapacity *int           `json:"max_capacity,omitempty"`
	AreaID      *uuid.UUID     `json:"area_id,omitempty" gorm:"type:uuid"`
	Status      string         `json:"status" gorm:"size:50;default:'available'"` // available, occupied, reserved, maintenance
	QRCodeURL   *string        `json:"qr_code_url,omitempty"`
	ImageURL    *string        `json:"image_url,omitempty"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	Position    *TablePosition `json:"position,omitempty" gorm:"embedded;embeddedPrefix:pos_"`
	Shape       string         `json:"shape" gorm:"size:20;default:'square'"` // square, round, rectangle
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Branch *ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Area   *TableArea  `json:"area,omitempty" gorm:"foreignKey:AreaID"`
	Orders []Order     `json:"orders,omitempty" gorm:"foreignKey:TableID"`
}

// TablePosition represents the position and dimensions of a table in the layout
type TablePosition struct {
	X      float64 `json:"x"`
	Y      float64 `json:"y"`
	Width  float64 `json:"width"`
	Height float64 `json:"height"`
	Angle  float64 `json:"angle,omitempty"`
}

// TableArea represents a dining area within a branch
type TableArea struct {
	ID          uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	BranchID    uuid.UUID      `json:"branch_id" gorm:"type:uuid;not null;index"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	Description *string        `json:"description,omitempty"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	SortOrder   int            `json:"sort_order" gorm:"default:0"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Branch *ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Tables []Table     `json:"tables,omitempty" gorm:"foreignKey:AreaID"`
}

// TableMethods
func (t *Table) BeforeCreate(tx *gorm.DB) error {
	if t.ID == uuid.Nil {
		t.ID = uuid.New()
	}
	return nil
}

func (ta *TableArea) BeforeCreate(tx *gorm.DB) error {
	if ta.ID == uuid.Nil {
		ta.ID = uuid.New()
	}
	return nil
}

// IsAvailable checks if the table is available for ordering
func (t *Table) IsAvailable() bool {
	return t.IsActive && t.Status == "available"
}

// GetDisplayName returns a display name for the table
func (t *Table) GetDisplayName() string {
	if t.Name != "" {
		return t.Name
	}
	return fmt.Sprintf("Table %d", t.Number)
}
