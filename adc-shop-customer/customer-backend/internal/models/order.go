package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Order represents a customer order (matches existing merchant backend schema)
type Order struct {
	ID       uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	BranchID uuid.UUID  `json:"branch_id" gorm:"type:uuid;not null;index"`
	TableID  *uuid.UUID `json:"table_id,omitempty" gorm:"type:uuid;index"` // For table orders

	// Customer information
	CustomerID    *uuid.UUID `json:"customer_id,omitempty" gorm:"type:uuid;index"`
	CustomerName  *string    `json:"customer_name,omitempty" gorm:"size:255"`
	CustomerPhone *string    `json:"customer_phone,omitempty" gorm:"size:50"`
	CustomerEmail *string    `json:"customer_email,omitempty" gorm:"size:255"`

	// Order details
	OrderNumber string  `json:"order_number" gorm:"size:50;uniqueIndex;not null"`
	Type        string  `json:"type" gorm:"size:20;default:'dine-in'"`   // dine-in, takeaway, delivery
	Status      string  `json:"status" gorm:"size:20;default:'pending'"` // pending, confirmed, preparing, ready, completed, cancelled
	Notes       *string `json:"notes,omitempty" gorm:"type:text"`

	// Pricing
	Subtotal      float64 `json:"subtotal" gorm:"type:decimal(10,2);not null;default:0"`
	Tax           float64 `json:"tax" gorm:"type:decimal(10,2);not null;default:0"`
	ServiceCharge float64 `json:"service_charge" gorm:"type:decimal(10,2);not null;default:0"`
	Discount      float64 `json:"discount" gorm:"type:decimal(10,2);not null;default:0"`
	Tip           float64 `json:"tip" gorm:"type:decimal(10,2);not null;default:0"`
	Total         float64 `json:"total" gorm:"type:decimal(10,2);not null;default:0"`

	// Payment
	PaymentStatus    string  `json:"payment_status" gorm:"size:20;default:'pending'"` // pending, paid, failed, refunded
	PaymentMethod    *string `json:"payment_method,omitempty" gorm:"size:50"`
	PaymentIntentID  *string `json:"payment_intent_id,omitempty" gorm:"size:255"` // Stripe Payment Intent ID
	ConnectedAccount *string `json:"connected_account,omitempty" gorm:"size:255"` // Stripe Connected Account ID

	// Timestamps
	EstimatedTime *int64     `json:"estimated_time,omitempty"` // minutes
	ServedAt      *time.Time `json:"served_at,omitempty"`
	CompletedAt   *time.Time `json:"completed_at,omitempty"`
	CancelledAt   *time.Time `json:"cancelled_at,omitempty"`

	CancellationReason *string `json:"cancellation_reason,omitempty" gorm:"type:text"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Branch     *ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Table      *Table      `json:"table,omitempty" gorm:"foreignKey:TableID"`
	OrderItems []OrderItem `json:"order_items,omitempty" gorm:"foreignKey:OrderID"`
}

// OrderItem represents an item in an order (matches existing schema)
type OrderItem struct {
	ID            uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrderID       uuid.UUID  `json:"order_id" gorm:"type:uuid;not null;index"`
	MenuItemID    *uuid.UUID `json:"menu_item_id,omitempty" gorm:"type:uuid;index"`
	Name          string     `json:"name" gorm:"size:255;not null"`
	Price         float64    `json:"price" gorm:"type:decimal(10,2);not null"`
	Quantity      int64      `json:"quantity" gorm:"not null;default:1"`
	Modifications *string    `json:"modifications,omitempty" gorm:"type:jsonb"`
	Notes         *string    `json:"notes,omitempty" gorm:"type:text"`
	Total         float64    `json:"total" gorm:"type:decimal(10,2);not null"`
	Status        string     `json:"status" gorm:"size:20;default:'pending'"` // pending, preparing, ready, served

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Order *Order `json:"order,omitempty" gorm:"foreignKey:OrderID"`
}

// OrderItemCustomization represents customizations for an order item (for JSON storage)
type OrderItemCustomization struct {
	Name  string  `json:"name"`
	Value string  `json:"value"`
	Price float64 `json:"price,omitempty"`
}

// TableName returns the table name for Order model
func (Order) TableName() string {
	return "orders"
}

// BeforeCreate sets the ID and order number if not set
func (o *Order) BeforeCreate(tx *gorm.DB) error {
	if o.ID == uuid.Nil {
		o.ID = uuid.New()
	}

	if o.OrderNumber == "" {
		// Generate order number: ORD-YYYYMMDD-HHMMSS-XXXX
		timestamp := time.Now().Format("20060102-150405")
		suffix := o.ID.String()[:4]
		o.OrderNumber = fmt.Sprintf("ORD-%s-%s", timestamp, suffix)
	}

	return nil
}

func (oi *OrderItem) BeforeCreate(tx *gorm.DB) error {
	if oi.ID == uuid.Nil {
		oi.ID = uuid.New()
	}
	return nil
}

// IsTableOrder checks if this is a table order
func (o *Order) IsTableOrder() bool {
	return o.TableID != nil && o.Type == "dine-in"
}

// CanBeCancelled checks if the order can be cancelled
func (o *Order) CanBeCancelled() bool {
	return o.Status == "pending" || o.Status == "confirmed"
}

// CalculateTotal calculates the total amount for the order
func (o *Order) CalculateTotal() {
	o.Total = o.Subtotal + o.ServiceCharge + o.Tax + o.Tip - o.Discount
}

// OrderItems is a custom type for handling JSON marshaling/unmarshaling
type OrderItems []OrderItem

// Value implements the driver.Valuer interface for OrderItems
func (items OrderItems) Value() (driver.Value, error) {
	if items == nil {
		return nil, nil
	}
	return json.Marshal(items)
}

// Scan implements the sql.Scanner interface for OrderItems
func (items *OrderItems) Scan(value interface{}) error {
	if value == nil {
		*items = OrderItems{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into OrderItems", value)
	}

	return json.Unmarshal(bytes, items)
}
