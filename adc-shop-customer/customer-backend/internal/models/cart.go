package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// CartSession represents a cart session for both logged-in users and guests
type CartSession struct {
	ID        uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID    *uuid.UUID `json:"user_id,omitempty" gorm:"type:uuid;index"` // null for guest users
	SessionID string     `json:"session_id" gorm:"size:255;index"`         // for guest users
	ExpiresAt time.Time  `json:"expires_at"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	Items []CartItem `json:"items,omitempty" gorm:"foreignKey:CartSessionID"`
}

// CartItem represents an item in the cart
type CartItem struct {
	ID            uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CartSessionID uuid.UUID `json:"cart_session_id" gorm:"type:uuid;not null;index"`
	MenuItemID    uuid.UUID `json:"menu_item_id" gorm:"type:uuid;not null"`
	Quantity      int       `json:"quantity" gorm:"not null;default:1"`

	// Menu item details (cached for performance and consistency)
	Name        string  `json:"name" gorm:"size:255;not null"`
	Description string  `json:"description" gorm:"type:text"`
	Image       string  `json:"image" gorm:"size:500"`
	Price       float64 `json:"price" gorm:"type:decimal(10,2);not null"`

	// Shop and branch context for proper order routing
	ShopSlug   string `json:"shop_slug" gorm:"size:255;not null"`
	BranchSlug string `json:"branch_slug" gorm:"size:255;not null"`

	// Table context for QR ordering (optional)
	TableID     *uuid.UUID `json:"table_id,omitempty" gorm:"type:uuid"`
	TableNumber *string    `json:"table_name,omitempty" gorm:"size:100"`
	ShopID      *uuid.UUID `json:"shop_id,omitempty" gorm:"type:uuid"`
	BranchID    *uuid.UUID `json:"branch_id,omitempty" gorm:"type:uuid"`

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	CartSession *CartSession `json:"cart_session,omitempty" gorm:"foreignKey:CartSessionID"`
}

// TableName returns the table name for CartSession model
func (CartSession) TableName() string {
	return "cart_sessions"
}

// TableName returns the table name for CartItem model
func (CartItem) TableName() string {
	return "cart_items"
}

// IsExpired checks if the cart session has expired
func (cs *CartSession) IsExpired() bool {
	return time.Now().After(cs.ExpiresAt)
}

// GetTotalItems returns the total number of items in the cart
func (cs *CartSession) GetTotalItems() int {
	total := 0
	for _, item := range cs.Items {
		total += item.Quantity
	}
	return total
}

// GetTotalPrice returns the total price of all items in the cart
func (cs *CartSession) GetTotalPrice() float64 {
	total := 0.0
	for _, item := range cs.Items {
		total += item.Price * float64(item.Quantity)
	}
	return total
}

// GetBranchItems returns items for a specific shop and branch
func (cs *CartSession) GetBranchItems(shopSlug, branchSlug string) []CartItem {
	var branchItems []CartItem
	for _, item := range cs.Items {
		if item.ShopSlug == shopSlug && item.BranchSlug == branchSlug {
			branchItems = append(branchItems, item)
		}
	}
	return branchItems
}

// GetBranchTotals returns totals for a specific shop and branch
func (cs *CartSession) GetBranchTotals(shopSlug, branchSlug string) (int, float64) {
	branchItems := cs.GetBranchItems(shopSlug, branchSlug)
	totalItems := 0
	totalPrice := 0.0

	for _, item := range branchItems {
		totalItems += item.Quantity
		totalPrice += item.Price * float64(item.Quantity)
	}

	return totalItems, totalPrice
}
