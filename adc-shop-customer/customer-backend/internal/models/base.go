package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseModel contains common fields for all models (matches merchant backend)
type BaseModel struct {
	ID        uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// BeforeC<PERSON> will set a UUID rather than numeric ID
func (base *BaseModel) BeforeCreate(tx *gorm.DB) error {
	if base.ID == uuid.Nil {
		base.ID = uuid.New()
	}
	return nil
}

// Address represents a physical address with location coordinates (matches merchant backend structure)
type Address struct {
	Street           string     `json:"street" gorm:"type:varchar(255)"`
	City             string     `json:"city" gorm:"type:varchar(100)"`
	State            string     `json:"state" gorm:"type:varchar(100)"`
	ZipCode          string     `json:"zip_code" gorm:"type:varchar(20)"`
	Country          string     `json:"country" gorm:"type:varchar(100)"`
	Latitude         *float64   `json:"latitude" gorm:"type:decimal(10,8)"`
	Longitude        *float64   `json:"longitude" gorm:"type:decimal(11,8)"`
	LocationAccuracy *int       `json:"location_accuracy" gorm:"default:0"`
	GeocodedAt       *time.Time `json:"geocoded_at"`
	PlaceID          string     `json:"place_id" gorm:"type:varchar(255)"`
	FormattedAddress string     `json:"formatted_address" gorm:"type:text"`
	LocationType     string     `json:"location_type" gorm:"type:varchar(50);default:'manual'"` // manual, geocoded, gps, imported
}

// PaginatedResponse represents a paginated response structure
type PaginatedResponse[T any] struct {
	Data       []T `json:"data"`
	Total      int `json:"total"`
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	TotalPages int `json:"total_pages"`
}
