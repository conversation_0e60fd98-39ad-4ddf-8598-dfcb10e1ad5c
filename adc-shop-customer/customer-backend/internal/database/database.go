package database

import (
	"customer-backend/pkg/logger"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

// Initialize initializes the database connection
func Initialize(dsn string, logger *logger.Logger) (*gorm.DB, error) {
	// Configure GORM logger
	var gormLogLevel gormLogger.LogLevel
	switch logger.GetLevel() {
	case "debug":
		gormLogLevel = gormLogger.Info
	case "info":
		gormLogLevel = gormLogger.Warn
	default:
		gormLogLevel = gormLogger.Error
	}

	config := &gorm.Config{
		Logger: gormLogger.Default.LogMode(gormLogLevel),
	}

	// Open database connection
	db, err := gorm.Open(postgres.Open(dsn), config)
	if err != nil {
		return nil, err
	}

	// Get underlying sql.DB to configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// Configure connection pool
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)

	logger.Info("Database connection established")
	return db, nil
}
