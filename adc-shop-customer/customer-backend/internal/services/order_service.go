package services

import (
	"context"
	"customer-backend/internal/models"
	"customer-backend/internal/repositories"
	"customer-backend/pkg/logger"
	"fmt"

	"github.com/google/uuid"
)

type OrderService struct {
	orderRepo *repositories.OrderRepository
	tableRepo *repositories.TableRepository
	shopRepo  *repositories.ShopRepository
	logger    *logger.Logger
}

func NewOrderService(
	orderRepo *repositories.OrderRepository,
	tableRepo *repositories.TableRepository,
	shopRepo *repositories.ShopRepository,
	logger *logger.Logger,
) *OrderService {
	return &OrderService{
		orderRepo: orderRepo,
		tableRepo: tableRepo,
		shopRepo:  shopRepo,
		logger:    logger,
	}
}

// CreateTableOrder creates a new table order
func (s *OrderService) CreateTableOrder(ctx context.Context, req CreateTableOrderRequest) (*models.Order, error) {
	// Validate table
	table, err := s.tableRepo.ValidateTableForOrder(ctx, req.TableID, req.BranchID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to validate table for order")
		return nil, fmt.Errorf("failed to validate table: %w", err)
	}

	if table == nil {
		return nil, fmt.Errorf("table not found or not available")
	}

	// Calculate totals
	subtotal := s.calculateSubtotal(req.Items)
	serviceCharge := subtotal * 0.10 // 10% service charge
	tax := subtotal * 0.07           // 7% VAT
	total := subtotal + serviceCharge + tax

	// Create order
	order := &models.Order{
		BranchID:      req.BranchID,
		TableID:       &req.TableID,
		CustomerName:  &req.CustomerName,
		CustomerPhone: &req.CustomerPhone,
		CustomerEmail: req.CustomerEmail,
		Type:          "dine-in",
		Status:        "pending",
		Notes:         req.SpecialRequests,
		Subtotal:      subtotal,
		ServiceCharge: serviceCharge,
		Tax:           tax,
		Total:         total,
	}

	err = s.orderRepo.CreateOrder(ctx, order)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create order")
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	// Create order items
	for _, item := range req.Items {
		orderItem := &models.OrderItem{
			OrderID:  order.ID,
			Name:     item.Name,
			Price:    item.Price,
			Quantity: int64(item.Quantity),
			Total:    item.Price * float64(item.Quantity),
			Status:   "pending",
		}

		err = s.orderRepo.CreateOrderItem(ctx, orderItem)
		if err != nil {
			s.logger.WithError(err).Error("Failed to create order item")
			return nil, fmt.Errorf("failed to create order item: %w", err)
		}
	}

	s.logger.WithField("order_id", order.ID).Info("Table order created successfully")
	return order, nil
}

// GetOrderByID retrieves an order by its ID
func (s *OrderService) GetOrderByID(ctx context.Context, orderID uuid.UUID) (*models.Order, error) {
	order, err := s.orderRepo.GetOrderByID(ctx, orderID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get order by ID")
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	if order == nil {
		return nil, fmt.Errorf("order not found")
	}

	return order, nil
}

// GetOrderByNumber retrieves an order by its order number
func (s *OrderService) GetOrderByNumber(ctx context.Context, orderNumber string) (*models.Order, error) {
	order, err := s.orderRepo.GetOrderByNumber(ctx, orderNumber)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get order by number")
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	if order == nil {
		return nil, fmt.Errorf("order not found")
	}

	return order, nil
}

// GetOrdersByTable retrieves orders for a specific table
func (s *OrderService) GetOrdersByTable(ctx context.Context, tableID uuid.UUID, limit int) ([]models.Order, error) {
	orders, err := s.orderRepo.GetOrdersByTable(ctx, tableID, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get orders by table")
		return nil, fmt.Errorf("failed to get orders: %w", err)
	}

	return orders, nil
}

// GetOrdersByCustomer retrieves orders for a specific customer
func (s *OrderService) GetOrdersByCustomer(ctx context.Context, customerPhone string, limit int) ([]models.Order, error) {
	orders, err := s.orderRepo.GetOrdersByCustomer(ctx, customerPhone, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get orders by customer")
		return nil, fmt.Errorf("failed to get orders: %w", err)
	}

	return orders, nil
}

// UpdateOrderStatus updates the status of an order
func (s *OrderService) UpdateOrderStatus(ctx context.Context, orderID uuid.UUID, status string) error {
	// Validate status
	validStatuses := []string{"pending", "confirmed", "preparing", "ready", "completed", "cancelled"}
	isValid := false
	for _, validStatus := range validStatuses {
		if status == validStatus {
			isValid = true
			break
		}
	}

	if !isValid {
		return fmt.Errorf("invalid order status: %s", status)
	}

	err := s.orderRepo.UpdateOrderStatus(ctx, orderID, status)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update order status")
		return fmt.Errorf("failed to update order status: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"order_id": orderID,
		"status":   status,
	}).Info("Order status updated")

	return nil
}

// GetActiveOrdersByTable retrieves active orders for a table
func (s *OrderService) GetActiveOrdersByTable(ctx context.Context, tableID uuid.UUID) ([]models.Order, error) {
	orders, err := s.orderRepo.GetActiveOrdersByTable(ctx, tableID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get active orders by table")
		return nil, fmt.Errorf("failed to get active orders: %w", err)
	}

	return orders, nil
}

// calculateSubtotal calculates the subtotal for order items
func (s *OrderService) calculateSubtotal(items []OrderItemRequest) float64 {
	var subtotal float64
	for _, item := range items {
		itemTotal := item.Price * float64(item.Quantity)
		subtotal += itemTotal
	}
	return subtotal
}

// CreateTableOrderRequest represents the request to create a table order
type CreateTableOrderRequest struct {
	TableID         uuid.UUID          `json:"table_id" binding:"required"`
	BranchID        uuid.UUID          `json:"branch_id" binding:"required"`
	CustomerName    string             `json:"customer_name" binding:"required"`
	CustomerPhone   string             `json:"customer_phone" binding:"required"`
	CustomerEmail   *string            `json:"customer_email,omitempty"`
	Items           []OrderItemRequest `json:"items" binding:"required,min=1"`
	SpecialRequests *string            `json:"special_requests,omitempty"`
}

// OrderItemRequest represents an item in the order request
type OrderItemRequest struct {
	ID          string  `json:"id"`
	Name        string  `json:"name" binding:"required"`
	Description string  `json:"description,omitempty"`
	Price       float64 `json:"price" binding:"required,min=0"`
	Quantity    int     `json:"quantity" binding:"required,min=1"`
	ImageURL    string  `json:"image_url,omitempty"`
}
