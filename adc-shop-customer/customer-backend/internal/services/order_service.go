package services

import (
	"context"
	"customer-backend/internal/models"
	"customer-backend/internal/repositories"
	"customer-backend/pkg/logger"
	"fmt"
	"time"

	"github.com/google/uuid"
)

type OrderService struct {
	orderRepo     *repositories.OrderRepository
	tableRepo     *repositories.TableRepository
	shopRepo      *repositories.ShopRepository
	stripeService *StripeService
	logger        *logger.Logger
}

func NewOrderService(
	orderRepo *repositories.OrderRepository,
	tableRepo *repositories.TableRepository,
	shopRepo *repositories.ShopRepository,
	stripeService *StripeService,
	logger *logger.Logger,
) *OrderService {
	return &OrderService{
		orderRepo:     orderRepo,
		tableRepo:     tableRepo,
		shopRepo:      shopRepo,
		stripeService: stripeService,
		logger:        logger,
	}
}

// CreateTableOrder creates a new table order
func (s *OrderService) CreateTableOrder(ctx context.Context, req CreateTableOrderRequest) (*models.Order, error) {
	// Validate table
	table, err := s.tableRepo.ValidateTableForOrder(ctx, req.TableID, req.BranchID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to validate table for order")
		return nil, fmt.Errorf("failed to validate table: %w", err)
	}

	if table == nil {
		return nil, fmt.Errorf("table not found or not available")
	}

	// Calculate totals
	subtotal := s.calculateSubtotal(req.Items)
	serviceCharge := subtotal * 0.10 // 10% service charge
	tax := subtotal * 0.07           // 7% VAT
	total := subtotal + serviceCharge + tax

	// Create order
	order := &models.Order{
		BranchID:      req.BranchID,
		TableID:       &req.TableID,
		CustomerName:  &req.CustomerName,
		CustomerPhone: &req.CustomerPhone,
		CustomerEmail: req.CustomerEmail,
		Type:          "dine-in",
		Status:        "pending",
		Notes:         req.SpecialRequests,
		Subtotal:      subtotal,
		ServiceCharge: serviceCharge,
		Tax:           tax,
		Total:         total,
	}

	err = s.orderRepo.CreateOrder(ctx, order)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create order")
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	// Create order items
	for _, item := range req.Items {
		orderItem := &models.OrderItem{
			OrderID:  order.ID,
			Name:     item.Name,
			Price:    item.Price,
			Quantity: int64(item.Quantity),
			Total:    item.Price * float64(item.Quantity),
			Status:   "pending",
		}

		err = s.orderRepo.CreateOrderItem(ctx, orderItem)
		if err != nil {
			s.logger.WithError(err).Error("Failed to create order item")
			return nil, fmt.Errorf("failed to create order item: %w", err)
		}
	}

	s.logger.WithField("order_id", order.ID).Info("Table order created successfully")
	return order, nil
}

// CreateOrderWithPayment creates an order with integrated payment processing
func (s *OrderService) CreateOrderWithPayment(ctx context.Context, req CreateOrderWithPaymentRequest) (*CreateOrderWithPaymentResponse, error) {
	// Parse branch ID
	branchID, err := uuid.Parse(req.BranchID)
	if err != nil {
		return nil, fmt.Errorf("invalid branch ID: %w", err)
	}

	// Parse table ID if provided
	var tableID *uuid.UUID
	if req.TableID != nil && *req.TableID != "" {
		parsedTableID, err := uuid.Parse(*req.TableID)
		if err != nil {
			return nil, fmt.Errorf("invalid table ID: %w", err)
		}
		tableID = &parsedTableID
	}

	// Calculate totals
	subtotal := s.calculateSubtotal(req.Items)

	// Calculate service charge (only for dine-in orders)
	var serviceCharge float64
	if req.OrderType == "dine_in" {
		serviceCharge = subtotal * 0.10 // 10% service charge
	}

	// Calculate tax
	tax := subtotal * 0.08 // 8% tax

	// Calculate delivery fee (only for delivery orders)
	var deliveryFee float64
	if req.OrderType == "delivery" {
		deliveryFee = 2.99
	}

	total := subtotal + serviceCharge + tax + deliveryFee

	// Generate order number
	orderNumber := s.generateOrderNumber()

	// Create order
	order := &models.Order{
		BranchID:      branchID,
		TableID:       tableID,
		OrderNumber:   orderNumber,
		CustomerName:  &req.CustomerName,
		CustomerPhone: &req.CustomerPhone,
		CustomerEmail: req.CustomerEmail,
		Type:          req.OrderType,
		Status:        "pending",
		Notes:         req.Notes,
		Subtotal:      subtotal,
		ServiceCharge: serviceCharge,
		Tax:           tax,
		Total:         total,
	}

	err = s.orderRepo.CreateOrder(ctx, order)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create order")
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	// Create order items
	for _, item := range req.Items {
		// Use UnitPrice if available, otherwise fall back to Price
		price := item.UnitPrice
		if price == 0 {
			price = item.Price
		}

		// Use item name if provided, otherwise use a default
		name := item.Name
		if name == "" {
			name = "Menu Item"
		}

		orderItem := &models.OrderItem{
			OrderID:  order.ID,
			Name:     name,
			Price:    price,
			Quantity: int64(item.Quantity),
			Total:    price * float64(item.Quantity),
			Status:   "pending",
		}

		err = s.orderRepo.CreateOrderItem(ctx, orderItem)
		if err != nil {
			s.logger.WithError(err).Error("Failed to create order item")
			return nil, fmt.Errorf("failed to create order item: %w", err)
		}
	}

	// Prepare response
	response := &CreateOrderWithPaymentResponse{
		Order:           order,
		RequiresPayment: req.PaymentMethod == "stripe_connect",
		EstimatedTime:   stringPtr("15-20 mins"),
	}

	// Create payment intent if using Stripe Connect
	if req.PaymentMethod == "stripe_connect" {
		// For now, we'll use a placeholder connected account ID
		// In a real implementation, this would be retrieved from the branch/shop settings
		connectedAccountID := "acct_1RWGqRCvS6V6yiE9" // This should come from branch settings

		// Create real Stripe payment intent
		paymentIntent, err := s.stripeService.CreatePaymentIntent(ctx, order, connectedAccountID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to create payment intent")
			return nil, fmt.Errorf("failed to create payment intent: %w", err)
		}

		s.logger.WithFields(map[string]interface{}{
			"payment_intent_id": paymentIntent.ID,
			"client_secret":     paymentIntent.ClientSecret,
			"amount":            paymentIntent.Amount,
			"currency":          paymentIntent.Currency,
		}).Info("Payment intent created for order")

		response.PaymentIntent = &PaymentIntentResponse{
			PaymentIntentID:    paymentIntent.ID,
			ClientSecret:       paymentIntent.ClientSecret,
			ConnectedAccountID: connectedAccountID,
			Amount:             paymentIntent.Amount,
			Currency:           string(paymentIntent.Currency),
		}
		response.ConnectedAccountID = connectedAccountID
	}

	s.logger.WithField("order_id", order.ID).Info("Order with payment created successfully")
	return response, nil
}

// Helper function to generate order number
func (s *OrderService) generateOrderNumber() string {
	return fmt.Sprintf("ORD-%d", time.Now().Unix())
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}

// GetOrderByID retrieves an order by its ID
func (s *OrderService) GetOrderByID(ctx context.Context, orderID uuid.UUID) (*models.Order, error) {
	order, err := s.orderRepo.GetOrderByID(ctx, orderID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get order by ID")
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	if order == nil {
		return nil, fmt.Errorf("order not found")
	}

	return order, nil
}

// GetOrderByNumber retrieves an order by its order number
func (s *OrderService) GetOrderByNumber(ctx context.Context, orderNumber string) (*models.Order, error) {
	order, err := s.orderRepo.GetOrderByNumber(ctx, orderNumber)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get order by number")
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	if order == nil {
		return nil, fmt.Errorf("order not found")
	}

	return order, nil
}

// GetOrdersByTable retrieves orders for a specific table
func (s *OrderService) GetOrdersByTable(ctx context.Context, tableID uuid.UUID, limit int) ([]models.Order, error) {
	orders, err := s.orderRepo.GetOrdersByTable(ctx, tableID, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get orders by table")
		return nil, fmt.Errorf("failed to get orders: %w", err)
	}

	return orders, nil
}

// GetOrdersByCustomer retrieves orders for a specific customer
func (s *OrderService) GetOrdersByCustomer(ctx context.Context, customerPhone string, limit int) ([]models.Order, error) {
	orders, err := s.orderRepo.GetOrdersByCustomer(ctx, customerPhone, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get orders by customer")
		return nil, fmt.Errorf("failed to get orders: %w", err)
	}

	return orders, nil
}

// UpdateOrderStatus updates the status of an order
func (s *OrderService) UpdateOrderStatus(ctx context.Context, orderID uuid.UUID, status string) error {
	// Validate status
	validStatuses := []string{"pending", "confirmed", "preparing", "ready", "completed", "cancelled"}
	isValid := false
	for _, validStatus := range validStatuses {
		if status == validStatus {
			isValid = true
			break
		}
	}

	if !isValid {
		return fmt.Errorf("invalid order status: %s", status)
	}

	err := s.orderRepo.UpdateOrderStatus(ctx, orderID, status)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update order status")
		return fmt.Errorf("failed to update order status: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"order_id": orderID,
		"status":   status,
	}).Info("Order status updated")

	return nil
}

// GetActiveOrdersByTable retrieves active orders for a table
func (s *OrderService) GetActiveOrdersByTable(ctx context.Context, tableID uuid.UUID) ([]models.Order, error) {
	orders, err := s.orderRepo.GetActiveOrdersByTable(ctx, tableID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get active orders by table")
		return nil, fmt.Errorf("failed to get active orders: %w", err)
	}

	return orders, nil
}

// calculateSubtotal calculates the subtotal for order items
func (s *OrderService) calculateSubtotal(items []OrderItemRequest) float64 {
	var subtotal float64
	for _, item := range items {
		// Use UnitPrice if available, otherwise fall back to Price
		price := item.UnitPrice
		if price == 0 {
			price = item.Price
		}
		itemTotal := price * float64(item.Quantity)
		subtotal += itemTotal
	}
	return subtotal
}

// CreateTableOrderRequest represents the request to create a table order
type CreateTableOrderRequest struct {
	TableID         uuid.UUID          `json:"table_id" binding:"required"`
	BranchID        uuid.UUID          `json:"branch_id" binding:"required"`
	CustomerName    string             `json:"customer_name" binding:"required"`
	CustomerPhone   string             `json:"customer_phone" binding:"required"`
	CustomerEmail   *string            `json:"customer_email,omitempty"`
	Items           []OrderItemRequest `json:"items" binding:"required,min=1"`
	SpecialRequests *string            `json:"special_requests,omitempty"`
}

// CreateOrderWithPaymentRequest represents the request to create an order with payment
type CreateOrderWithPaymentRequest struct {
	BranchID      string             `json:"branch_id" binding:"required"`
	TableID       *string            `json:"table_id,omitempty"`
	CustomerName  string             `json:"customer_name" binding:"required"`
	CustomerPhone string             `json:"customer_phone" binding:"required"`
	CustomerEmail *string            `json:"customer_email,omitempty"`
	OrderType     string             `json:"order_type" binding:"required"` // dine_in, takeaway, delivery
	Items         []OrderItemRequest `json:"items" binding:"required,min=1"`
	Notes         *string            `json:"notes,omitempty"`
	PaymentMethod string             `json:"payment_method" binding:"required"` // stripe_connect, cash
	Metadata      map[string]string  `json:"metadata,omitempty"`
}

// CreateOrderWithPaymentResponse represents the response from creating an order with payment
type CreateOrderWithPaymentResponse struct {
	Order              *models.Order          `json:"order"`
	PaymentIntent      *PaymentIntentResponse `json:"payment_intent,omitempty"`
	ConnectedAccountID string                 `json:"connected_account_id,omitempty"`
	RequiresPayment    bool                   `json:"requires_payment"`
	EstimatedTime      *string                `json:"estimated_time,omitempty"`
}

// PaymentIntentResponse represents payment intent information
type PaymentIntentResponse struct {
	PaymentIntentID    string `json:"payment_intent_id"`
	ClientSecret       string `json:"client_secret"`
	ConnectedAccountID string `json:"connected_account_id"`
	Amount             int64  `json:"amount"`
	Currency           string `json:"currency"`
}

// OrderItemRequest represents an item in the order request
type OrderItemRequest struct {
	ID              string        `json:"id"`
	MenuItemID      string        `json:"menu_item_id"`
	Name            string        `json:"name"`
	Description     string        `json:"description,omitempty"`
	Price           float64       `json:"price"`
	UnitPrice       float64       `json:"unit_price"`
	Quantity        int           `json:"quantity" binding:"required,min=1"`
	Customizations  []interface{} `json:"customizations"`
	SpecialRequests string        `json:"special_requests"`
	ImageURL        string        `json:"image_url,omitempty"`
}
