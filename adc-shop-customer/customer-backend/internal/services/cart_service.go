package services

import (
	"context"
	"customer-backend/internal/models"
	"customer-backend/internal/repositories"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"
	"fmt"

	"github.com/google/uuid"
)

type CartService struct {
	cartRepo *repositories.CartRepository
	menuRepo *repositories.MenuRepository
	logger   *logger.Logger
}

func NewCartService(cartRepo *repositories.CartRepository, menuRepo *repositories.MenuRepository, logger *logger.Logger) *CartService {
	return &CartService{
		cartRepo: cartRepo,
		menuRepo: menuRepo,
		logger:   logger,
	}
}

// GetCart retrieves the cart for a user or guest session
func (s *CartService) GetCart(ctx context.Context, userID *uuid.UUID, sessionID string) (*types.CartResponse, error) {
	cartSession, err := s.cartRepo.GetOrCreateCartSession(ctx, userID, sessionID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get cart session")
		return &types.CartResponse{
			Success: false,
			Message: "Failed to get cart",
		}, err
	}

	// Convert to response format
	cartData := &types.CartSessionData{
		ID:        cartSession.ID.String(),
		UserID:    nil,
		SessionID: cartSession.SessionID,
		Items:     s.convertCartItemsToResponse(cartSession.Items),
		CreatedAt: cartSession.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt: cartSession.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		ExpiresAt: cartSession.ExpiresAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	if cartSession.UserID != nil {
		userIDStr := cartSession.UserID.String()
		cartData.UserID = &userIDStr
	}

	return &types.CartResponse{
		Success: true,
		Data:    cartData,
		Message: "Cart retrieved successfully",
	}, nil
}

// AddToCart adds an item to the cart
func (s *CartService) AddToCart(ctx context.Context, userID *uuid.UUID, sessionID string, req *types.AddToCartRequest) (*types.CartResponse, error) {
	// Get or create cart session
	cartSession, err := s.cartRepo.GetOrCreateCartSession(ctx, userID, sessionID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get cart session")
		return &types.CartResponse{
			Success: false,
			Message: "Failed to get cart",
		}, err
	}

	// Validate menu item exists and get details
	menuItemID, err := uuid.Parse(req.Item.ID)
	if err != nil {
		return &types.CartResponse{
			Success: false,
			Message: "Invalid menu item ID",
		}, fmt.Errorf("invalid menu item ID: %w", err)
	}

	// For now, we'll use the item details from the request
	// In a production system, you might want to fetch fresh data from the menu service
	cartItem := &models.CartItem{
		MenuItemID:  menuItemID,
		Quantity:    req.Quantity,
		Name:        req.Item.Name,
		Description: req.Item.Description,
		Image:       req.Item.Image,
		Price:       req.Item.Price,
		ShopSlug:    req.Item.ShopSlug,
		BranchSlug:  req.Item.BranchSlug,
	}

	// Add table context if provided
	if req.Item.TableContext != nil {
		if req.Item.TableContext.TableID != "" {
			tableID, err := uuid.Parse(req.Item.TableContext.TableID)
			if err == nil {
				cartItem.TableID = &tableID
			}
		}
		cartItem.TableNumber = &req.Item.TableContext.TableName
		if req.Item.TableContext.ShopID != "" {
			shopID, err := uuid.Parse(req.Item.TableContext.ShopID)
			if err == nil {
				cartItem.ShopID = &shopID
			}
		}
		if req.Item.TableContext.BranchID != "" {
			branchID, err := uuid.Parse(req.Item.TableContext.BranchID)
			if err == nil {
				cartItem.BranchID = &branchID
			}
		}
	}

	// Add item to cart
	err = s.cartRepo.AddItem(ctx, cartSession.ID, cartItem)
	if err != nil {
		s.logger.WithError(err).Error("Failed to add item to cart")
		return &types.CartResponse{
			Success: false,
			Message: "Failed to add item to cart",
		}, err
	}

	// Return updated cart
	return s.GetCart(ctx, userID, sessionID)
}

// UpdateQuantity updates the quantity of a cart item
func (s *CartService) UpdateQuantity(ctx context.Context, userID *uuid.UUID, sessionID string, req *types.UpdateQuantityRequest) (*types.CartResponse, error) {
	// Get cart session
	cartSession, err := s.cartRepo.GetOrCreateCartSession(ctx, userID, sessionID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get cart session")
		return &types.CartResponse{
			Success: false,
			Message: "Failed to get cart",
		}, err
	}

	// Parse item ID
	itemID, err := uuid.Parse(req.ItemID)
	if err != nil {
		return &types.CartResponse{
			Success: false,
			Message: "Invalid item ID",
		}, fmt.Errorf("invalid item ID: %w", err)
	}

	// Update quantity
	err = s.cartRepo.UpdateItemQuantity(ctx, cartSession.ID, itemID, req.Quantity)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update item quantity")
		return &types.CartResponse{
			Success: false,
			Message: "Failed to update item quantity",
		}, err
	}

	// Return updated cart
	return s.GetCart(ctx, userID, sessionID)
}

// RemoveFromCart removes an item from the cart
func (s *CartService) RemoveFromCart(ctx context.Context, userID *uuid.UUID, sessionID string, req *types.RemoveFromCartRequest) (*types.CartResponse, error) {
	// Get cart session
	cartSession, err := s.cartRepo.GetOrCreateCartSession(ctx, userID, sessionID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get cart session")
		return &types.CartResponse{
			Success: false,
			Message: "Failed to get cart",
		}, err
	}

	// Parse item ID
	itemID, err := uuid.Parse(req.ItemID)
	if err != nil {
		return &types.CartResponse{
			Success: false,
			Message: "Invalid item ID",
		}, fmt.Errorf("invalid item ID: %w", err)
	}

	// Remove item
	err = s.cartRepo.RemoveItem(ctx, cartSession.ID, itemID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to remove item from cart")
		return &types.CartResponse{
			Success: false,
			Message: "Failed to remove item from cart",
		}, err
	}

	// Return updated cart
	return s.GetCart(ctx, userID, sessionID)
}

// ClearCart removes all items from the cart
func (s *CartService) ClearCart(ctx context.Context, userID *uuid.UUID, sessionID string) (*types.CartResponse, error) {
	// Get cart session
	cartSession, err := s.cartRepo.GetOrCreateCartSession(ctx, userID, sessionID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get cart session")
		return &types.CartResponse{
			Success: false,
			Message: "Failed to get cart",
		}, err
	}

	// Clear cart
	err = s.cartRepo.ClearCart(ctx, cartSession.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to clear cart")
		return &types.CartResponse{
			Success: false,
			Message: "Failed to clear cart",
		}, err
	}

	// Return updated cart
	return s.GetCart(ctx, userID, sessionID)
}

// ClearBranchCart removes all items for a specific shop and branch
func (s *CartService) ClearBranchCart(ctx context.Context, userID *uuid.UUID, sessionID string, req *types.ClearBranchCartRequest) (*types.CartResponse, error) {
	// Get cart session
	cartSession, err := s.cartRepo.GetOrCreateCartSession(ctx, userID, sessionID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get cart session")
		return &types.CartResponse{
			Success: false,
			Message: "Failed to get cart",
		}, err
	}

	// Clear branch cart
	err = s.cartRepo.ClearBranchCart(ctx, cartSession.ID, req.ShopSlug, req.BranchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to clear branch cart")
		return &types.CartResponse{
			Success: false,
			Message: "Failed to clear branch cart",
		}, err
	}

	// Return updated cart
	return s.GetCart(ctx, userID, sessionID)
}

// SyncCartOnLogin merges guest cart with user cart when user logs in
func (s *CartService) SyncCartOnLogin(ctx context.Context, userID uuid.UUID, guestSessionID string) (*types.CartResponse, error) {
	// Sync guest cart to user cart
	err := s.cartRepo.SyncGuestCartToUser(ctx, userID, guestSessionID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to sync guest cart to user")
		return &types.CartResponse{
			Success: false,
			Message: "Failed to sync cart",
		}, err
	}

	// Return updated user cart
	return s.GetCart(ctx, &userID, "")
}

// Helper function to convert cart items to response format
func (s *CartService) convertCartItemsToResponse(items []models.CartItem) []types.CartItemResponse {
	var responseItems []types.CartItemResponse

	for _, item := range items {
		responseItem := types.CartItemResponse{
			ID:          item.ID.String(),
			Name:        item.Name,
			Description: item.Description,
			Image:       item.Image,
			Price:       item.Price,
			Quantity:    item.Quantity,
			ShopSlug:    item.ShopSlug,
			BranchSlug:  item.BranchSlug,
		}

		// Add table context if available
		if item.TableID != nil || item.TableNumber != nil || item.ShopID != nil || item.BranchID != nil {
			responseItem.TableContext = &types.TableContext{}
			if item.TableID != nil {
				responseItem.TableContext.TableID = item.TableID.String()
			}
			if item.TableNumber != nil {
				responseItem.TableContext.TableName = *item.TableNumber
			}
			if item.ShopID != nil {
				responseItem.TableContext.ShopID = item.ShopID.String()
			}
			if item.BranchID != nil {
				responseItem.TableContext.BranchID = item.BranchID.String()
			}
		}

		responseItems = append(responseItems, responseItem)
	}

	return responseItems
}
