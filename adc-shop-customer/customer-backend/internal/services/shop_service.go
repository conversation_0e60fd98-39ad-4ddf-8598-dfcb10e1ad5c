package services

import (
	"context"
	"customer-backend/internal/repositories"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"
	"customer-backend/pkg/pagination"
	"fmt"

	"github.com/google/uuid"
)

// ShopService handles business logic for shop operations
type ShopService struct {
	shopRepo *repositories.ShopRepository
	logger   *logger.Logger
}

// NewShopService creates a new shop service
func NewShopService(shopRepo *repositories.ShopRepository, logger *logger.Logger) *ShopService {
	return &ShopService{
		shopRepo: shopRepo,
		logger:   logger,
	}
}

// GetShopSettings retrieves shop settings for customer view
func (s *ShopService) GetShopSettings(ctx context.Context, shopID uuid.UUID) (*types.CustomerShopSettings, error) {
	s.logger.Infof("Getting shop settings for shop ID: %s", shopID.String())

	shop, err := s.shopRepo.GetShopSettings(ctx, shopID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get shop settings")
		return nil, fmt.Errorf("failed to get shop settings: %w", err)
	}

	// Add any business logic transformations here
	s.enrichShopSettings(shop)

	return shop, nil
}

// GetShopSettingsBySlug retrieves shop settings by slug for customer view
func (s *ShopService) GetShopSettingsBySlug(ctx context.Context, slug string) (*types.CustomerShopSettings, error) {
	s.logger.Infof("Getting shop settings for shop slug: %s", slug)

	shop, err := s.shopRepo.GetShopSettingsBySlug(ctx, slug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get shop settings by slug")
		return nil, fmt.Errorf("failed to get shop settings: %w", err)
	}

	// Add any business logic transformations here
	s.enrichShopSettings(shop)

	return shop, nil
}

// GetShopOperatingStatusBySlug checks if a shop is currently open by slug
func (s *ShopService) GetShopOperatingStatusBySlug(ctx context.Context, slug string) (bool, error) {
	shop, err := s.shopRepo.GetShopSettingsBySlug(ctx, slug)
	if err != nil {
		return false, err
	}

	return shop.IsOpen, nil
}

// GetShops retrieves shops based on customer filters with pagination
func (s *ShopService) GetShops(ctx context.Context, filters types.ShopFilters) (*types.ShopListResponse, error) {
	s.logger.Infof("Getting shops with filters: page=%d, limit=%d", filters.Page, filters.Limit)

	// Apply defaults
	filters.CustomerPagination.ApplyDefaults()
	filters.CustomerSorting.ApplyDefaults()

	// Validate and adjust limits
	if filters.Limit > 100 {
		filters.Limit = 100
	}

	shops, total, err := s.shopRepo.GetShopsByFilters(ctx, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get shops")
		return nil, fmt.Errorf("failed to get shops: %w", err)
	}

	// Create pagination info with center pagination
	paginationConfig := pagination.DefaultCenterConfig()
	paginationInfo := pagination.CreatePaginationInfo(total, filters.Page, filters.Limit, paginationConfig)

	// Create response
	response := &types.ShopListResponse{
		CustomerResponse: types.CreateCustomerResponse(
			nil, // Don't set data here since ShopListResponse has its own Data field
			"Shops retrieved successfully",
			paginationInfo,
		),
	}

	// Set the shops data in the specific Data field
	response.Data.Shops = shops

	return response, nil
}

// GetNearbyShops retrieves shops near a specific location
func (s *ShopService) GetNearbyShops(ctx context.Context, latitude, longitude, radius float64, filters types.ShopFilters) (*types.ShopListResponse, error) {
	s.logger.Infof("Getting nearby shops: lat=%f, lng=%f, radius=%f", latitude, longitude, radius)

	// Set location filters
	filters.Latitude = &latitude
	filters.Longitude = &longitude
	filters.Radius = &radius

	// Default sorting for nearby shops
	if filters.SortBy == "" {
		filters.SortBy = "distance"
	}

	return s.GetShops(ctx, filters)
}

// SearchShops searches for shops based on query string
func (s *ShopService) SearchShops(ctx context.Context, query string, filters types.ShopFilters) (*types.ShopListResponse, error) {
	s.logger.Infof("Searching shops with query: %s", query)

	// Set search filter
	filters.Search = query

	// Default sorting for search results
	if filters.SortBy == "" {
		filters.SortBy = "rating"
	}

	return s.GetShops(ctx, filters)
}

// GetPopularShops retrieves popular shops
func (s *ShopService) GetPopularShops(ctx context.Context, filters types.ShopFilters) (*types.ShopListResponse, error) {
	s.logger.Info("Getting popular shops")

	// Set filters for popular shops
	minRating := 4.0
	filters.MinRating = &minRating
	filters.SortBy = "rating"
	filters.SortOrder = "desc"

	return s.GetShops(ctx, filters)
}

// GetShopsByCategory retrieves shops by cuisine category
func (s *ShopService) GetShopsByCategory(ctx context.Context, category string, filters types.ShopFilters) (*types.ShopListResponse, error) {
	s.logger.Infof("Getting shops by category: %s", category)

	// Set category filter
	filters.CuisineType = []string{category}

	return s.GetShops(ctx, filters)
}

// ValidateShopAccess checks if a shop is accessible to customers
func (s *ShopService) ValidateShopAccess(ctx context.Context, shopID uuid.UUID) error {
	shop, err := s.shopRepo.GetShopSettings(ctx, shopID)
	if err != nil {
		return fmt.Errorf("shop not found or not accessible: %w", err)
	}

	if !shop.IsActive {
		return fmt.Errorf("shop is not active")
	}

	// Add any additional access validation logic here
	// For example, check if shop is temporarily closed, etc.

	return nil
}

// GetShopOperatingStatus checks if a shop is currently open
func (s *ShopService) GetShopOperatingStatus(ctx context.Context, shopID uuid.UUID) (bool, error) {
	shop, err := s.shopRepo.GetShopSettings(ctx, shopID)
	if err != nil {
		return false, err
	}

	return shop.IsOpen, nil
}

// GetBranchBySlug retrieves branch information with shop details by slugs
func (s *ShopService) GetBranchBySlug(ctx context.Context, shopSlug, branchSlug string) (interface{}, error) {
	s.logger.Infof("Getting branch by slugs: shop=%s, branch=%s", shopSlug, branchSlug)

	// Get the branch from repository
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("failed to get branch: %w", err)
	}

	// Get the shop information
	shop, err := s.shopRepo.GetShopSettingsBySlug(ctx, shopSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get shop by slug")
		return nil, fmt.Errorf("failed to get shop: %w", err)
	}

	// Create a combined response with branch and shop information
	response := map[string]interface{}{
		"id":      branch.ID.String(),
		"name":    branch.Name,
		"slug":    branch.Slug,
		"shop_id": branch.ShopID.String(),
		"email":   branch.Email,
		"phone":   branch.Phone,
		"address": map[string]interface{}{
			"street":   branch.AddressStreet,
			"city":     branch.AddressCity,
			"state":    branch.AddressState,
			"zip_code": branch.AddressZipCode,
			"country":  branch.AddressCountry,
		},
		"settings": map[string]interface{}{
			"tax_rate":            branch.Settings.TaxRate,
			"service_charge_rate": branch.Settings.ServiceChargeRate,
			"payment_methods":     branch.Settings.PaymentMethods,
			"online_ordering":     getFeatureSetting(branch.Settings.Features, "online_ordering"),
			"delivery_enabled":    getFeatureSetting(branch.Settings.Features, "delivery_enabled"),
			"pickup_enabled":      getFeatureSetting(branch.Settings.Features, "pickup_enabled"),
		},
		"shop": map[string]interface{}{
			"id":          shop.ID,
			"name":        shop.Name,
			"slug":        shop.Slug,
			"description": shop.Description,
			"logo":        shop.Logo,
			"cover_image": shop.CoverImage,
		},
	}

	return response, nil
}

// Helper function to get feature setting with default value
func getFeatureSetting(features map[string]bool, key string) bool {
	if features == nil {
		return false
	}
	value, exists := features[key]
	return exists && value
}

// Helper methods

// enrichShopSettings adds additional computed fields to shop settings
func (s *ShopService) enrichShopSettings(shop *types.CustomerShopSettings) {
	// Add computed fields or business logic transformations

	// Example: Calculate distance if user location is available
	// This would require user context

	// Example: Add promotional information
	// shop.HasActivePromotions = s.checkActivePromotions(shop.ID)

	// Example: Add estimated delivery time
	// shop.EstimatedDeliveryTime = s.calculateDeliveryTime(shop.ID)

	// For now, just ensure all required fields are set
	if shop.Currency == "" {
		shop.Currency = "USD"
	}

	if shop.Theme.PrimaryColor == "" {
		shop.Theme.PrimaryColor = "#3B82F6"
	}
}

// GetShopFilterOptions retrieves available filter options for shops
func (s *ShopService) GetShopFilterOptions(ctx context.Context) (*types.ShopFilterOptions, error) {
	// Query the database for actual available options
	cuisineTypes, err := s.shopRepo.GetAvailableCuisineTypes(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get cuisine types")
		// Fall back to default options
		cuisineTypes = []string{"Thai", "general"}
	}

	priceRanges, err := s.shopRepo.GetAvailablePriceRanges(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get price ranges")
		// Fall back to default options
		priceRanges = []string{"$", "$$", "$$$", "$$$$", "medium"}
	}

	return &types.ShopFilterOptions{
		CuisineTypes: cuisineTypes,
		PriceRanges:  priceRanges,
		Features: []string{
			"delivery", "pickup", "reservations", "online_ordering",
			"loyalty_program", "gift_cards",
		},
	}, nil
}
