package services

import (
	"context"
	"customer-backend/internal/config"
	"customer-backend/internal/models"
	"customer-backend/pkg/logger"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/stripe/stripe-go/v76"
	"github.com/stripe/stripe-go/v76/paymentintent"
	"github.com/stripe/stripe-go/v76/webhook"
)

type StripeService struct {
	config *config.Config
	logger *logger.Logger
}

func NewStripeService(config *config.Config, logger *logger.Logger) *StripeService {
	// Initialize Stripe with secret key
	stripe.Key = config.Stripe.SecretKey

	return &StripeService{
		config: config,
		logger: logger,
	}
}

// CreatePaymentIntent creates a Stripe Payment Intent for an order
func (s *StripeService) CreatePaymentIntent(ctx context.Context, order *models.Order, connectedAccountID string) (*stripe.PaymentIntent, error) {
	// Convert amount to cents (Stripe expects amounts in smallest currency unit)
	amountInCents := int64(order.Total * 100)

	// Calculate platform fee
	platformFeeAmount := (amountInCents * s.config.Stripe.PlatformFee) / 10000

	params := &stripe.PaymentIntentParams{
		Amount:   stripe.Int64(amountInCents),
		Currency: stripe.String(s.config.Stripe.Currency),
		Metadata: map[string]string{
			"order_id":      order.ID.String(),
			"order_number":  order.OrderNumber,
			"branch_id":     order.BranchID.String(),
			"customer_name": *order.CustomerName,
		},
		// Automatic payment methods
		AutomaticPaymentMethods: &stripe.PaymentIntentAutomaticPaymentMethodsParams{
			Enabled: stripe.Bool(true),
		},
	}

	// Add Connect transfer for valid connected accounts
	// For Thailand: Use direct charges with connected accounts created via Connect onboarding
	if connectedAccountID != "" && !strings.HasPrefix(connectedAccountID, "acct_test_") {
		params.TransferData = &stripe.PaymentIntentTransferDataParams{
			Destination: stripe.String(connectedAccountID),
		}
		params.ApplicationFeeAmount = stripe.Int64(platformFeeAmount)

		s.logger.WithFields(map[string]interface{}{
			"connected_account": connectedAccountID,
			"platform_fee":      platformFeeAmount,
			"amount":            amountInCents,
		}).Info("Creating payment intent with Connect transfer for Thailand marketplace")
	}

	// Add table information if it's a table order
	if order.TableID != nil {
		params.Metadata["table_id"] = order.TableID.String()
		params.Metadata["order_type"] = "table_order"
	}

	pi, err := paymentintent.New(params)
	if err != nil {
		s.logger.WithError(err).Error("Failed to create payment intent")
		return nil, fmt.Errorf("failed to create payment intent: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"payment_intent_id": pi.ID,
		"order_id":          order.ID,
		"amount":            amountInCents,
		"platform_fee":      platformFeeAmount,
		"connected_account": connectedAccountID,
	}).Info("Payment intent created successfully")

	return pi, nil
}

// ConfirmPaymentIntent confirms a payment intent
func (s *StripeService) ConfirmPaymentIntent(ctx context.Context, paymentIntentID string) (*stripe.PaymentIntent, error) {
	params := &stripe.PaymentIntentConfirmParams{}

	pi, err := paymentintent.Confirm(paymentIntentID, params)
	if err != nil {
		s.logger.WithError(err).Error("Failed to confirm payment intent")
		return nil, fmt.Errorf("failed to confirm payment intent: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"payment_intent_id": pi.ID,
		"status":            pi.Status,
	}).Info("Payment intent confirmed")

	return pi, nil
}

// GetPaymentIntent retrieves a payment intent
func (s *StripeService) GetPaymentIntent(ctx context.Context, paymentIntentID string) (*stripe.PaymentIntent, error) {
	pi, err := paymentintent.Get(paymentIntentID, nil)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get payment intent")
		return nil, fmt.Errorf("failed to get payment intent: %w", err)
	}

	return pi, nil
}

// CancelPaymentIntent cancels a payment intent
func (s *StripeService) CancelPaymentIntent(ctx context.Context, paymentIntentID string) (*stripe.PaymentIntent, error) {
	params := &stripe.PaymentIntentCancelParams{}

	pi, err := paymentintent.Cancel(paymentIntentID, params)
	if err != nil {
		s.logger.WithError(err).Error("Failed to cancel payment intent")
		return nil, fmt.Errorf("failed to cancel payment intent: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"payment_intent_id": pi.ID,
		"status":            pi.Status,
	}).Info("Payment intent cancelled")

	return pi, nil
}

// VerifyWebhookSignature verifies Stripe webhook signature
func (s *StripeService) VerifyWebhookSignature(payload []byte, signature string) (stripe.Event, error) {
	// Use ConstructEventWithOptions to ignore API version mismatch
	event, err := webhook.ConstructEventWithOptions(
		payload,
		signature,
		s.config.Stripe.WebhookSecret,
		webhook.ConstructEventOptions{
			IgnoreAPIVersionMismatch: true,
		},
	)
	if err != nil {
		s.logger.WithError(err).Error("Failed to verify webhook signature")
		return event, fmt.Errorf("failed to verify webhook signature: %w", err)
	}

	return event, nil
}

// ProcessWebhookEvent processes Stripe webhook events
func (s *StripeService) ProcessWebhookEvent(ctx context.Context, event stripe.Event) error {
	s.logger.WithFields(map[string]interface{}{
		"event_type": event.Type,
		"event_id":   event.ID,
	}).Info("Processing Stripe webhook event")

	switch event.Type {
	case "payment_intent.succeeded":
		return s.handlePaymentIntentSucceeded(ctx, event)
	case "payment_intent.payment_failed":
		return s.handlePaymentIntentFailed(ctx, event)
	case "payment_intent.canceled":
		return s.handlePaymentIntentCanceled(ctx, event)
	default:
		s.logger.WithField("event_type", event.Type).Info("Unhandled webhook event type")
		return nil
	}
}

// handlePaymentIntentSucceeded handles successful payment
func (s *StripeService) handlePaymentIntentSucceeded(ctx context.Context, event stripe.Event) error {
	var paymentIntent stripe.PaymentIntent
	err := json.Unmarshal(event.Data.Raw, &paymentIntent)
	if err != nil {
		return fmt.Errorf("failed to parse payment intent: %w", err)
	}

	orderID := paymentIntent.Metadata["order_id"]
	if orderID == "" {
		return fmt.Errorf("order_id not found in payment intent metadata")
	}

	s.logger.WithFields(map[string]interface{}{
		"payment_intent_id": paymentIntent.ID,
		"order_id":          orderID,
		"amount":            paymentIntent.Amount,
	}).Info("Payment succeeded")

	// Here you would update the order status to "paid"
	// This would typically involve calling the order service

	return nil
}

// handlePaymentIntentFailed handles failed payment
func (s *StripeService) handlePaymentIntentFailed(ctx context.Context, event stripe.Event) error {
	var paymentIntent stripe.PaymentIntent
	err := json.Unmarshal(event.Data.Raw, &paymentIntent)
	if err != nil {
		return fmt.Errorf("failed to parse payment intent: %w", err)
	}

	orderID := paymentIntent.Metadata["order_id"]
	if orderID == "" {
		return fmt.Errorf("order_id not found in payment intent metadata")
	}

	s.logger.WithFields(map[string]interface{}{
		"payment_intent_id":  paymentIntent.ID,
		"order_id":           orderID,
		"last_payment_error": paymentIntent.LastPaymentError,
	}).Error("Payment failed")

	// Here you would update the order payment status to "failed"

	return nil
}

// handlePaymentIntentCanceled handles canceled payment
func (s *StripeService) handlePaymentIntentCanceled(ctx context.Context, event stripe.Event) error {
	var paymentIntent stripe.PaymentIntent
	err := json.Unmarshal(event.Data.Raw, &paymentIntent)
	if err != nil {
		return fmt.Errorf("failed to parse payment intent: %w", err)
	}

	orderID := paymentIntent.Metadata["order_id"]
	if orderID == "" {
		return fmt.Errorf("order_id not found in payment intent metadata")
	}

	s.logger.WithFields(map[string]interface{}{
		"payment_intent_id": paymentIntent.ID,
		"order_id":          orderID,
	}).Info("Payment canceled")

	// Here you would update the order payment status to "canceled"

	return nil
}

// CalculatePlatformFee calculates the platform fee for an amount
func (s *StripeService) CalculatePlatformFee(amount float64) int64 {
	amountInCents := int64(amount * 100)
	return (amountInCents * s.config.Stripe.PlatformFee) / 10000
}

// FormatAmountForDisplay formats amount in cents to display format
func (s *StripeService) FormatAmountForDisplay(amountInCents int64) string {
	amount := float64(amountInCents) / 100
	return fmt.Sprintf("%.2f", amount)
}

// GetPublishableKey returns the Stripe publishable key for frontend
func (s *StripeService) GetPublishableKey() string {
	return s.config.Stripe.PublishableKey
}
