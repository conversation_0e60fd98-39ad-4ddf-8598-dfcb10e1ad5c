package services

import (
	"context"
	"customer-backend/internal/models"
	"customer-backend/internal/repositories"
	"customer-backend/pkg/logger"
	"fmt"

	"github.com/google/uuid"
)

type TableService struct {
	tableRepo *repositories.TableRepository
	shopRepo  *repositories.ShopRepository
	logger    *logger.Logger
}

func NewTableService(
	tableRepo *repositories.TableRepository,
	shopRepo *repositories.ShopRepository,
	logger *logger.Logger,
) *TableService {
	return &TableService{
		tableRepo: tableRepo,
		shopRepo:  shopRepo,
		logger:    logger,
	}
}

// GetTableByID retrieves a table by its ID with validation
func (s *TableService) GetTableByID(ctx context.Context, tableID uuid.UUID) (*models.Table, error) {
	table, err := s.tableRepo.GetTableByID(ctx, tableID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get table by ID")
		return nil, fmt.Errorf("failed to get table: %w", err)
	}
	
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	
	return table, nil
}

// GetTablesByBranch retrieves all tables for a branch
func (s *TableService) GetTablesByBranch(ctx context.Context, shopSlug, branchSlug string) ([]models.Table, error) {
	// Get branch by slugs
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("failed to get branch: %w", err)
	}
	
	if branch == nil {
		return nil, fmt.Errorf("branch not found")
	}
	
	tables, err := s.tableRepo.GetTablesByBranch(ctx, branch.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get tables by branch")
		return nil, fmt.Errorf("failed to get tables: %w", err)
	}
	
	return tables, nil
}

// GetAvailableTablesByBranch retrieves available tables for a branch
func (s *TableService) GetAvailableTablesByBranch(ctx context.Context, shopSlug, branchSlug string) ([]models.Table, error) {
	// Get branch by slugs
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("failed to get branch: %w", err)
	}
	
	if branch == nil {
		return nil, fmt.Errorf("branch not found")
	}
	
	tables, err := s.tableRepo.GetAvailableTablesByBranch(ctx, branch.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get available tables by branch")
		return nil, fmt.Errorf("failed to get available tables: %w", err)
	}
	
	return tables, nil
}

// ValidateTableForOrder validates that a table exists and is available for ordering
func (s *TableService) ValidateTableForOrder(ctx context.Context, tableID uuid.UUID, shopSlug, branchSlug string) (*models.Table, error) {
	// Get branch by slugs
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("failed to get branch: %w", err)
	}
	
	if branch == nil {
		return nil, fmt.Errorf("branch not found")
	}
	
	// Validate table
	table, err := s.tableRepo.ValidateTableForOrder(ctx, tableID, branch.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to validate table for order")
		return nil, fmt.Errorf("failed to validate table: %w", err)
	}
	
	if table == nil {
		return nil, fmt.Errorf("table not found or not available for ordering")
	}
	
	return table, nil
}

// GetTableByNumber retrieves a table by its number within a branch
func (s *TableService) GetTableByNumber(ctx context.Context, shopSlug, branchSlug string, tableNumber int) (*models.Table, error) {
	// Get branch by slugs
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("failed to get branch: %w", err)
	}
	
	if branch == nil {
		return nil, fmt.Errorf("branch not found")
	}
	
	table, err := s.tableRepo.GetTableByNumber(ctx, branch.ID, tableNumber)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get table by number")
		return nil, fmt.Errorf("failed to get table: %w", err)
	}
	
	if table == nil {
		return nil, fmt.Errorf("table not found")
	}
	
	return table, nil
}

// GetTableAreas retrieves all areas for a branch
func (s *TableService) GetTableAreas(ctx context.Context, shopSlug, branchSlug string) ([]models.TableArea, error) {
	// Get branch by slugs
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("failed to get branch: %w", err)
	}
	
	if branch == nil {
		return nil, fmt.Errorf("branch not found")
	}
	
	areas, err := s.tableRepo.GetTableAreas(ctx, branch.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get table areas")
		return nil, fmt.Errorf("failed to get table areas: %w", err)
	}
	
	return areas, nil
}

// GetTableWithQRCode retrieves a table by its QR code URL
func (s *TableService) GetTableWithQRCode(ctx context.Context, qrCodeURL string) (*models.Table, error) {
	table, err := s.tableRepo.GetTableWithQRCode(ctx, qrCodeURL)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get table with QR code")
		return nil, fmt.Errorf("failed to get table with QR code: %w", err)
	}
	
	if table == nil {
		return nil, fmt.Errorf("table not found for QR code")
	}
	
	return table, nil
}
