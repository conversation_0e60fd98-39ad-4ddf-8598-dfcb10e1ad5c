package types

// Cart-related request and response types

// CartItemRequest represents a cart item in requests
type CartItemRequest struct {
	ID           string        `json:"id" binding:"required"`
	Name         string        `json:"name" binding:"required"`
	Description  string        `json:"description"`
	Image        string        `json:"image"`
	Price        float64       `json:"price" binding:"required,min=0"`
	ShopSlug     string        `json:"shopSlug" binding:"required"`
	BranchSlug   string        `json:"branchSlug" binding:"required"`
	TableContext *TableContext `json:"tableContext,omitempty"`
}

// TableContext represents table information for QR ordering
type TableContext struct {
	TableID   string `json:"tableId"`
	ShopID    string `json:"shopId"`
	BranchID  string `json:"branchId"`
	TableName string `json:"tableName"`
}

// AddToCartRequest represents the request to add an item to cart
type AddToCartRequest struct {
	Item     CartItemRequest `json:"item" binding:"required"`
	Quantity int             `json:"quantity" binding:"required,min=1"`
}

// UpdateQuantityRequest represents the request to update item quantity
type UpdateQuantityRequest struct {
	ItemID   string `json:"item_id" binding:"required"`
	Quantity int    `json:"quantity" binding:"required,min=0"`
}

// RemoveFromCartRequest represents the request to remove an item from cart
type RemoveFromCartRequest struct {
	ItemID string `json:"item_id" binding:"required"`
}

// ClearBranchCartRequest represents the request to clear cart for a specific branch
type ClearBranchCartRequest struct {
	ShopSlug   string `json:"shop_slug" binding:"required"`
	BranchSlug string `json:"branch_slug" binding:"required"`
}

// CartItemResponse represents a cart item in responses
type CartItemResponse struct {
	ID           string        `json:"id"`
	Name         string        `json:"name"`
	Description  string        `json:"description"`
	Image        string        `json:"image"`
	Price        float64       `json:"price"`
	Quantity     int           `json:"quantity"`
	ShopSlug     string        `json:"shopSlug"`
	BranchSlug   string        `json:"branchSlug"`
	TableContext *TableContext `json:"tableContext,omitempty"`
}

// CartSessionData represents cart session data in responses
type CartSessionData struct {
	ID        string             `json:"id"`
	UserID    *string            `json:"user_id,omitempty"`
	SessionID string             `json:"session_id"`
	Items     []CartItemResponse `json:"items"`
	CreatedAt string             `json:"created_at"`
	UpdatedAt string             `json:"updated_at"`
	ExpiresAt string             `json:"expires_at"`
}

// CartResponse represents the standard cart API response
type CartResponse struct {
	Success bool             `json:"success"`
	Data    *CartSessionData `json:"data,omitempty"`
	Message string           `json:"message"`
}
