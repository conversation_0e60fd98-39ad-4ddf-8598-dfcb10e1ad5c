package types

import (
	"time"

	"github.com/google/uuid"
)

// CustomerPagination represents pagination parameters optimized for customer experience
type CustomerPagination struct {
	Page      int    `form:"page" json:"page"`
	Limit     int    `form:"limit" json:"limit"`
	UseCursor bool   `form:"use_cursor" json:"use_cursor"`
	Cursor    string `form:"cursor" json:"cursor,omitempty"`
}

// CustomerSorting represents sorting parameters for customer queries
type CustomerSorting struct {
	SortBy    string `form:"sort_by" json:"sort_by"`
	SortOrder string `form:"sort_order" json:"sort_order"`
}

// CustomerSearch represents search parameters
type CustomerSearch struct {
	Search   string   `form:"search" json:"search"`
	Category string   `form:"category" json:"category"`
	Tags     []string `form:"tags" json:"tags"`
}

// CustomerResponse represents the standard response structure for customer API
type CustomerResponse struct {
	Success    bool            `json:"success"`
	Data       interface{}     `json:"data,omitempty"`
	Message    string          `json:"message,omitempty"`
	Pagination *PaginationInfo `json:"pagination,omitempty"`
	Meta       *ResponseMeta   `json:"meta,omitempty"`
}

// PaginationInfo contains pagination information optimized for customer UX
type PaginationInfo struct {
	CurrentPage  int    `json:"current_page"`
	TotalPages   int    `json:"total_pages"`
	TotalItems   int64  `json:"total_items"`
	ItemsPerPage int    `json:"items_per_page"`
	HasNext      bool   `json:"has_next"`
	HasPrev      bool   `json:"has_prev"`
	NextCursor   string `json:"next_cursor,omitempty"`
	PrevCursor   string `json:"prev_cursor,omitempty"`

	// Center pagination support
	CenterPages []int `json:"center_pages,omitempty"`
	ShowFirst   bool  `json:"show_first"`
	ShowLast    bool  `json:"show_last"`
}

// ResponseMeta contains additional metadata for responses
type ResponseMeta struct {
	RequestID   string    `json:"request_id,omitempty"`
	Timestamp   time.Time `json:"timestamp"`
	Version     string    `json:"version,omitempty"`
	ProcessTime string    `json:"process_time,omitempty"`
}

// CustomerFilters represents common filtering options for customers
type CustomerFilters struct {
	CustomerPagination
	CustomerSorting
	CustomerSearch

	// Common filters
	ShopID   *uuid.UUID `form:"shop_id" json:"shop_id"`
	BranchID *uuid.UUID `form:"branch_id" json:"branch_id"`
	IsActive *bool      `form:"is_active" json:"is_active"`
	DateFrom *time.Time `form:"date_from" time_format:"2006-01-02" json:"date_from"`
	DateTo   *time.Time `form:"date_to" time_format:"2006-01-02" json:"date_to"`
	PriceMin *float64   `form:"price_min" json:"price_min"`
	PriceMax *float64   `form:"price_max" json:"price_max"`
}

// CustomerDefaults contains default values for customer API
var CustomerDefaults = struct {
	Page           int
	Limit          int
	SortBy         string
	SortOrder      string
	CenterPageSize int
}{
	Page:           1,
	Limit:          20,
	SortBy:         "created_at",
	SortOrder:      "desc",
	CenterPageSize: 5,
}

// ApplyDefaults applies default values to customer pagination and sorting
func (cp *CustomerPagination) ApplyDefaults() {
	if cp.Page <= 0 {
		cp.Page = CustomerDefaults.Page
	}
	if cp.Limit <= 0 {
		cp.Limit = CustomerDefaults.Limit
	}
	// Enforce limits
	if cp.Limit > 100 {
		cp.Limit = 100
	}
}

// ApplyDefaults applies default values to customer sorting
func (cs *CustomerSorting) ApplyDefaults() {
	if cs.SortBy == "" {
		cs.SortBy = CustomerDefaults.SortBy
	}
	if cs.SortOrder == "" {
		cs.SortOrder = CustomerDefaults.SortOrder
	}
	// Validate sort order
	if cs.SortOrder != "asc" && cs.SortOrder != "desc" {
		cs.SortOrder = CustomerDefaults.SortOrder
	}
}

// CalculateCenterPages calculates center pagination pages for better UX
func CalculateCenterPages(currentPage, totalPages, centerSize int) []int {
	if totalPages <= centerSize {
		pages := make([]int, totalPages)
		for i := 0; i < totalPages; i++ {
			pages[i] = i + 1
		}
		return pages
	}

	start := currentPage - centerSize/2
	if start < 1 {
		start = 1
	}

	end := start + centerSize - 1
	if end > totalPages {
		end = totalPages
		start = end - centerSize + 1
		if start < 1 {
			start = 1
		}
	}

	pages := make([]int, end-start+1)
	for i := 0; i < len(pages); i++ {
		pages[i] = start + i
	}

	return pages
}

// CreateCustomerResponse creates a standardized customer response
func CreateCustomerResponse(data interface{}, message string, pagination *PaginationInfo) CustomerResponse {
	return CustomerResponse{
		Success:    true,
		Data:       data,
		Message:    message,
		Pagination: pagination,
		Meta: &ResponseMeta{
			Timestamp: time.Now(),
			Version:   "1.0",
		},
	}
}

// CreateErrorResponse creates a standardized error response
func CreateErrorResponse(message string) CustomerResponse {
	return CustomerResponse{
		Success: false,
		Message: message,
		Meta: &ResponseMeta{
			Timestamp: time.Now(),
			Version:   "1.0",
		},
	}
}
