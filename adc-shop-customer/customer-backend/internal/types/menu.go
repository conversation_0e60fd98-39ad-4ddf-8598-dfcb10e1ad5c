package types

import (
	"time"

	"github.com/google/uuid"
)

// CustomerMenuItem represents a menu item optimized for customer view
type CustomerMenuItem struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Price       float64 `json:"price"`
	Image       string  `json:"image"`
	Category    string  `json:"category"`

	// Customer-relevant info
	IsAvailable  bool `json:"is_available"`
	IsPopular    bool `json:"is_popular"`
	IsNew        bool `json:"is_new"`
	IsVegetarian bool `json:"is_vegetarian"`
	IsVegan      bool `json:"is_vegan"`
	IsGlutenFree bool `json:"is_gluten_free"`
	IsSpicy      bool `json:"is_spicy"`
	SpicyLevel   int  `json:"spicy_level"` // 1-5

	// Nutritional info
	Calories        *int `json:"calories,omitempty"`
	PreparationTime *int `json:"preparation_time,omitempty"` // in minutes

	// Tags and allergens
	Tags      []string `json:"tags"`
	Allergens []string `json:"allergens"`

	// Customization options
	HasCustomizations bool                    `json:"has_customizations"`
	Customizations    []MenuItemCustomization `json:"customizations,omitempty"`

	// Ratings and reviews
	Rating      float64 `json:"rating"`
	ReviewCount int     `json:"review_count"`

	// Metadata
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// MenuItemCustomization represents customization options for menu items
type MenuItemCustomization struct {
	ID            uuid.UUID                     `json:"id"`
	Name          string                        `json:"name"`
	Type          string                        `json:"type"` // single, multiple, text
	Required      bool                          `json:"required"`
	MaxSelections *int                          `json:"max_selections,omitempty"`
	Options       []MenuItemCustomizationOption `json:"options,omitempty"`
}

// MenuItemCustomizationOption represents individual customization options
type MenuItemCustomizationOption struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	PriceChange float64   `json:"price_change"`
	IsAvailable bool      `json:"is_available"`
}

// MenuCategory represents a menu category
type MenuCategory struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Image       string    `json:"image,omitempty"`
	SortOrder   int       `json:"sort_order"`
	IsActive    bool      `json:"is_active"`
	ItemCount   int       `json:"item_count"`
}

// MenuFilters represents filters for menu queries
type MenuFilters struct {
	CustomerFilters

	// Menu-specific filters
	CategoryID    *uuid.UUID `form:"category_id" json:"category_id"`
	Categories    []string   `form:"categories" json:"categories"`
	IsAvailable   *bool      `form:"is_available" json:"is_available"`
	IsPopular     *bool      `form:"is_popular" json:"is_popular"`
	IsNew         *bool      `form:"is_new" json:"is_new"`
	IsVegetarian  *bool      `form:"is_vegetarian" json:"is_vegetarian"`
	IsVegan       *bool      `form:"is_vegan" json:"is_vegan"`
	IsGlutenFree  *bool      `form:"is_gluten_free" json:"is_gluten_free"`
	IsSpicy       *bool      `form:"is_spicy" json:"is_spicy"`
	MaxSpicyLevel *int       `form:"max_spicy_level" json:"max_spicy_level"`
	MaxCalories   *int       `form:"max_calories" json:"max_calories"`
	MaxPrepTime   *int       `form:"max_prep_time" json:"max_prep_time"`
	MinRating     *float64   `form:"min_rating" json:"min_rating"`
	Allergens     []string   `form:"allergens" json:"allergens"` // exclude items with these allergens
}

// MenuListResponse represents the response for menu list queries
type MenuListResponse struct {
	CustomerResponse
	Data struct {
		Items      []CustomerMenuItem `json:"items"`
		Categories []MenuCategory     `json:"categories"`
		Filters    MenuFilterOptions  `json:"filters"`
	} `json:"data"`
}

// MenuItemResponse represents the response for menu item detail queries
type MenuItemResponse struct {
	CustomerResponse
	Data struct {
		Item CustomerMenuItem `json:"item"`
	} `json:"data"`
}

// MenuFilterOptions represents available filter options
type MenuFilterOptions struct {
	Categories     []MenuCategory `json:"categories"`
	PriceRanges    []PriceRange   `json:"price_ranges"`
	DietaryOptions []string       `json:"dietary_options"`
	SpicyLevels    []int          `json:"spicy_levels"`
	Allergens      []string       `json:"allergens"`
}

// PriceRange represents a price range option
type PriceRange struct {
	Label string  `json:"label"`
	Min   float64 `json:"min"`
	Max   float64 `json:"max"`
}
