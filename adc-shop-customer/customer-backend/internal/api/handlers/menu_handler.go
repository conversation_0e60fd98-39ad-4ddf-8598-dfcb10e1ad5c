package handlers

import (
	"customer-backend/internal/services"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// MenuHandler handles menu-related HTTP requests
type Menu<PERSON><PERSON><PERSON> struct {
	menuService *services.MenuService
	logger      *logger.Logger
}

// NewMenuHandler creates a new menu handler
func NewMenuHandler(menuService *services.MenuService, logger *logger.Logger) *MenuHandler {
	return &MenuHandler{
		menuService: menuService,
		logger:      logger,
	}
}

// GetMenuItems godoc
// @Summary Get menu items
// @Description Get a paginated list of menu items for a shop
// @Tags menu
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param search query string false "Search query"
// @Param category_id query string false "Category ID"
// @Param categories query []string false "Category names"
// @Param is_available query bool false "Filter by availability"
// @Param is_popular query bool false "Filter by popularity"
// @Param is_new query bool false "Filter by new items"
// @Param is_vegetarian query bool false "Filter vegetarian items"
// @Param is_vegan query bool false "Filter vegan items"
// @Param is_gluten_free query bool false "Filter gluten-free items"
// @Param price_min query number false "Minimum price"
// @Param price_max query number false "Maximum price"
// @Param min_rating query number false "Minimum rating"
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 404 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/{shopId}/menu [get]
func (h *MenuHandler) GetMenuItems(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid shop ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid shop ID"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetMenuItems(c.Request.Context(), shopID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get menu items")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get menu items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetMenuItem godoc
// @Summary Get menu item details
// @Description Get detailed information about a specific menu item
// @Tags menu
// @Accept json
// @Produce json
// @Param itemId path string true "Menu Item ID"
// @Success 200 {object} types.MenuItemResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 404 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /menu/items/{itemId} [get]
func (h *MenuHandler) GetMenuItem(c *gin.Context) {
	itemID, err := uuid.Parse(c.Param("itemId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid item ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid item ID"))
		return
	}

	response, err := h.menuService.GetMenuItem(c.Request.Context(), itemID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get menu item")
		c.JSON(http.StatusNotFound, types.CreateErrorResponse("Menu item not found"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetPopularItems godoc
// @Summary Get popular menu items
// @Description Get popular menu items for a shop
// @Tags menu
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/{shopId}/menu/popular [get]
func (h *MenuHandler) GetPopularItems(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid shop ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid shop ID"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetPopularItems(c.Request.Context(), shopID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get popular items")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get popular items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetNewItems godoc
// @Summary Get new menu items
// @Description Get new menu items for a shop
// @Tags menu
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/{shopId}/menu/new [get]
func (h *MenuHandler) GetNewItems(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid shop ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid shop ID"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetNewItems(c.Request.Context(), shopID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get new items")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get new items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetItemsByCategory godoc
// @Summary Get menu items by category
// @Description Get menu items filtered by category
// @Tags menu
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param categoryId path string true "Category ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/{shopId}/menu/categories/{categoryId} [get]
func (h *MenuHandler) GetItemsByCategory(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid shop ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid shop ID"))
		return
	}

	categoryID, err := uuid.Parse(c.Param("categoryId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid category ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid category ID"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetItemsByCategory(c.Request.Context(), shopID, categoryID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get items by category")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get items by category"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// SearchMenuItems godoc
// @Summary Search menu items
// @Description Search for menu items based on query string
// @Tags menu
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param q query string true "Search query"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/{shopId}/menu/search [get]
func (h *MenuHandler) SearchMenuItems(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid shop ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid shop ID"))
		return
	}

	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Search query is required"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.SearchMenuItems(c.Request.Context(), shopID, query, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search menu items")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to search menu items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetVegetarianItems godoc
// @Summary Get vegetarian menu items
// @Description Get vegetarian menu items for a shop
// @Tags menu
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/{shopId}/menu/vegetarian [get]
func (h *MenuHandler) GetVegetarianItems(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid shop ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid shop ID"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetVegetarianItems(c.Request.Context(), shopID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get vegetarian items")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get vegetarian items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetMenuCategories godoc
// @Summary Get menu categories
// @Description Get menu categories for a shop
// @Tags menu
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Success 200 {object} types.CustomerResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/{shopId}/menu/categories [get]
func (h *MenuHandler) GetMenuCategories(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid shop ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid shop ID"))
		return
	}

	categories, err := h.menuService.GetMenuCategories(c.Request.Context(), shopID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get menu categories")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get menu categories"))
		return
	}

	response := types.CreateCustomerResponse(
		struct {
			Categories []types.MenuCategory `json:"categories"`
		}{Categories: categories},
		"Menu categories retrieved successfully",
		nil,
	)

	c.JSON(http.StatusOK, response)
}

// GetMenuItemsBySlug godoc
// @Summary Get menu items by shop slug
// @Description Get a paginated list of menu items for a shop using slug
// @Tags menu
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param search query string false "Search query"
// @Param category_id query string false "Category ID"
// @Param categories query []string false "Category names"
// @Param is_available query bool false "Filter by availability"
// @Param is_popular query bool false "Filter by popularity"
// @Param is_new query bool false "Filter by new items"
// @Param is_vegetarian query bool false "Filter vegetarian items"
// @Param is_vegan query bool false "Filter vegan items"
// @Param is_gluten_free query bool false "Filter gluten-free items"
// @Param price_min query number false "Minimum price"
// @Param price_max query number false "Maximum price"
// @Param min_rating query number false "Minimum rating"
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 404 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{slug}/menu [get]
func (h *MenuHandler) GetMenuItemsBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		h.logger.Error("Shop slug is required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug is required"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetMenuItemsBySlug(c.Request.Context(), slug, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get menu items by slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get menu items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetPopularItemsBySlug godoc
// @Summary Get popular menu items by shop slug
// @Description Get popular menu items for a shop using slug
// @Tags menu
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{slug}/menu/popular [get]
func (h *MenuHandler) GetPopularItemsBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		h.logger.Error("Shop slug is required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug is required"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetPopularItemsBySlug(c.Request.Context(), slug, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get popular items by slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get popular items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetNewItemsBySlug godoc
// @Summary Get new menu items by shop slug
// @Description Get new menu items for a shop using slug
// @Tags menu
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{slug}/menu/new [get]
func (h *MenuHandler) GetNewItemsBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		h.logger.Error("Shop slug is required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug is required"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetNewItemsBySlug(c.Request.Context(), slug, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get new items by slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get new items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetVegetarianItemsBySlug godoc
// @Summary Get vegetarian menu items by shop slug
// @Description Get vegetarian menu items for a shop using slug
// @Tags menu
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{slug}/menu/vegetarian [get]
func (h *MenuHandler) GetVegetarianItemsBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		h.logger.Error("Shop slug is required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug is required"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetVegetarianItemsBySlug(c.Request.Context(), slug, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get vegetarian items by slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get vegetarian items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// SearchMenuItemsBySlug godoc
// @Summary Search menu items by shop slug
// @Description Search for menu items based on query string using shop slug
// @Tags menu
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Param q query string true "Search query"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{slug}/menu/search [get]
func (h *MenuHandler) SearchMenuItemsBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		h.logger.Error("Shop slug is required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug is required"))
		return
	}

	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Search query is required"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.SearchMenuItemsBySlug(c.Request.Context(), slug, query, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search menu items by slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to search menu items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetMenuCategoriesBySlug godoc
// @Summary Get menu categories by shop slug
// @Description Get menu categories for a shop using slug
// @Tags menu
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Success 200 {object} types.CustomerResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{slug}/menu/categories [get]
func (h *MenuHandler) GetMenuCategoriesBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		h.logger.Error("Shop slug is required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug is required"))
		return
	}

	categories, err := h.menuService.GetMenuCategoriesBySlug(c.Request.Context(), slug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get menu categories by slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get menu categories"))
		return
	}

	response := types.CreateCustomerResponse(
		struct {
			Categories []types.MenuCategory `json:"categories"`
		}{Categories: categories},
		"Menu categories retrieved successfully",
		nil,
	)

	c.JSON(http.StatusOK, response)
}

// GetItemsByCategoryBySlug godoc
// @Summary Get menu items by category using shop slug
// @Description Get menu items filtered by category using shop slug
// @Tags menu
// @Accept json
// @Produce json
// @Param slug path string true "Shop Slug"
// @Param categoryId path string true "Category ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{slug}/menu/categories/{categoryId} [get]
func (h *MenuHandler) GetItemsByCategoryBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		h.logger.Error("Shop slug is required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug is required"))
		return
	}

	categoryID, err := uuid.Parse(c.Param("categoryId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid category ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid category ID"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetItemsByCategoryBySlug(c.Request.Context(), slug, categoryID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get items by category by slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get items by category"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetMenuItemsByBranchSlug godoc
// @Summary Get menu items by shop and branch slug
// @Description Get menu items for a specific shop branch using shop slug and branch slug
// @Tags menu
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param search query string false "Search query"
// @Param categories query []string false "Category names"
// @Param is_available query bool false "Filter available items"
// @Param is_popular query bool false "Filter popular items"
// @Param is_new query bool false "Filter new items"
// @Param is_vegetarian query bool false "Filter vegetarian items"
// @Param is_vegan query bool false "Filter vegan items"
// @Param is_gluten_free query bool false "Filter gluten-free items"
// @Param price_min query number false "Minimum price"
// @Param price_max query number false "Maximum price"
// @Param min_rating query number false "Minimum rating"
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 404 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{shopSlug}/branches/slug/{branchSlug}/menu [get]
func (h *MenuHandler) GetMenuItemsByBranchSlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" {
		h.logger.Error("Shop slug is required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug is required"))
		return
	}

	if branchSlug == "" {
		h.logger.Error("Branch slug is required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Branch slug is required"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetMenuItemsByBranchSlug(c.Request.Context(), shopSlug, branchSlug, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get menu items by branch slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get menu items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetPopularItemsByBranchSlug godoc
// @Summary Get popular menu items by shop and branch slug
// @Description Get popular menu items for a specific shop branch using shop slug and branch slug
// @Tags menu
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{shopSlug}/branches/slug/{branchSlug}/menu/popular [get]
func (h *MenuHandler) GetPopularItemsByBranchSlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		h.logger.Error("Shop slug and branch slug are required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug and branch slug are required"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetPopularItemsByBranchSlug(c.Request.Context(), shopSlug, branchSlug, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get popular items by branch slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get popular items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetNewItemsByBranchSlug godoc
// @Summary Get new menu items by shop and branch slug
// @Description Get new menu items for a specific shop branch using shop slug and branch slug
// @Tags menu
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{shopSlug}/branches/slug/{branchSlug}/menu/new [get]
func (h *MenuHandler) GetNewItemsByBranchSlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		h.logger.Error("Shop slug and branch slug are required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug and branch slug are required"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetNewItemsByBranchSlug(c.Request.Context(), shopSlug, branchSlug, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get new items by branch slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get new items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetVegetarianItemsByBranchSlug godoc
// @Summary Get vegetarian menu items by shop and branch slug
// @Description Get vegetarian menu items for a specific shop branch using shop slug and branch slug
// @Tags menu
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{shopSlug}/branches/slug/{branchSlug}/menu/vegetarian [get]
func (h *MenuHandler) GetVegetarianItemsByBranchSlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		h.logger.Error("Shop slug and branch slug are required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug and branch slug are required"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetVegetarianItemsByBranchSlug(c.Request.Context(), shopSlug, branchSlug, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get vegetarian items by branch slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get vegetarian items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// SearchMenuItemsByBranchSlug godoc
// @Summary Search menu items by shop and branch slug
// @Description Search menu items for a specific shop branch using shop slug and branch slug
// @Tags menu
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Param q query string true "Search query"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{shopSlug}/branches/slug/{branchSlug}/menu/search [get]
func (h *MenuHandler) SearchMenuItemsByBranchSlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		h.logger.Error("Shop slug and branch slug are required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug and branch slug are required"))
		return
	}

	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Search query is required"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.SearchMenuItemsByBranchSlug(c.Request.Context(), shopSlug, branchSlug, query, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search menu items by branch slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to search menu items"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetMenuCategoriesByBranchSlug godoc
// @Summary Get menu categories by shop and branch slug
// @Description Get menu categories for a specific shop branch using shop slug and branch slug
// @Tags menu
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Success 200 {object} types.CustomerResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{shopSlug}/branches/slug/{branchSlug}/menu/categories [get]
func (h *MenuHandler) GetMenuCategoriesByBranchSlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		h.logger.Error("Shop slug and branch slug are required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug and branch slug are required"))
		return
	}

	categories, err := h.menuService.GetMenuCategoriesByBranchSlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get menu categories by branch slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get menu categories"))
		return
	}

	response := types.CreateCustomerResponse(
		struct {
			Categories []types.MenuCategory `json:"categories"`
		}{Categories: categories},
		"Menu categories retrieved successfully",
		nil,
	)

	c.JSON(http.StatusOK, response)
}

// GetItemsByCategoryByBranchSlug godoc
// @Summary Get menu items by category using shop and branch slug
// @Description Get menu items by category for a specific shop branch using shop slug and branch slug
// @Tags menu
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Param categoryId path string true "Category ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.MenuListResponse
// @Failure 400 {object} types.CustomerResponse
// @Failure 500 {object} types.CustomerResponse
// @Router /shops/slug/{shopSlug}/branches/slug/{branchSlug}/menu/categories/{categoryId} [get]
func (h *MenuHandler) GetItemsByCategoryByBranchSlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		h.logger.Error("Shop slug and branch slug are required")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Shop slug and branch slug are required"))
		return
	}

	categoryID, err := uuid.Parse(c.Param("categoryId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid category ID")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid category ID"))
		return
	}

	var filters types.MenuFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.WithError(err).Error("Invalid query parameters")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid query parameters"))
		return
	}

	response, err := h.menuService.GetItemsByCategoryByBranchSlug(c.Request.Context(), shopSlug, branchSlug, categoryID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get items by category by branch slug")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get items by category"))
		return
	}

	c.JSON(http.StatusOK, response)
}
