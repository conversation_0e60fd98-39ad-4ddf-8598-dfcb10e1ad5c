package handlers

import (
	"customer-backend/internal/services"
	"customer-backend/pkg/logger"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type TableHandler struct {
	tableService *services.TableService
	logger       *logger.Logger
}

func NewTableHandler(tableService *services.TableService, logger *logger.Logger) *TableHandler {
	return &TableHandler{
		tableService: tableService,
		logger:       logger,
	}
}

// GetTableByID retrieves a table by its ID
// @Summary Get table by ID
// @Description Get table details by table ID
// @Tags tables
// @Accept json
// @Produce json
// @Param tableId path string true "Table ID"
// @Success 200 {object} models.Table
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /tables/{tableId} [get]
func (h *TableHandler) GetTableByID(c *gin.Context) {
	tableIDStr := c.Param("tableId")
	tableID, err := uuid.Parse(tableIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid table ID"})
		return
	}

	table, err := h.tableService.GetTableByID(c.Request.Context(), tableID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get table by ID")
		if err.Error() == "table not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Table not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get table"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    table,
	})
}

// GetTablesByBranch retrieves all tables for a branch
// @Summary Get tables by branch
// @Description Get all tables for a specific branch
// @Tags tables
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /shops/{shopSlug}/branches/{branchSlug}/tables [get]
func (h *TableHandler) GetTablesByBranch(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	tables, err := h.tableService.GetTablesByBranch(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get tables by branch")
		if err.Error() == "branch not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get tables"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    tables,
	})
}

// GetAvailableTablesByBranch retrieves available tables for a branch
// @Summary Get available tables by branch
// @Description Get all available tables for a specific branch
// @Tags tables
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /shops/{shopSlug}/branches/{branchSlug}/tables/available [get]
func (h *TableHandler) GetAvailableTablesByBranch(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	tables, err := h.tableService.GetAvailableTablesByBranch(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get available tables by branch")
		if err.Error() == "branch not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get available tables"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    tables,
	})
}

// GetTableByNumber retrieves a table by its number within a branch
// @Summary Get table by number
// @Description Get table details by table number within a branch
// @Tags tables
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Param tableNumber path int true "Table Number"
// @Success 200 {object} models.Table
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /shops/{shopSlug}/branches/{branchSlug}/tables/number/{tableNumber} [get]
func (h *TableHandler) GetTableByNumber(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	tableNumberStr := c.Param("tableNumber")

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	tableNumber, err := strconv.Atoi(tableNumberStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid table number"})
		return
	}

	table, err := h.tableService.GetTableByNumber(c.Request.Context(), shopSlug, branchSlug, tableNumber)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get table by number")
		if err.Error() == "table not found" || err.Error() == "branch not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Table not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get table"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    table,
	})
}

// ValidateTableForOrder validates that a table is available for ordering
// @Summary Validate table for order
// @Description Validate that a table exists and is available for placing an order
// @Tags tables
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Param tableId path string true "Table ID"
// @Success 200 {object} models.Table
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /shops/{shopSlug}/branches/{branchSlug}/tables/{tableId}/validate [get]
func (h *TableHandler) ValidateTableForOrder(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	tableIDStr := c.Param("tableId")

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	tableID, err := uuid.Parse(tableIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid table ID"})
		return
	}

	table, err := h.tableService.ValidateTableForOrder(c.Request.Context(), tableID, shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to validate table for order")
		if err.Error() == "table not found or not available for ordering" || err.Error() == "branch not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Table not found or not available"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to validate table"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    table,
		"message": "Table is available for ordering",
	})
}

// GetTableAreas retrieves all areas for a branch
// @Summary Get table areas
// @Description Get all table areas for a specific branch
// @Tags tables
// @Accept json
// @Produce json
// @Param shopSlug path string true "Shop Slug"
// @Param branchSlug path string true "Branch Slug"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /shops/{shopSlug}/branches/{branchSlug}/table-areas [get]
func (h *TableHandler) GetTableAreas(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	areas, err := h.tableService.GetTableAreas(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get table areas")
		if err.Error() == "branch not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get table areas"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    areas,
	})
}
