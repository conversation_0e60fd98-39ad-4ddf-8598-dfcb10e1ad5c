package handlers

import (
	"customer-backend/internal/services"
	"customer-backend/pkg/logger"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type PaymentHandler struct {
	stripeService *services.StripeService
	orderService  *services.OrderService
	logger        *logger.Logger
}

func NewPaymentHandler(
	stripeService *services.StripeService,
	orderService *services.OrderService,
	logger *logger.Logger,
) *PaymentHandler {
	return &PaymentHandler{
		stripeService: stripeService,
		orderService:  orderService,
		logger:        logger,
	}
}

// CreatePaymentIntent creates a Stripe Payment Intent for an order
// @Summary Create payment intent
// @Description Create a Stripe Payment Intent for order payment
// @Tags payments
// @Accept json
// @Produce json
// @Param request body CreatePaymentIntentRequest true "Payment intent request"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payments/create-intent [post]
func (h *PaymentHandler) CreatePaymentIntent(c *gin.Context) {
	var req CreatePaymentIntentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Get the order
	order, err := h.orderService.GetOrderByID(c.Request.Context(), req.OrderID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get order for payment")
		if err.Error() == "order not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get order"})
		return
	}

	// Create payment intent
	paymentIntent, err := h.stripeService.CreatePaymentIntent(
		c.Request.Context(),
		order,
		req.ConnectedAccountID,
	)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create payment intent")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create payment intent"})
		return
	}

	// Update order with payment intent ID
	// Note: You might want to add a method to update payment intent ID in order service

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"payment_intent_id":    paymentIntent.ID,
			"client_secret":        paymentIntent.ClientSecret,
			"amount":               paymentIntent.Amount,
			"currency":             paymentIntent.Currency,
			"status":               paymentIntent.Status,
			"publishable_key":      h.stripeService.GetPublishableKey(),
			"connected_account_id": req.ConnectedAccountID,
		},
	})
}

// ConfirmPayment confirms a payment intent
// @Summary Confirm payment
// @Description Confirm a Stripe Payment Intent
// @Tags payments
// @Accept json
// @Produce json
// @Param request body ConfirmPaymentRequest true "Payment confirmation request"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payments/confirm [post]
func (h *PaymentHandler) ConfirmPayment(c *gin.Context) {
	var req ConfirmPaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Confirm payment intent
	paymentIntent, err := h.stripeService.ConfirmPaymentIntent(c.Request.Context(), req.PaymentIntentID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to confirm payment intent")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to confirm payment"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"payment_intent_id": paymentIntent.ID,
			"status":            paymentIntent.Status,
			"amount":            paymentIntent.Amount,
		},
	})
}

// GetPaymentStatus gets the status of a payment intent
// @Summary Get payment status
// @Description Get the status of a Stripe Payment Intent
// @Tags payments
// @Accept json
// @Produce json
// @Param paymentIntentId path string true "Payment Intent ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payments/{paymentIntentId}/status [get]
func (h *PaymentHandler) GetPaymentStatus(c *gin.Context) {
	paymentIntentID := c.Param("paymentIntentId")
	if paymentIntentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Payment intent ID is required"})
		return
	}

	// Get payment intent
	paymentIntent, err := h.stripeService.GetPaymentIntent(c.Request.Context(), paymentIntentID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get payment intent")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get payment status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"payment_intent_id": paymentIntent.ID,
			"status":            paymentIntent.Status,
			"amount":            paymentIntent.Amount,
			"currency":          paymentIntent.Currency,
			"metadata":          paymentIntent.Metadata,
		},
	})
}

// CancelPayment cancels a payment intent
// @Summary Cancel payment
// @Description Cancel a Stripe Payment Intent
// @Tags payments
// @Accept json
// @Produce json
// @Param paymentIntentId path string true "Payment Intent ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payments/{paymentIntentId}/cancel [post]
func (h *PaymentHandler) CancelPayment(c *gin.Context) {
	paymentIntentID := c.Param("paymentIntentId")
	if paymentIntentID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Payment intent ID is required"})
		return
	}

	// Cancel payment intent
	paymentIntent, err := h.stripeService.CancelPaymentIntent(c.Request.Context(), paymentIntentID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to cancel payment intent")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel payment"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"payment_intent_id": paymentIntent.ID,
			"status":            paymentIntent.Status,
		},
		"message": "Payment cancelled successfully",
	})
}

// HandleWebhook handles Stripe webhooks
// @Summary Handle Stripe webhook
// @Description Handle Stripe webhook events
// @Tags payments
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /payments/webhook [post]
func (h *PaymentHandler) HandleWebhook(c *gin.Context) {
	// Read the request body
	payload, err := io.ReadAll(c.Request.Body)
	if err != nil {
		h.logger.WithError(err).Error("Failed to read webhook payload")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body"})
		return
	}

	// Get the Stripe signature header
	signature := c.GetHeader("Stripe-Signature")
	if signature == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing Stripe signature"})
		return
	}

	// Verify webhook signature and parse event
	event, err := h.stripeService.VerifyWebhookSignature(payload, signature)
	if err != nil {
		h.logger.WithError(err).Error("Failed to verify webhook signature")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid webhook signature"})
		return
	}

	// Process the webhook event
	err = h.stripeService.ProcessWebhookEvent(c.Request.Context(), event)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process webhook event")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process webhook"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Webhook processed successfully",
	})
}

// GetPublishableKey returns the Stripe publishable key
// @Summary Get Stripe publishable key
// @Description Get the Stripe publishable key for frontend integration
// @Tags payments
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /payments/config [get]
func (h *PaymentHandler) GetPublishableKey(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"publishable_key": h.stripeService.GetPublishableKey(),
		},
	})
}

// Request/Response types
type CreatePaymentIntentRequest struct {
	OrderID            uuid.UUID `json:"order_id" binding:"required"`
	ConnectedAccountID string    `json:"connected_account_id"` // Optional for testing
}

type ConfirmPaymentRequest struct {
	PaymentIntentID string `json:"payment_intent_id" binding:"required"`
}
