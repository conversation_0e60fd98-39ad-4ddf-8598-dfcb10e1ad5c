package handlers

import (
	"customer-backend/internal/services"
	"customer-backend/pkg/logger"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type OrderHandler struct {
	orderService *services.OrderService
	logger       *logger.Logger
}

func NewOrderHandler(orderService *services.OrderService, logger *logger.Logger) *OrderHandler {
	return &OrderHandler{
		orderService: orderService,
		logger:       logger,
	}
}

// CreateTableOrder creates a new table order
// @Summary Create table order
// @Description Create a new order for a specific table
// @Tags orders
// @Accept json
// @Produce json
// @Param order body services.CreateTableOrderRequest true "Order data"
// @Success 201 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /orders/table [post]
func (h *OrderHandler) CreateTableOrder(c *gin.Context) {
	var req services.CreateTableOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	order, err := h.orderService.CreateTableOrder(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create table order")
		if err.Error() == "table not found or not available" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Table not found or not available"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create order"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    order,
		"message": "Order created successfully",
	})
}

// CreateOrderWithPayment creates an order with integrated payment processing
// @Summary Create order with payment
// @Description Create a new order with integrated Stripe Connect payment processing
// @Tags orders
// @Accept json
// @Produce json
// @Param order body services.CreateOrderWithPaymentRequest true "Order data with payment details"
// @Success 201 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /orders/create-with-payment [post]
func (h *OrderHandler) CreateOrderWithPayment(c *gin.Context) {
	var req services.CreateOrderWithPaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	response, err := h.orderService.CreateOrderWithPayment(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create order with payment")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create order"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    response,
		"message": "Order created successfully",
	})
}

// GetOrderByID retrieves an order by its ID
// @Summary Get order by ID
// @Description Get order details by order ID
// @Tags orders
// @Accept json
// @Produce json
// @Param orderId path string true "Order ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /orders/{orderId} [get]
func (h *OrderHandler) GetOrderByID(c *gin.Context) {
	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	order, err := h.orderService.GetOrderByID(c.Request.Context(), orderID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get order by ID")
		if err.Error() == "order not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get order"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    order,
	})
}

// GetOrderByNumber retrieves an order by its order number
// @Summary Get order by number
// @Description Get order details by order number
// @Tags orders
// @Accept json
// @Produce json
// @Param orderNumber path string true "Order Number"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /orders/number/{orderNumber} [get]
func (h *OrderHandler) GetOrderByNumber(c *gin.Context) {
	orderNumber := c.Param("orderNumber")
	if orderNumber == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Order number is required"})
		return
	}

	order, err := h.orderService.GetOrderByNumber(c.Request.Context(), orderNumber)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get order by number")
		if err.Error() == "order not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get order"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    order,
	})
}

// GetOrdersByTable retrieves orders for a specific table
// @Summary Get orders by table
// @Description Get all orders for a specific table
// @Tags orders
// @Accept json
// @Produce json
// @Param tableId path string true "Table ID"
// @Param limit query int false "Limit number of orders" default(10)
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /orders/table/{tableId} [get]
func (h *OrderHandler) GetOrdersByTable(c *gin.Context) {
	tableIDStr := c.Param("tableId")
	tableID, err := uuid.Parse(tableIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid table ID"})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}

	orders, err := h.orderService.GetOrdersByTable(c.Request.Context(), tableID, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get orders by table")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get orders"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    orders,
	})
}

// GetOrdersByCustomer retrieves orders for a specific customer
// @Summary Get orders by customer
// @Description Get all orders for a specific customer by phone number
// @Tags orders
// @Accept json
// @Produce json
// @Param customerPhone query string true "Customer Phone Number"
// @Param limit query int false "Limit number of orders" default(10)
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /orders/customer [get]
func (h *OrderHandler) GetOrdersByCustomer(c *gin.Context) {
	customerPhone := c.Query("customerPhone")
	if customerPhone == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Customer phone number is required"})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 10
	}

	orders, err := h.orderService.GetOrdersByCustomer(c.Request.Context(), customerPhone, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get orders by customer")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get orders"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    orders,
	})
}

// UpdateOrderStatus updates the status of an order
// @Summary Update order status
// @Description Update the status of an existing order
// @Tags orders
// @Accept json
// @Produce json
// @Param orderId path string true "Order ID"
// @Param status body map[string]string true "Status update"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /orders/{orderId}/status [put]
func (h *OrderHandler) UpdateOrderStatus(c *gin.Context) {
	orderIDStr := c.Param("orderId")
	orderID, err := uuid.Parse(orderIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid order ID"})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	err = h.orderService.UpdateOrderStatus(c.Request.Context(), orderID, req.Status)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update order status")
		if err.Error() == "order not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Order not found"})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Order status updated successfully",
	})
}

// GetActiveOrdersByTable retrieves active orders for a table
// @Summary Get active orders by table
// @Description Get all active orders for a specific table
// @Tags orders
// @Accept json
// @Produce json
// @Param tableId path string true "Table ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /orders/table/{tableId}/active [get]
func (h *OrderHandler) GetActiveOrdersByTable(c *gin.Context) {
	tableIDStr := c.Param("tableId")
	tableID, err := uuid.Parse(tableIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid table ID"})
		return
	}

	orders, err := h.orderService.GetActiveOrdersByTable(c.Request.Context(), tableID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get active orders by table")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get active orders"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    orders,
	})
}
