package handlers

import (
	"customer-backend/internal/services"
	"customer-backend/internal/types"
	"customer-backend/pkg/logger"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type CartHandler struct {
	cartService *services.CartService
	logger      *logger.Logger
}

func NewCartHandler(cartService *services.CartService, logger *logger.Logger) *CartHandler {
	return &CartHandler{
		cartService: cartService,
		logger:      logger,
	}
}

// GetCart retrieves the cart for the current user or guest session
func (h *CartHandler) GetCart(c *gin.Context) {
	userID, sessionID := h.extractUserAndSession(c)

	response, err := h.cartService.GetCart(c.Request.Context(), userID, sessionID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get cart")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to get cart"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// AddToCart adds an item to the cart
func (h *<PERSON>tHandler) AddToCart(c *gin.Context) {
	userID, sessionID := h.extractUserAndSession(c)

	var req types.AddToCartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body for add to cart")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid request body"))
		return
	}

	response, err := h.cartService.AddToCart(c.Request.Context(), userID, sessionID, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to add item to cart")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to add item to cart"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdateQuantity updates the quantity of a cart item
func (h *CartHandler) UpdateQuantity(c *gin.Context) {
	userID, sessionID := h.extractUserAndSession(c)

	var req types.UpdateQuantityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body for update quantity")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid request body"))
		return
	}

	response, err := h.cartService.UpdateQuantity(c.Request.Context(), userID, sessionID, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update item quantity")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to update item quantity"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// RemoveFromCart removes an item from the cart
func (h *CartHandler) RemoveFromCart(c *gin.Context) {
	userID, sessionID := h.extractUserAndSession(c)

	var req types.RemoveFromCartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body for remove from cart")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid request body"))
		return
	}

	response, err := h.cartService.RemoveFromCart(c.Request.Context(), userID, sessionID, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to remove item from cart")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to remove item from cart"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// ClearCart removes all items from the cart
func (h *CartHandler) ClearCart(c *gin.Context) {
	userID, sessionID := h.extractUserAndSession(c)

	response, err := h.cartService.ClearCart(c.Request.Context(), userID, sessionID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to clear cart")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to clear cart"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// ClearBranchCart removes all items for a specific shop and branch
func (h *CartHandler) ClearBranchCart(c *gin.Context) {
	userID, sessionID := h.extractUserAndSession(c)

	var req types.ClearBranchCartRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body for clear branch cart")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid request body"))
		return
	}

	response, err := h.cartService.ClearBranchCart(c.Request.Context(), userID, sessionID, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to clear branch cart")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to clear branch cart"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// SyncCartOnLogin merges guest cart with user cart when user logs in
func (h *CartHandler) SyncCartOnLogin(c *gin.Context) {
	// Get user ID from authentication context
	userIDStr, exists := c.Get("user_id")
	if !exists {
		h.logger.Error("User ID not found in context for cart sync")
		c.JSON(http.StatusUnauthorized, types.CreateErrorResponse("Authentication required"))
		return
	}

	userID, err := uuid.Parse(userIDStr.(string))
	if err != nil {
		h.logger.WithError(err).Error("Invalid user ID for cart sync")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Invalid user ID"))
		return
	}

	// Get guest session ID from header
	guestSessionID := c.GetHeader("X-Session-ID")
	if guestSessionID == "" {
		h.logger.Error("Guest session ID not provided for cart sync")
		c.JSON(http.StatusBadRequest, types.CreateErrorResponse("Guest session ID required"))
		return
	}

	response, err := h.cartService.SyncCartOnLogin(c.Request.Context(), userID, guestSessionID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to sync cart on login")
		c.JSON(http.StatusInternalServerError, types.CreateErrorResponse("Failed to sync cart"))
		return
	}

	c.JSON(http.StatusOK, response)
}

// Helper function to extract user ID and session ID from request context
func (h *CartHandler) extractUserAndSession(c *gin.Context) (*uuid.UUID, string) {
	var userID *uuid.UUID
	var sessionID string

	// Try to get user ID from authentication context (for logged-in users)
	if userIDStr, exists := c.Get("user_id"); exists {
		if parsedUserID, err := uuid.Parse(userIDStr.(string)); err == nil {
			userID = &parsedUserID
		}
	}

	// Get session ID from header (for guest users or additional tracking)
	sessionID = c.GetHeader("X-Session-ID")
	if sessionID == "" {
		// Generate a default session ID if none provided
		sessionID = "guest_" + uuid.New().String()
	}

	return userID, sessionID
}
