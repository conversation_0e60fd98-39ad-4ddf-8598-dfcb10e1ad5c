package repositories

import (
	"context"
	"customer-backend/internal/models"
	"customer-backend/pkg/logger"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type CartRepository struct {
	db     *gorm.DB
	logger *logger.Logger
}

func NewCartRepository(db *gorm.DB, logger *logger.Logger) *CartRepository {
	return &CartRepository{
		db:     db,
		logger: logger,
	}
}

// GetOrCreateCartSession gets an existing cart session or creates a new one
func (r *CartRepository) GetOrCreateCartSession(ctx context.Context, userID *uuid.UUID, sessionID string) (*models.CartSession, error) {
	var cartSession models.CartSession

	// Try to find existing session
	query := r.db.WithContext(ctx).Preload("Items")
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	} else {
		query = query.Where("session_id = ? AND user_id IS NULL", sessionID)
	}

	err := query.First(&cartSession).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create new cart session
			cartSession = models.CartSession{
				UserID:    userID,
				SessionID: sessionID,
				ExpiresAt: time.Now().Add(24 * time.Hour), // 24 hours expiry
			}

			err = r.db.WithContext(ctx).Create(&cartSession).Error
			if err != nil {
				r.logger.WithError(err).Error("Failed to create cart session")
				return nil, fmt.Errorf("failed to create cart session: %w", err)
			}

			// Load items (will be empty for new session)
			err = r.db.WithContext(ctx).Preload("Items").First(&cartSession, cartSession.ID).Error
			if err != nil {
				r.logger.WithError(err).Error("Failed to load cart session after creation")
				return nil, fmt.Errorf("failed to load cart session: %w", err)
			}
		} else {
			r.logger.WithError(err).Error("Failed to get cart session")
			return nil, fmt.Errorf("failed to get cart session: %w", err)
		}
	}

	// Check if session is expired and clean up if needed
	if cartSession.IsExpired() {
		err = r.CleanupExpiredSession(ctx, cartSession.ID)
		if err != nil {
			r.logger.WithError(err).Error("Failed to cleanup expired session")
		}

		// Create new session
		cartSession = models.CartSession{
			UserID:    userID,
			SessionID: sessionID,
			ExpiresAt: time.Now().Add(24 * time.Hour),
		}

		err = r.db.WithContext(ctx).Create(&cartSession).Error
		if err != nil {
			r.logger.WithError(err).Error("Failed to create new cart session after cleanup")
			return nil, fmt.Errorf("failed to create cart session: %w", err)
		}

		cartSession.Items = []models.CartItem{} // Empty items for new session
	}

	return &cartSession, nil
}

// AddItem adds an item to the cart or updates quantity if it already exists
func (r *CartRepository) AddItem(ctx context.Context, cartSessionID uuid.UUID, item *models.CartItem) error {
	// Check if item already exists in cart
	var existingItem models.CartItem
	err := r.db.WithContext(ctx).Where(
		"cart_session_id = ? AND menu_item_id = ? AND shop_slug = ? AND branch_slug = ?",
		cartSessionID, item.MenuItemID, item.ShopSlug, item.BranchSlug,
	).First(&existingItem).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// Create new item
			item.CartSessionID = cartSessionID
			err = r.db.WithContext(ctx).Create(item).Error
			if err != nil {
				r.logger.WithError(err).Error("Failed to create cart item")
				return fmt.Errorf("failed to create cart item: %w", err)
			}
		} else {
			r.logger.WithError(err).Error("Failed to check existing cart item")
			return fmt.Errorf("failed to check existing cart item: %w", err)
		}
	} else {
		// Update existing item quantity
		existingItem.Quantity += item.Quantity
		err = r.db.WithContext(ctx).Save(&existingItem).Error
		if err != nil {
			r.logger.WithError(err).Error("Failed to update cart item quantity")
			return fmt.Errorf("failed to update cart item: %w", err)
		}
	}

	// Update cart session timestamp
	err = r.db.WithContext(ctx).Model(&models.CartSession{}).
		Where("id = ?", cartSessionID).
		Update("updated_at", time.Now()).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to update cart session timestamp")
	}

	return nil
}

// UpdateItemQuantity updates the quantity of a cart item
func (r *CartRepository) UpdateItemQuantity(ctx context.Context, cartSessionID uuid.UUID, itemID uuid.UUID, quantity int) error {
	if quantity <= 0 {
		return r.RemoveItem(ctx, cartSessionID, itemID)
	}

	err := r.db.WithContext(ctx).Model(&models.CartItem{}).
		Where("id = ? AND cart_session_id = ?", itemID, cartSessionID).
		Update("quantity", quantity).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update cart item quantity")
		return fmt.Errorf("failed to update cart item quantity: %w", err)
	}

	// Update cart session timestamp
	err = r.db.WithContext(ctx).Model(&models.CartSession{}).
		Where("id = ?", cartSessionID).
		Update("updated_at", time.Now()).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to update cart session timestamp")
	}

	return nil
}

// RemoveItem removes an item from the cart
func (r *CartRepository) RemoveItem(ctx context.Context, cartSessionID uuid.UUID, itemID uuid.UUID) error {
	err := r.db.WithContext(ctx).Where("id = ? AND cart_session_id = ?", itemID, cartSessionID).
		Delete(&models.CartItem{}).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to remove cart item")
		return fmt.Errorf("failed to remove cart item: %w", err)
	}

	// Update cart session timestamp
	err = r.db.WithContext(ctx).Model(&models.CartSession{}).
		Where("id = ?", cartSessionID).
		Update("updated_at", time.Now()).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to update cart session timestamp")
	}

	return nil
}

// ClearCart removes all items from the cart
func (r *CartRepository) ClearCart(ctx context.Context, cartSessionID uuid.UUID) error {
	err := r.db.WithContext(ctx).Where("cart_session_id = ?", cartSessionID).
		Delete(&models.CartItem{}).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to clear cart")
		return fmt.Errorf("failed to clear cart: %w", err)
	}

	// Update cart session timestamp
	err = r.db.WithContext(ctx).Model(&models.CartSession{}).
		Where("id = ?", cartSessionID).
		Update("updated_at", time.Now()).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to update cart session timestamp")
	}

	return nil
}

// ClearBranchCart removes all items for a specific shop and branch
func (r *CartRepository) ClearBranchCart(ctx context.Context, cartSessionID uuid.UUID, shopSlug, branchSlug string) error {
	err := r.db.WithContext(ctx).Where(
		"cart_session_id = ? AND shop_slug = ? AND branch_slug = ?",
		cartSessionID, shopSlug, branchSlug,
	).Delete(&models.CartItem{}).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to clear branch cart")
		return fmt.Errorf("failed to clear branch cart: %w", err)
	}

	// Update cart session timestamp
	err = r.db.WithContext(ctx).Model(&models.CartSession{}).
		Where("id = ?", cartSessionID).
		Update("updated_at", time.Now()).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to update cart session timestamp")
	}

	return nil
}

// CleanupExpiredSession removes an expired cart session and its items
func (r *CartRepository) CleanupExpiredSession(ctx context.Context, cartSessionID uuid.UUID) error {
	// Delete all items first
	err := r.db.WithContext(ctx).Where("cart_session_id = ?", cartSessionID).
		Delete(&models.CartItem{}).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to delete cart items during cleanup")
		return fmt.Errorf("failed to delete cart items: %w", err)
	}

	// Delete the session
	err = r.db.WithContext(ctx).Delete(&models.CartSession{}, cartSessionID).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to delete cart session during cleanup")
		return fmt.Errorf("failed to delete cart session: %w", err)
	}

	return nil
}

// SyncGuestCartToUser merges guest cart items into user cart when user logs in
func (r *CartRepository) SyncGuestCartToUser(ctx context.Context, userID uuid.UUID, guestSessionID string) error {
	// Get or create user cart session
	userCart, err := r.GetOrCreateCartSession(ctx, &userID, "")
	if err != nil {
		return fmt.Errorf("failed to get user cart: %w", err)
	}

	// Get guest cart session
	var guestCart models.CartSession
	err = r.db.WithContext(ctx).Preload("Items").
		Where("session_id = ? AND user_id IS NULL", guestSessionID).
		First(&guestCart).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// No guest cart to sync
			return nil
		}
		return fmt.Errorf("failed to get guest cart: %w", err)
	}

	// Merge guest cart items into user cart
	for _, guestItem := range guestCart.Items {
		// Create a copy of the item for the user cart
		userItem := models.CartItem{
			MenuItemID:  guestItem.MenuItemID,
			Quantity:    guestItem.Quantity,
			Name:        guestItem.Name,
			Description: guestItem.Description,
			Image:       guestItem.Image,
			Price:       guestItem.Price,
			ShopSlug:    guestItem.ShopSlug,
			BranchSlug:  guestItem.BranchSlug,
			TableID:     guestItem.TableID,
			TableNumber: guestItem.TableNumber,
			ShopID:      guestItem.ShopID,
			BranchID:    guestItem.BranchID,
		}

		err = r.AddItem(ctx, userCart.ID, &userItem)
		if err != nil {
			r.logger.WithError(err).Error("Failed to add guest item to user cart")
			// Continue with other items even if one fails
		}
	}

	// Clean up guest cart
	err = r.CleanupExpiredSession(ctx, guestCart.ID)
	if err != nil {
		r.logger.WithError(err).Error("Failed to cleanup guest cart after sync")
		// Don't return error as sync was successful
	}

	return nil
}
