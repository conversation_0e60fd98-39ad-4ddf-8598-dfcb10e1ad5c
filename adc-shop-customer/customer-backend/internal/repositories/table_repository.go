package repositories

import (
	"context"
	"customer-backend/internal/models"
	"customer-backend/pkg/logger"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type TableRepository struct {
	db     *gorm.DB
	logger *logger.Logger
}

func NewTableRepository(db *gorm.DB, logger *logger.Logger) *TableRepository {
	return &TableRepository{
		db:     db,
		logger: logger,
	}
}

// GetTableByID retrieves a table by its ID
func (r *TableRepository) GetTableByID(ctx context.Context, tableID uuid.UUID) (*models.Table, error) {
	var table models.Table
	
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Branch.Shop").
		Preload("Area").
		Where("id = ? AND is_active = ?", tableID, true).
		First(&table).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.WithError(err).Error("Failed to get table by ID")
		return nil, err
	}
	
	return &table, nil
}

// GetTablesByBranch retrieves all tables for a branch
func (r *TableRepository) GetTablesByBranch(ctx context.Context, branchID uuid.UUID) ([]models.Table, error) {
	var tables []models.Table
	
	err := r.db.WithContext(ctx).
		Preload("Area").
		Where("branch_id = ? AND is_active = ?", branchID, true).
		Order("number ASC").
		Find(&tables).Error
	
	if err != nil {
		r.logger.WithError(err).Error("Failed to get tables by branch")
		return nil, err
	}
	
	return tables, nil
}

// GetAvailableTablesByBranch retrieves available tables for a branch
func (r *TableRepository) GetAvailableTablesByBranch(ctx context.Context, branchID uuid.UUID) ([]models.Table, error) {
	var tables []models.Table
	
	err := r.db.WithContext(ctx).
		Preload("Area").
		Where("branch_id = ? AND is_active = ? AND status = ?", branchID, true, "available").
		Order("number ASC").
		Find(&tables).Error
	
	if err != nil {
		r.logger.WithError(err).Error("Failed to get available tables by branch")
		return nil, err
	}
	
	return tables, nil
}

// GetTableByNumber retrieves a table by its number within a branch
func (r *TableRepository) GetTableByNumber(ctx context.Context, branchID uuid.UUID, tableNumber int) (*models.Table, error) {
	var table models.Table
	
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Branch.Shop").
		Preload("Area").
		Where("branch_id = ? AND number = ? AND is_active = ?", branchID, tableNumber, true).
		First(&table).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.WithError(err).Error("Failed to get table by number")
		return nil, err
	}
	
	return &table, nil
}

// ValidateTableForOrder checks if a table is valid for placing an order
func (r *TableRepository) ValidateTableForOrder(ctx context.Context, tableID uuid.UUID, branchID uuid.UUID) (*models.Table, error) {
	var table models.Table
	
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Branch.Shop").
		Where("id = ? AND branch_id = ? AND is_active = ?", tableID, branchID, true).
		First(&table).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.WithError(err).Error("Failed to validate table for order")
		return nil, err
	}
	
	// Check if table is available for ordering
	if !table.IsAvailable() {
		return nil, nil
	}
	
	return &table, nil
}

// GetTableAreas retrieves all areas for a branch
func (r *TableRepository) GetTableAreas(ctx context.Context, branchID uuid.UUID) ([]models.TableArea, error) {
	var areas []models.TableArea
	
	err := r.db.WithContext(ctx).
		Where("branch_id = ? AND is_active = ?", branchID, true).
		Order("sort_order ASC, name ASC").
		Find(&areas).Error
	
	if err != nil {
		r.logger.WithError(err).Error("Failed to get table areas")
		return nil, err
	}
	
	return areas, nil
}

// GetTableWithQRCode retrieves a table by its QR code URL
func (r *TableRepository) GetTableWithQRCode(ctx context.Context, qrCodeURL string) (*models.Table, error) {
	var table models.Table
	
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Branch.Shop").
		Preload("Area").
		Where("qr_code_url = ? AND is_active = ?", qrCodeURL, true).
		First(&table).Error
	
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		r.logger.WithError(err).Error("Failed to get table with QR code")
		return nil, err
	}
	
	return &table, nil
}
